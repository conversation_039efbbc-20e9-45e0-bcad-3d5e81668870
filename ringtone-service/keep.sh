#!/bin/bash

# 备份mysql数据和清理运行时产生的日志文件
# 数据和日志保留20天

backupdir=/opt/backup/dump
tcwlogdir=/opt/tcyy/tcw/application/logs
sylogdir=/var/logs/sycloud

time=`date +%Y%m%d`

/usr/bin/mysqldump -utcyy -h47.108.67.67 -ptcdbtc tcw | gzip > $backupdir/tcw_$time.sql.gz
/usr/bin/mysqldump -utcyy -h47.108.67.67 -ptcdbtc tcyy | gzip > $backupdir/tcyy_$time.sql.gz

find $backupdir -name "*.sql.gz" -type f -mtime +20 -exec rm {} \; > /dev/null 2>&1
find $tcwlogdir -name "*.log" -type f -mtime +20 -exec rm {} \; > /dev/null 2>&1
find $sylogdir -name "*.log" -type f -mtime +20 -exec rm {} \; > /dev/null 2>&1

# docker私仓搭建
# docker pull registry:2
# docker run -d -v /opt/docker/registry:/var/lib/registry -p 4000:5000 --name registry registry:2
# 关于仓库清理，理论上这些镜像仅用户加速构建，没有保存的必要，在体积比较大时，直接删掉registry容器
# 并清空/opt/docker/registry目录重新建立新的registry即可