package com.xy.scanner.controller;


import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.xy.scanner.dto.ApiGentDTO;
import com.xy.scanner.entity.HttpApi;
import com.xy.scanner.entity.HttpEntity;
import com.xy.scanner.entity.HttpEntityField;
import com.xy.scanner.vo.EntityVO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.validation.Valid;
import java.sql.SQLException;
import java.util.*;


/**
 * 生成相关代码
 *
 * <AUTHOR>
 * @since 2022/4/28.
 */
@Controller
@RequestMapping("/gent")
@RequiredArgsConstructor
public class GentController {


    /**
     * 重载项目结构
     */
    @PostMapping("/refreshAll")
    public boolean refreshAll() {


        return true;
    }

    /**
     * 生成模块对应的dts
     */
    @PostMapping("/api")
    public String gentApi(@Valid @RequestBody ApiGentDTO dto, Model model) throws SQLException {

        HttpApi query = new HttpApi();
        query.setModule(dto.getModuleName());
        if(StringUtils.hasText(dto.getUrl())){
            query.setUrl(dto.getUrl());
        }

        Db db = Db.use();
        List<HttpApi> apis = db.findAll(Entity.parseWithUnderlineCase(query), HttpApi.class);

        model.addAttribute("apis", apis);
        model.addAttribute("moduleName", StringUtils.hasText(dto.getRename()) ? dto.getRename() : dto.getModuleName());

        return "ts/api";
    }


    /**
     * 生成模块对应的dts
     */
    @PostMapping("/dts")
    public String gentDts(@Valid @RequestBody ApiGentDTO dto, Model model) throws SQLException {

        HttpEntity query = new HttpEntity();
        query.setModule(dto.getModuleName());


        Db db = Db.use();
        List<HttpEntity> entities = db.findAll(Entity.parseWithUnderlineCase(query), HttpEntity.class);

        Set<String> ids = new HashSet<>();

        HashMap<String, EntityVO> vos = new HashMap<>();
        entities.forEach(e -> {
            ids.add(e.getId());
            EntityVO vo = new EntityVO();
            BeanUtils.copyProperties(e, vo);
            vo.setFields(new ArrayList<>());
            vos.put(e.getId(), vo);
        });
        List<HttpEntityField> fields = db.findAll(Entity.parseWithUnderlineCase(new HttpEntityField()).set("entity_id", ids), HttpEntityField.class);

        fields.forEach(f->{
            EntityVO entityVO = vos.get(f.getEntityId());
            entityVO.getFields().add(f);
        });

        model.addAttribute("entities", vos.values());

        return "ts/dts";
    }


}
