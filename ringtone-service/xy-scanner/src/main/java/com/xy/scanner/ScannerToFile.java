package com.xy.scanner;

import cn.hutool.core.text.StrFormatter;
import com.xy.scanner.entity.HttpApi;
import com.xy.scanner.service.ScannerService;
import com.xy.scanner.vo.EntityVO;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.SneakyThrows;
import org.springframework.beans.BeanUtils;

import java.io.File;
import java.io.FileWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 直接生成代码到文件
 *
 * <AUTHOR>
 * @since 2023/7/5 9:23
 */
public class ScannerToFile {

    private final static String TEMPLATE_DIR = System.getProperty("user.dir") + "/xy-scanner/src/main/resources/templates/ts";

    /**
     * 要生成的文件目录
     */
//    private final static String TARGET_DIR = "D:\\Workspace\\david\\ringtone-h5\\src\\api";
    private final static String TARGET_DIR = "D:\\Workspace\\david\\ringpromote\\ringtone-h5\\src\\api";


    public static void main(String[] args) {

        System.out.println("TEMPLATE_DIR:" + TEMPLATE_DIR);

        String baseDir = "D:\\Workspace\\david\\ringpromote\\ringtone-service";
        String module = "uaa";

        ScannerService scanner = new ScannerService(baseDir);
        scanner.scan("/xy-uaa/src", module);

        processApiTemplate(scanner);
        processDtsTemplate(scanner);
    }


    @SneakyThrows
    public static void processApiTemplate(ScannerService scanner) {
        Map<String, Object> data = new HashMap<>();
        data.put("moduleName", scanner.getModule());
        data.put("apis", scanner.getApis());

        String fileName = StrFormatter.format("{}/modules/{}.ts", TARGET_DIR, scanner.getModule());

        processTemplate("api.ftl", fileName, data);
    }


    @SneakyThrows
    public static void processDtsTemplate(ScannerService scanner) {
        Map<String, Object> data = new HashMap<>();

        HashMap<String, EntityVO> vos = new HashMap<>();

        scanner.getEntities().values().forEach(e -> {
            EntityVO vo = new EntityVO();
            BeanUtils.copyProperties(e, vo);
            vo.setFields(new ArrayList<>());
            vos.put(e.getId(), vo);
        });

        scanner.getEntityFields().forEach(f -> {
            EntityVO entityVO = vos.get(f.getEntityId());
            entityVO.getFields().add(f);
        });

        data.put("entities", vos.values());

        String fileName = StrFormatter.format("{}/dts/{}.d.ts", TARGET_DIR, scanner.getModule());

        processTemplate("dts.ftl", fileName, data);
    }


    @SneakyThrows
    public static void processTemplate(String ftl, String filePath, Map<String, Object> data) {
        Configuration configuration = new Configuration(Configuration.getVersion());
        Template template;

        FileWriter fileWriter = new FileWriter(filePath);

//        StringWriter stringWriter = new StringWriter();

        configuration.setDirectoryForTemplateLoading(new File(TEMPLATE_DIR));
        template = configuration.getTemplate(ftl);

        HttpApi query = new HttpApi();
        query.setModule("uaa");

        template.process(data, fileWriter);
        fileWriter.close();

    }


}
