package com.xy.scanner;

import com.xy.base.starter.config.StarterAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * <AUTHOR>
 * @since 2023/6/25 15:45
 */
@SpringBootApplication(exclude = {
        StarterAutoConfiguration.class,
        DataSourceAutoConfiguration.class,
        RedisAutoConfiguration.class,
        RedisRepositoriesAutoConfiguration.class
})
public class ScannerApplication {
    public static void main(String[] args) {
        SpringApplication.run(ScannerApplication.class, args);
    }
}