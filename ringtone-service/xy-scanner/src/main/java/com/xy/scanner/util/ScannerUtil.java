package com.xy.scanner.util;

import cn.hutool.core.util.StrUtil;
import com.thoughtworks.qdox.builder.impl.EvaluatingVisitor;
import com.thoughtworks.qdox.model.JavaAnnotation;
import com.thoughtworks.qdox.model.expression.AnnotationValue;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.LinkedList;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @since 2023/6/12 12:00
 */
public class ScannerUtil {

    /**
     * 格式化url，去除多余的/
     */
    public static String fixUrl(String url) {
        StringJoiner sj = new StringJoiner("/");
        for (String s : url.split("/")) {
            if (!StrUtil.isEmpty(s)) {
                sj.add(s);
            }
        }
        return "/" + sj;
    }

    /**
     * 获取方法的路由
     */
    public static String getMappingValue(JavaAnnotation ano) {

        String v = "";

        Object value = ano.getProperty("value").accept(new EvaluatingVisitor());

        // 可选参数的注解，使用较长的作为最终选项
        if (value instanceof LinkedList) {
            LinkedList<?> values = (LinkedList<?>) value;
            for (Object o : values) {
                if (o.toString().length() > v.length()) {
                    v = o.toString();
                }
            }
        } else {
            v = value.toString();
        }
        return v;
    }

    /**
     * 获取方法签名
     * 由controller的包名，类名和方法名共同构成
     */
    public static String sign(String toSign) {
        try {
            byte[] md5 = MessageDigest.getInstance("MD5").digest((toSign).getBytes(StandardCharsets.UTF_8));

            StringBuilder sb = new StringBuilder();
            for (byte b : md5) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();

        } catch (NoSuchAlgorithmException e) {
            return "";
        }
    }

    /**
     * 获取基本名称，不含包名
     */
    public static String getSimpleName(String className) {

        return className.contains(".") ? className.substring(className.lastIndexOf(".") + 1) : className;
    }

    public static boolean isList(String javaType) {

        // 先粗暴的认为java.util下面的类型为list
        return javaType.startsWith("java.util.");
    }

    public static boolean isPage(String javaType) {

        return javaType.contains("com.baomidou.") && javaType.endsWith("Page");
    }

    public static boolean isRequestParam(String typeName) {

        return typeName.endsWith("RequestBody") || typeName.endsWith("RequestParam") || typeName.endsWith("PathVariable");
    }

    public static boolean isPostParam(String typeName) {

        return typeName.endsWith("RequestBody");
    }

    public static boolean isRequired(AnnotationValue requiredValue) {
        return requiredValue == null || !"false".equals(requiredValue.accept(new EvaluatingVisitor()).toString());
    }
}
