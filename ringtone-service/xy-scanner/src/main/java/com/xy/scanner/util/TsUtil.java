package com.xy.scanner.util;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @since 2023/6/12 14:41
 */
public class TsUtil {


    /**
     * java中的基础数据类型
     */
    private static final HashMap<String, String> JAVA_BASE_TYPE_DICT = new HashMap<>();


    static {
        JAVA_BASE_TYPE_DICT.put("void", "void");
        JAVA_BASE_TYPE_DICT.put("byte", "number");
        JAVA_BASE_TYPE_DICT.put("java.lang.Byte".toLowerCase(), "number");
        JAVA_BASE_TYPE_DICT.put("short", "number");
        JAVA_BASE_TYPE_DICT.put("java.lang.Short".toLowerCase(), "number");
        JAVA_BASE_TYPE_DICT.put("int", "number");
        JAVA_BASE_TYPE_DICT.put("java.lang.Integer".toLowerCase(), "number");
        JAVA_BASE_TYPE_DICT.put("long", "number");
        JAVA_BASE_TYPE_DICT.put("java.lang.Long".toLowerCase(), "number");
        JAVA_BASE_TYPE_DICT.put("float", "number");
        JAVA_BASE_TYPE_DICT.put("java.lang.Float".toLowerCase(), "number");
        JAVA_BASE_TYPE_DICT.put("double", "number");
        JAVA_BASE_TYPE_DICT.put("java.lang.Double".toLowerCase(), "number");
        JAVA_BASE_TYPE_DICT.put("char", "string");
        JAVA_BASE_TYPE_DICT.put("java.lang.Character".toLowerCase(), "string");
        JAVA_BASE_TYPE_DICT.put("boolean", "boolean");
        JAVA_BASE_TYPE_DICT.put("java.lang.Boolean".toLowerCase(), "boolean");
        JAVA_BASE_TYPE_DICT.put("java.lang.String".toLowerCase(), "string");
        JAVA_BASE_TYPE_DICT.put("java.math.BigDecimal".toLowerCase(), "number");
        JAVA_BASE_TYPE_DICT.put("java.util.Date".toLowerCase(), "string");
        JAVA_BASE_TYPE_DICT.put("java.time.LocalDate".toLowerCase(), "string");
        JAVA_BASE_TYPE_DICT.put("java.time.LocalDateTime".toLowerCase(), "string");
        JAVA_BASE_TYPE_DICT.put("java.lang.Object".toLowerCase(), "any");
    }


    public static String java2TsType(String javaType) {
        return JAVA_BASE_TYPE_DICT.get(javaType.toLowerCase());
    }



}
