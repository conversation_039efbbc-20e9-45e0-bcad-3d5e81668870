package com.xy.scanner.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.thoughtworks.qdox.JavaProjectBuilder;
import com.thoughtworks.qdox.builder.impl.EvaluatingVisitor;
import com.thoughtworks.qdox.model.JavaAnnotation;
import com.thoughtworks.qdox.model.JavaClass;
import com.thoughtworks.qdox.model.JavaMethod;
import com.thoughtworks.qdox.model.JavaParameter;
import com.thoughtworks.qdox.model.impl.DefaultJavaParameterizedType;
import com.xy.scanner.entity.HttpApi;
import com.xy.scanner.entity.HttpEntity;
import com.xy.scanner.entity.HttpEntityField;
import com.xy.scanner.util.ScannerUtil;
import com.xy.scanner.util.TsUtil;
import lombok.Data;
import lombok.SneakyThrows;
import org.springframework.util.StringUtils;

import java.io.File;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/6/9 16:52
 */
@Data
public class ScannerService {

    /**
     * 如不需要生成api的方法，请使用该tag
     * `@ignore`
     */
    private static final String IGNORE = "ignore";
    /**
     * 目前只针对RestController的方法生成api
     */
    private static final String CONTROLLER = "RestController";
    private static final String MAPPING = "RequestMapping";

    /**
     * 当前正在处理的项目包名
     */
    private String projectPack;
    /**
     * 当前正在处理的controller名
     */
    private String controllerName;
    private String module;
    private JavaProjectBuilder builder;
    private List<HttpApi> apis;
    private HashMap<String, HttpEntity> entities;
    private List<HttpEntityField> entityFields;

    /**
     * 要扫描的代码基础路径
     */
    private final String baseDir;

    public ScannerService(String baseDir) {
        this.baseDir = baseDir;
    }


    @SneakyThrows
    public void scanThenSave(String src, String module) {
        this.scan(src, module);
        this.saveData();
    }


    public void scan(String src, String module) {

        this.module = module;
        this.apis = new ArrayList<>();
        this.entityFields = new ArrayList<>();
        this.entities = new HashMap<>();
        this.builder = new JavaProjectBuilder();
        this.projectPack = null;

        // 固定读取内容
        builder.addSourceTree(new File(baseDir + "/xy-base-core/src"));
        builder.addSourceTree(new File(baseDir + "/xy-base-feign/src"));
        builder.addSourceTree(new File(baseDir + "/xy-base-mq/src"));
        builder.addSourceTree(new File(baseDir + "/xy-base-starter/src"));

        builder.addSourceTree(new File(baseDir + src));

        builder.getSources().forEach(javaSource -> javaSource.getClasses().forEach(javaClass -> javaClass.getAnnotations().stream().filter(a -> a.getType().getName().endsWith(CONTROLLER)).findAny().ifPresent(c -> scanController(javaClass))));
    }

    private void saveData() throws SQLException {
        Db db = Db.use();

        db.del(Entity.create("http_api").set("module", this.module));
        List<Entity> dbApis = apis.stream().map(Entity::parseWithUnderlineCase).collect(Collectors.toList());
        db.insert(dbApis);

        db.del(Entity.create("http_entity").set("module", module));
        db.del(Entity.create("http_entity_field").set("module", module));
        List<Entity> entityList = entities.values().stream().map(Entity::parseWithUnderlineCase).collect(Collectors.toList());
        db.insert(entityList);

        List<Entity> fields = entityFields.stream().map(Entity::parseWithUnderlineCase).collect(Collectors.toList());
        db.insert(fields);
    }

    /**
     * 开始扫描controller，分析方法
     */
    public void scanController(JavaClass javaClass) {

        if (projectPack == null) {
            String fullName = javaClass.getFullyQualifiedName();
            projectPack = fullName.substring(0, fullName.lastIndexOf(".", fullName.lastIndexOf(".") - 1));
            System.out.println(projectPack);
        }

        controllerName = javaClass.getSimpleName();

        String mapping = javaClass.getAnnotations().stream().filter(a -> a.getType().getName().endsWith(MAPPING)).findAny().map(ScannerUtil::getMappingValue).orElse("");

        javaClass.getMethods().forEach(method -> {
            if (method.getTags().stream().anyMatch(t -> IGNORE.equals(t.getName()))) {
                System.out.println("方法添加了ignore，跳过：" + method.getName());
            } else {
                scanMethod(method, mapping);
            }
        });
    }

    public void scanMethod(JavaMethod method, String mapping) {

        JavaAnnotation urlAno = null;
        String httpMethod = null;
        for (JavaAnnotation annotation : method.getAnnotations()) {
            String name = ScannerUtil.getSimpleName(annotation.getType().getSimpleName());
            if (name.endsWith("GetMapping") || name.endsWith("PostMapping") || name.endsWith("RequestMapping")) {
                urlAno = annotation;
                httpMethod = name.replace("Mapping", "").toUpperCase();
                break;
            }
        }

        if (urlAno == null) {
            System.out.println("方法没有相关mapping注解，跳过：" + method.getName());
            return;
        }

        String url = ScannerUtil.fixUrl(mapping + "/" + ScannerUtil.getMappingValue(urlAno));
        HttpApi api = new HttpApi();
        api.setName(method.getName());
        api.setComment(method.getComment() == null ? "" : method.getComment());
        api.setHttpMethod(httpMethod);
        api.setUrl(url);
        api.setController(controllerName);
        api.setId(ScannerUtil.sign(projectPack + controllerName + method.getName()));
        api.setModule(module);
        api.setCommonName(StrUtil.lowerFirst(controllerName.replace("Controller", "")) + StrUtil.upperFirst(method.getName()));
        dealReq(method, api);
        dealRes(method, api);

//        apis.add(Entity.parseWithUnderlineCase(api));
        apis.add(api);
    }

    public void dealReq(JavaMethod method, HttpApi api) {

        List<JavaParameter> parameters = method.getParameters();

        api.setReqArray(false);

        if (parameters.size() == 0) {
            api.setReqEntity("EmptyData");
            return;
        }

        // 已知问题：目前无法识别数组形式的参数，如果有类似需求，只能构建一个含有数组字段的类
        AtomicBoolean needVirtualEntity = new AtomicBoolean(false);
        AtomicReference<String> parent = new AtomicReference<>("");

        List<HttpEntityField> fields = new ArrayList<>();

        HashMap<String, String> comments = new HashMap<>();

        method.getTags().forEach(tag -> {
            if ("param".equals(tag.getName())) {
                String value = tag.getValue();
                if (StringUtils.hasText(value) && value.contains(" ")) {
                    String n = value.substring(0, value.indexOf(" ")).trim();
                    String c = value.substring(value.indexOf(" ")).trim();
                    comments.put(n, c);
                }
            }
        });

        parameters.forEach(p -> p.getAnnotations().stream()
                .filter(a -> ScannerUtil.isRequestParam(a.getType().getFullyQualifiedName()))
                .findAny()
                .ifPresent(a -> {
                    HttpEntityField f = new HttpEntityField();
                    f.setModule(module);
                    f.setRequired(ScannerUtil.isRequired(a.getProperty("required")));
                    f.setArray(false);

                    // 每一个参数都是一个字段
                    if (ScannerUtil.isPostParam(a.getType().getFullyQualifiedName())) {
                        if (TsUtil.java2TsType(p.getType().getFullyQualifiedName()) != null) {
                            throw new RuntimeException("RequestBody标记的类型必须为对象--" + api.getUrl());
                        }
                        api.setReqEntity(gentEntityName((DefaultJavaParameterizedType) p.getType()));
                        parent.set(api.getReqEntity());
                        f.setName(p.getName());

                        f.setTypeName(api.getReqEntity());
                        f.setRequired(true);
                    } else {
                        String tsType = TsUtil.java2TsType(p.getType().getFullyQualifiedName());
                        if (tsType == null) {
                            throw new RuntimeException("RequestParam或者PathVariable标记的类型必须为基本类型--" + api.getUrl());
                        }
                        needVirtualEntity.set(true);
                        f.setName(a.getProperty("value") == null ? p.getName() : a.getProperty("value").accept(new EvaluatingVisitor()).toString());
                        f.setRequired(ScannerUtil.isRequired(a.getProperty("required")));
                        f.setTypeName(tsType);
                    }
                    f.setComment(StringUtils.hasText(comments.get(f.getName())) ? comments.get(f.getName()) : "");
                    fields.add(f);
                }));

        if (needVirtualEntity.get()) {
            HttpEntity entity = new HttpEntity();

            entity.setName(StrUtil.upperFirst(this.module) + StrUtil.upperFirst(method.getName()) + "Req");
            entity.setId(ScannerUtil.sign(this.module + api.getUrl()));
            entity.setComment(method.getName());
            entity.setModule(module);
            entity.setPackName(api.getUrl());
            if (StringUtils.hasText(api.getReqEntity())) {
                entity.setParent(true);
                entity.setParentName(api.getReqEntity());
            } else {
                entity.setParent(false);
                entity.setParentName("");
            }
            if (StringUtils.hasText(parent.get())) {
                entity.setParent(true);
                entity.setParentName(parent.get());
            } else {
                entity.setParent(false);
                entity.setParentName("");
            }
            entities.put(entity.getPackName() + entity.getName(), entity);
            fields.forEach(f -> {
                f.setEntityId(entity.getId());
                entityFields.add(f);
            });

            api.setReqEntity(entity.getName());
        }

    }

    public void dealRes(JavaMethod method, HttpApi api) {

        DefaultJavaParameterizedType returns = (DefaultJavaParameterizedType) method.getReturns();

        if (returns.isArray() || ScannerUtil.isList(returns.getFullyQualifiedName())) {
            api.setResArray(true);
        }

        api.setResEntity(gentEntityName(returns));

        if (ScannerUtil.isPage(returns.getFullyQualifiedName())) {
            api.setResEntity(StrUtil.format("ResPage<{}>", api.getResEntity()));
        }
    }

    /**
     * 生成实体对象
     *
     * @return 对象名称
     */
    private String gentEntityName(DefaultJavaParameterizedType type) {
        /*
        生成规则，如果是基本对象，直接返回ts类型名称
        否则开始判断：
        是否为数组或者集合（集合必须为泛型，否则会报错，代码规范要求必须为泛型集合，哪怕是object），标记好后取起具体数组类型再次生成类型
        是否为泛型，由于经过了集合的判断，泛型只可能是对象，而且一般需要特殊处理
        普通类直接生成
         */
        return Optional.ofNullable(TsUtil.java2TsType(type.getFullyQualifiedName()))
                .orElseGet(() -> {
                    String toGentFullName = type.getFullyQualifiedName();
                    if (type.isArray() || ScannerUtil.isList(type.getFullyQualifiedName())) {
                        if (type.isArray()) {
                            toGentFullName = type.getBinaryName();
                        } else {
                            toGentFullName = type.getActualTypeArguments().get(0).getFullyQualifiedName();
                        }
                    } else if (type.getActualTypeArguments().size() > 0) {
                        toGentFullName = type.getActualTypeArguments().get(0).getFullyQualifiedName();
                    }

                    final String finalGentFullName = toGentFullName;
                    return Optional.ofNullable(TsUtil.java2TsType(toGentFullName))
                            .orElseGet(() -> {
                                if (finalGentFullName.startsWith(this.projectPack)) {

                                    HttpEntity entity = new HttpEntity();
                                    entity.setName(StrUtil.upperFirst(this.module) + ScannerUtil.getSimpleName(finalGentFullName));
                                    entity.setId(ScannerUtil.sign(finalGentFullName));
                                    entity.setComment(type.getComment());
                                    entity.setModule(module);
                                    entity.setParent(false);
                                    entity.setParentName("");
                                    entity.setPackName(finalGentFullName);

                                    if (!entities.containsKey(finalGentFullName)) {
                                        System.out.println("非基础类型准备生成新对象：" + finalGentFullName + " 发起方：" + type.getFullyQualifiedName());

                                        entities.put(finalGentFullName, entity);

                                        JavaClass entityClass = builder.getClassByName(finalGentFullName);

                                        if (!"java.lang.Object".equals(entityClass.getSuperClass().getFullyQualifiedName())) {
                                            entity.setParent(true);
                                            entity.setParentName(gentEntityName((DefaultJavaParameterizedType) entityClass.getSuperClass()));
                                        }

                                        gentEntityField(entity.getId(), entityClass);
                                    }
                                    return entity.getName();
                                } else {
                                    // 如果要生成的对象不在本项目，先用any表示，暂不生成(一般会在公共文件中补充，根据情况调整)
                                    // 根据情况返回any或者直接返回类名（如果在公共文件中定义）
                                    return ScannerUtil.getSimpleName(finalGentFullName);
//                                    return "any";
                                }
                            });
                });
    }

    /**
     * 生成字段
     */
    private void gentEntityField(String entityId, JavaClass entityClass) {

        entityClass.getFields().forEach(f -> {

            DefaultJavaParameterizedType fieldType = (DefaultJavaParameterizedType) f.getType();

            HttpEntityField field = new HttpEntityField();
            field.setEntityId(entityId);
            field.setModule(module);
            field.setName(f.getName());
            field.setArray(false);
            field.setComment(StringUtils.hasText(f.getComment()) ? f.getComment() : "");

            field.setTypeName(Optional.ofNullable(TsUtil.java2TsType(fieldType.getFullyQualifiedName()))
                    .orElseGet(() -> {
                        String toGentFullName = fieldType.getFullyQualifiedName();
                        // 是否为数组对象
                        if (fieldType.isArray() || ScannerUtil.isList(toGentFullName)) {
                            field.setArray(true);
                            if (fieldType.isArray()) {
                                toGentFullName = fieldType.getBinaryName();
                            } else {
                                toGentFullName = fieldType.getActualTypeArguments().get(0).getFullyQualifiedName();
                            }
                        }
                        return Optional.ofNullable(TsUtil.java2TsType(toGentFullName)).orElseGet(() -> gentEntityName(fieldType));
                    })
            );

            field.setRequired(false);
            entityFields.add(field);

        });

    }

}
