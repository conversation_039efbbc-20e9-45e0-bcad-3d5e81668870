package ${domain.packageName};

<#list tableClass.pkFields as field><#if field.autoIncrement>import com.baomidou.mybatisplus.annotation.IdType;</#if></#list>
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
<#list tableClass.importList as fieldType>import ${fieldType};${"\n"}</#list>
/**
 * ${tableClass.remark!}
 *
 * <AUTHOR>
 * @since ${.now?string('yyyy-MM-dd')}
 */
@TableName(value = "${tableClass.tableName}")
@Data
public class ${tableClass.shortClassName} implements Serializable {

<#list tableClass.pkFields as field>
    /**
     * ${field.remark!}
     */
    @TableId<#if field.autoIncrement>(type = IdType.AUTO)</#if>
    private ${field.shortTypeName} ${field.fieldName};
</#list>

<#list tableClass.baseFields as field>
    /**
     * ${field.remark!}
     */
    private ${field.shortTypeName} ${field.fieldName};
</#list>

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
