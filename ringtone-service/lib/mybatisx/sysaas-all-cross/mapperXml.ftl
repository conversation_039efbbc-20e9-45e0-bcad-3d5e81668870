<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<#assign basePackage = domain.packageName?replace('.entity', '')>
<#assign interfacePackage = basePackage + ".mapper">
<mapper namespace="${interfacePackage}.${baseInfo.fileName}">

    <!--suppress SqlDialectInspection -->
    <sql id="fullAssociationField">
        <#list tableClass.allFields as field>${tableClass.tableName}.${field.columnName} as ${tableClass.tableName}_${field.columnName}<#sep>,${"\n        "}</#list>
    </sql>

    ${r'<!--'}
    <select id="list??VO" resultMap="??VO">
        select
        <include refid="fullAssociationField"/>,

        <include refid="${interfacePackage}.??.fullAssociationField"/>

        FROM ${tableClass.tableName}
        LEFT JOIN ?? ON ${tableClass.tableName}.?? = ??.??
        ${r'${ew.customSqlSegment}'}

    </select>
    <resultMap id="??VO" type="${basePackage}.??" extends="associationResultMap">
        <association property="??" resultMap="${interfacePackage}.??.associationResultMap"/>
    </resultMap>
    ${r'-->'}

    <resultMap id="associationResultMap" type="${tableClass.fullClassName}">
        <#list tableClass.pkFields as field>
            <id property="${field.fieldName}" column="${tableClass.tableName}_${field.columnName}"/>
        </#list>
        <#list tableClass.baseFields as field>
            <result property="${field.fieldName}" column="${tableClass.tableName}_${field.columnName}"/>
        </#list>
    </resultMap>

</mapper>
