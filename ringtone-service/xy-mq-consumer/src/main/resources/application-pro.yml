spring:
  cloud:
    nacos:
      config:
        enabled: true
        server-addr: 172.18.188.192:6846
        prefix: base
        file-extension: yaml
        group: DEFAULT_GROUP
      discovery:
        enabled: true
        server-addr: 172.18.188.192:6846
        
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************
    username: tcyy
    password: tcdbtc
    hikari:
      connection-test-query: select 1 from dual
      connection-timeout: 30000
      idle-timeout: 60000
      max-lifetime: 1800000
      maximum-pool-size: 20
      minimum-idle: 1

  redis:
    database: 0
    host: *************
    password: On34QubzIEXx3Gs28vuf03GiUg1JMlZH
    port: 6379

mq:
  address: ************:9876

logging:
  config: classpath:logback-spring-tcyy.xml
