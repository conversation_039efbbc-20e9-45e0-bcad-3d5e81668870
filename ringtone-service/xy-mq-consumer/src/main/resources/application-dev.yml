spring:
  cloud:
    nacos:
      discovery:
        enabled: false
        server-addr: 192.168.0.32:8848

  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************
    username: sysaas
    password: Ku#mf0dZ
    hikari:
      connection-test-query: select 1 from dual
      connection-timeout: 30000
      idle-timeout: 60000
      max-lifetime: 1800000
      maximum-pool-size: 20
      minimum-idle: 1

  redis:
    database: 6
    host: ************
    password: YAGi2acwr4oIBTbB8geq
    port: 6378

mq:
  address: ************:9876

logging:
  config: classpath:logback-spring-dev.xml
