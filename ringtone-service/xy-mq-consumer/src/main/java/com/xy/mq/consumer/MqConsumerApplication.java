package com.xy.mq.consumer;

import com.xy.base.mq.annotation.EnableMQProducer;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR>
 * @since 2020/11/17
 */
@SpringBootApplication
@EnableDiscoveryClient
@MapperScan("com.xy.*.mapper")
@EnableMQProducer
@EnableAsync
public class MqConsumerApplication {

    public static void main(String[] args) {
        SpringApplication.run(MqConsumerApplication.class, args);
    }
}
