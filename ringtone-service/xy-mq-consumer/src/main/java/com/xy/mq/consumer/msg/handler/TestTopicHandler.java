package com.xy.mq.consumer.msg.handler;

import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.springframework.stereotype.Component;

/**
 * 测试mq消费(仅作为演示用)
 *
 * <AUTHOR>
 * @since 2022/10/27
 */
@Component
public class TestTopicHandler {

    public ConsumeConcurrentlyStatus testTagProcessor(String msg) {

        System.out.println("收到消息:" + msg);
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

}
