package com.xy.mq.consumer.msg;

import cn.hutool.core.util.StrUtil;
import com.xy.base.core.exception.AppException;
import com.xy.base.core.util.SpringUtils;
import com.xy.base.mq.constant.MQConsts;
import com.xy.base.mq.constant.MQTopic;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Async;

import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.HashMap;

/**
 * RocketMQ消息队列消费实现
 *
 * <AUTHOR>
 * @since 2022/8/19.
 */
@Slf4j
@Configuration
public class MQRunner implements ApplicationRunner {

    private final HashMap<String, HashMap<String, Method>> mqHandlers = new HashMap<>();

    @Value("${mq.address:************:9876}")
    private String address;

    // 消费者线程数据量
    private static final int CONSUME_THREAD_MIN = 4;
    private static final int CONSUME_THREAD_MAX = 16;

    /**
     * MQ消费线程，消费规则如下：
     * 收到消息时，在spring beans中查找相关bean（名称为 msg.getTopic() + Handler）
     * 然后执行该bean的msg.getTags() + Processor方法
     * 所以需要处理消息的bean应该用Handler结尾命名，且注解为 @Component
     * 例如TestTopicHandler表示处理testTopic中的消息
     * testTagProcessor方法负责处理tag为testTag的消息
     * 当接收到消息时开启单线程处理相关数据。注意方法效率，如果一个方法耗时过长会影响其他方法
     */
    @Async
    @Override
    public void run(ApplicationArguments args) throws MQClientException {

        log.info("MQ consumer thread ready 2 start---------------------------------------");

        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(MQConsts.CLUSTER_GROUP);
        consumer.setNamesrvAddr(address);
        consumer.setConsumeThreadMin(CONSUME_THREAD_MIN);
        consumer.setConsumeThreadMax(CONSUME_THREAD_MAX);
        // 设置一次消费消息的条数，默认1
        consumer.setConsumeMessageBatchMaxSize(1);
        // 设置consumer第一次启动是从队列头部开始还是队列尾部开始 如果不是第一次启动，那么按照上次消费的位置继续消费
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
        // 设置消费模型，集群还是广播，默认为集群
        consumer.setMessageModel(MessageModel.CLUSTERING);

        // 订阅主题，如果还有其他主题，需要这这里订阅
        consumer.subscribe(MQTopic.TEST, "*");
        consumer.subscribe(MQTopic.ORDER, "*");
        consumer.subscribe(MQTopic.SYS, "*");
        consumer.subscribe(MQTopic.WECHAT, "*");

        consumer.registerMessageListener((MessageListenerConcurrently) (list, consumeConcurrentlyContext) -> {
            ConsumeConcurrentlyStatus status = null;

            // 根据配置，每次只会消费1条消息（虽然是个list，实际只有1条消息）

            for (MessageExt msg : list) {
                try {
                    log.info("准备处理消息 {}:{}:{}", msg.getTopic(), msg.getTags(), msg.getMsgId());
                    status = consumerMsg(msg);
                } catch (Exception e) {
                    log.error("消费消息错误：" + e.getMessage());
                    status = ConsumeConcurrentlyStatus.RECONSUME_LATER;
                } finally {
                    assert status != null;
                    log.info("处理消息 {}:{}:{}:{}", msg.getTopic(), msg.getTags(), status, msg.getMsgId());
                }
            }

            return status;
        });

        consumer.start();
        log.info("MQ consumer thread start success---------------------------------------");
    }

    private ConsumeConcurrentlyStatus consumerMsg(MessageExt msg) {

        final String handlerName = StrUtil.lowerFirst(msg.getTopic()) + "Handler";
        final String tagMethodName = (msg.getTags() + "Processor").toLowerCase();

        HashMap<String, Method> handler = mqHandlers.get(handlerName);
        // 在调用方法的时候需要用到类的实例，先获取出来（如果没有相关实例，会直接抛出异常）
        // 通过名称从spring beans中获取（名称为对应实体类的首字母小写字母，规则由spring boot控制，可能会修改）
        Object handlerBean = SpringUtils.getApplicationContext().getBean(handlerName);

        if (handler == null) {
            // 如果没有找到相关的处理器，尝试注册
            Method[] declaredMethods = handlerBean.getClass().getDeclaredMethods();
            HashMap<String, Method> handlerMethods = new HashMap<>();

            for (Method m : declaredMethods) {
                if (Modifier.isPublic(m.getModifiers())) {
                    handlerMethods.put(m.getName().toLowerCase(), m);
                }
            }
            handler = handlerMethods;
            mqHandlers.put(handlerName, handler);
        }

        // 尝试调用相关的tag处理方法
        Method method = handler.get(tagMethodName);
        AppException.tnt(method == null, StrUtil.format("没有相应的tag处理方法:{}-{}", handlerName, tagMethodName));

        try {
            return (ConsumeConcurrentlyStatus) method.invoke(handlerBean, new String(msg.getBody()));
        } catch (Exception e) {
            // 当异常超过16次，消息会进入死信队列
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
    }
}
