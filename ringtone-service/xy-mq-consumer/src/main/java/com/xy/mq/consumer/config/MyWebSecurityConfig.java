package com.xy.mq.consumer.config;

import com.xy.base.starter.security.filter.RedisSecurityFilter;
import com.xy.base.starter.security.handler.SimpleAccessDeniedHandler;
import com.xy.base.starter.security.handler.SimpleAuthenticationEntryPoint;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * <AUTHOR>
 * @since 2020/7/27
 */
@Configuration
@EnableWebSecurity
public class MyWebSecurityConfig {

    @Bean
    public RedisSecurityFilter authenticationTokenFilterBean() {
        return new RedisSecurityFilter();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity httpSecurity) throws Exception {

        return httpSecurity
                .csrf(AbstractHttpConfigurer::disable)
                .exceptionHandling(exp -> exp
                        .accessDeniedHandler(new SimpleAccessDeniedHandler())
                        .authenticationEntryPoint(new SimpleAuthenticationEntryPoint()))
                .sessionManagement(s -> s.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .authorizeHttpRequests(authorize -> authorize.anyRequest().authenticated())
                .addFilterBefore(authenticationTokenFilterBean(), UsernamePasswordAuthenticationFilter.class)
                .headers(h -> h.cacheControl(HeadersConfigurer.CacheControlConfig::disable))
                .build();
    }
}
