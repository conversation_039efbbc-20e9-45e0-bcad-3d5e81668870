-- 营销推广订单综合表
CREATE TABLE `api_order` (
     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
     `app_id` varchar(32) DEFAULT NULL COMMENT '应用ID',
     `channel_no` varchar(32) DEFAULT NULL COMMENT '渠道编号',
     `product_no` varchar(32) DEFAULT NULL COMMENT '产品编号',
     `mobile_no` varchar(20) DEFAULT NULL COMMENT '手机号码',
     `sms_code` varchar(16) DEFAULT NULL COMMENT '短信验证码',
     `linkId` varchar(32)  DEFAULT '0' COMMENT '订购linkId',
     `order_id` varchar(64) DEFAULT NULL COMMENT '订单ID',
     `out_order_id` varchar(64) DEFAULT NULL COMMENT '外部订单ID',
     `order_status` varchar(16) DEFAULT NULL COMMENT '订单状态码',
     `operation_type` varchar(32) DEFAULT NULL COMMENT '操作类型：SMS-短信发送，ORDER-订单提交',
     `status` tinyint(1) DEFAULT '0' COMMENT '状态：0-处理中，1-成功，2-失败',
     `client_ip` varchar(64) DEFAULT NULL COMMENT '客户端IP',
     `user_agent` varchar(1024) DEFAULT NULL COMMENT '用户代理',
     `app_package` varchar(128) DEFAULT NULL COMMENT '应用包名',
     `app_name` varchar(128) DEFAULT NULL COMMENT '应用名称',
     `platform` varchar(64) DEFAULT NULL COMMENT '平台名称',
     `page_url` varchar(1024) DEFAULT NULL COMMENT '页面URL',
     `remark` varchar(512) DEFAULT NULL COMMENT '备注',
     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     PRIMARY KEY (`id`),
     UNIQUE KEY `uk_order_id` (`order_id`),
     UNIQUE KEY `uk_out_order_id` (`out_order_id`),
     KEY `idx_mobile_no` (`mobile_no`),
     KEY `idx_create_time` (`create_time`),
     KEY `idx_channel_product` (`channel_no`,`product_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='营销推广订单综合表';