<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.promote.mapper.LogApiMapper">

    <resultMap id="BaseResultMap" type="com.xy.promote.entity.LogApi">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="url" column="url" jdbcType="VARCHAR"/>
            <result property="ip" column="ip" jdbcType="VARCHAR"/>
            <result property="method" column="method" jdbcType="VARCHAR"/>
            <result property="position" column="position" jdbcType="VARCHAR"/>
            <result property="request" column="request" jdbcType="VARCHAR"/>
            <result property="response" column="response" jdbcType="VARCHAR"/>
            <result property="timing" column="timing" jdbcType="INTEGER"/>
            <result property="exception" column="exception" jdbcType="BIT"/>
            <result property="exceptionName" column="exception_name" jdbcType="VARCHAR"/>
            <result property="exceptionTrace" column="exception_trace" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,type,url,
        ip,method,position,
        request,response,timing,
        exception,exception_name,exception_trace,
        create_time
    </sql>
</mapper>
