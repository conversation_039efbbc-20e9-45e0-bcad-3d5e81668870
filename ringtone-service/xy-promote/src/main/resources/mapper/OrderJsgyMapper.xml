<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.promote.mapper.OrderJsgyMapper">

    <resultMap id="BaseResultMap" type="com.xy.promote.entity.OrderJsgy">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="msg" column="msg" jdbcType="VARCHAR"/>
            <result property="price" column="price" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="channelNo" column="channel_no" jdbcType="VARCHAR"/>
            <result property="linkid" column="linkid" jdbcType="VARCHAR"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="cpparam" column="cpparam" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,msg,price,
        status,channel_no,linkid,
        phone,cpparam,create_time,
        update_time
    </sql>
</mapper>
