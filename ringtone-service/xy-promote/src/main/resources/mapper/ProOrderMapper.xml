<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.promote.mapper.ProOrderMapper">

    <resultMap id="BaseResultMap" type="com.xy.promote.entity.ProOrder">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="channel" column="channel" jdbcType="VARCHAR"/>
            <result property="source" column="source" jdbcType="VARCHAR"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="reportStatus" column="report_status" jdbcType="SMALLINT"/>
            <result property="huichuanStatus" column="huichuan_status" jdbcType="SMALLINT"/>
            <result property="pro" column="pro" jdbcType="VARCHAR"/>
            <result property="callback" column="callback" jdbcType="VARCHAR"/>
            <result property="platform" column="platform" jdbcType="VARCHAR"/>
            <result property="projectid" column="projectid" jdbcType="VARCHAR"/>
            <result property="promotionid" column="promotionid" jdbcType="VARCHAR"/>
            <result property="orderId" column="order_id" jdbcType="VARCHAR"/>
            <result property="url" column="url" jdbcType="VARCHAR"/>
            <result property="ua" column="ua" jdbcType="VARCHAR"/>
            <result property="app" column="app" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,channel,source,
        phone,report_status,huichuan_status,
        pro,callback,platform,
        projectid,promotionid,order_id,
        url,ua,app,
        create_time,update_time
    </sql>
</mapper>
