<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.promote.mapper.DliangInfoMapper">
  <resultMap id="BaseResultMap" type="com.xy.promote.entity.DliangInfo">
    <!--@mbg.generated-->
    <!--@Table dliang_info-->
    <result column="pro" jdbcType="VARCHAR" property="pro" />
    <result column="orderId" jdbcType="VARCHAR" property="orderid" />
    <result column="notice" jdbcType="VARCHAR" property="notice" />
    <result column="enable" jdbcType="SMALLINT" property="enable" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    pro, orderId, notice, `enable`, description, create_time, update_time
  </sql>
</mapper>