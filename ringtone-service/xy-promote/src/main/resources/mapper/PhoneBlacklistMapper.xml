<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.promote.mapper.PhoneBlacklistMapper">
  <resultMap id="BaseResultMap" type="com.xy.promote.entity.PhoneBlacklist">
    <!--@mbg.generated-->
    <!--@Table phone_blacklist-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, phone, `type`, description, create_time, update_time
  </sql>
  <update id="updateByPrimaryKey" parameterType="com.xy.promote.entity.PhoneBlacklist">
    <!--@mbg.generated-->
    update phone_blacklist
    set phone = #{phone,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      description = #{description,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>