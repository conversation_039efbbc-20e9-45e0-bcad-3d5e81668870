<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.promote.mapper.PackageFilterBaseMapper">
  <resultMap id="BaseResultMap" type="com.xy.promote.entity.PackageFilterBase">
    <!--@mbg.generated-->
    <!--@Table package_filter_base-->
    <id column="pkg" jdbcType="VARCHAR" property="pkg" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="usefor" jdbcType="VARCHAR" property="usefor" />
    <result column="nofor" jdbcType="VARCHAR" property="nofor" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    pkg, `name`, usefor, nofor, create_time, update_time
  </sql>
  <update id="updateByPrimaryKey" parameterType="com.xy.promote.entity.PackageFilterBase">
    <!--@mbg.generated-->
    update package_filter_base
    set `name` = #{name,jdbcType=VARCHAR},
      usefor = #{usefor,jdbcType=VARCHAR},
      nofor = #{nofor,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where pkg = #{pkg,jdbcType=VARCHAR}
  </update>
</mapper>