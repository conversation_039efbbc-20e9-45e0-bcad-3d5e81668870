<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.promote.mapper.ProductPromotionCompanyMapper">
  <resultMap id="BaseResultMap" type="com.xy.promote.entity.ProductPromotionCompany">
    <!--@mbg.generated-->
    <!--@Table product_promotion_company-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="secret_key" jdbcType="VARCHAR" property="secretKey" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, company_id, company_name, app_id, secret_key, create_time, update_time
  </sql>
  <update id="updateByPrimaryKey" parameterType="com.xy.promote.entity.ProductPromotionCompany">
    <!--@mbg.generated-->
    update product_promotion_company
    set company_id = #{companyId,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      app_id = #{appId,jdbcType=VARCHAR},
      secret_key = #{secretKey,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>