<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.promote.mapper.ProInfoProductMapper">
  <resultMap id="BaseResultMap" type="com.xy.promote.entity.ProInfoProduct">
    <!--@mbg.generated-->
    <!--@Table pro_info_product-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company" jdbcType="VARCHAR" property="company" />
    <result column="product" jdbcType="VARCHAR" property="product" />
    <result column="pro" jdbcType="VARCHAR" property="pro" />
    <result column="product_no" jdbcType="VARCHAR" property="productNo" />
    <result column="params" jdbcType="VARCHAR" property="params" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, company, product, pro, product_no, params, create_time, update_time
  </sql>
  <update id="updateByPrimaryKey" parameterType="com.xy.promote.entity.ProInfoProduct">
    <!--@mbg.generated-->
    update pro_info_product
    set company = #{company,jdbcType=VARCHAR},
      product = #{product,jdbcType=VARCHAR},
      pro = #{pro,jdbcType=VARCHAR},
      product_no = #{productNo,jdbcType=VARCHAR},
      product_no = #{params,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>