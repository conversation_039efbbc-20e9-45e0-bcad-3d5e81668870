<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.promote.mapper.ConfigHuichuanMapper">

    <resultMap id="BaseResultMap" type="com.xy.promote.entity.ConfigHuichuan">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="channel" column="channel" jdbcType="VARCHAR"/>
            <result property="source" column="source" jdbcType="VARCHAR"/>
            <result property="paramName" column="param_name" jdbcType="VARCHAR"/>
            <result property="platform" column="platform" jdbcType="VARCHAR"/>
            <result property="nodelay" column="nodelay" jdbcType="SMALLINT"/>
            <result property="huichuanPct" column="huichuan_pct" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,channel,source,
        param_name,platform,nodelay,
        huichuan_pct,create_time,update_time
    </sql>
</mapper>
