spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************************************************
    username: couser1
    password: USero01@ARd21@Liy2504
    hikari:
      connection-timeout: 3000
      idle-timeout: 5000
      max-lifetime: 5500
      maximum-pool-size: 20
      minimum-idle: 5

  redis:
    database: 3
    host: 127.0.0.1
    password:
    port: 6379

logging:
  config: classpath:logback-spring-dev.xml

server:
  tag: test01