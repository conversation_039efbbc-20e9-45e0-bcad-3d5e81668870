package com.xy.promote.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.xy.promote.dto.ProOrderDTO;
import com.xy.promote.dto.jsgy.JsgyCheckCodeReq;
import com.xy.promote.dto.jsgy.JsgyResponse;
import com.xy.promote.dto.jsgy.JsgyVerifyCodeReq;
import com.xy.promote.dto.zsgl.ZsglReqVO;
import com.xy.promote.service.JsgyService;
import com.xy.promote.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;

/**
 * 江苏冠优
 */
@Slf4j
@RestController
@RequestMapping("/jsgy")
@RequiredArgsConstructor

// @TODO 暂时未使用，适配原来zsgl的原页面，上线前先检查
public class JsgyController {
    
    private final JsgyService jsgyService;
    
    /**
     * 获取验证码
     */
    @PostMapping("/getVerifyCode")
    public JsgyResponse getVerifyCode(@RequestBody ZsglReqVO params, HttpServletRequest request) {
        // 请求参数
        JsgyVerifyCodeReq req = JsgyVerifyCodeReq.builder()
                .channelNo(params.getChannelNo())
                .price(params.getPrice())
                .cpparam(params.getCpparam())
                .phone(params.getPhone())
                .ip(ServletUtil.getClientIP(request))
                .appPackage(StringUtil.extractPackageName(request.getHeader("user-agent")))
                //.appName(getAppName(config == null ? "" : config.getPlatform())) // 服务层已经处理了，这里不需要
                .userAgent(request.getHeader("user-agent"))
                .build();
        // 订单参数
        ProOrderDTO proDTO = ProOrderDTO.builder()
                .ua(request.getHeader("user-agent"))
                .pro(params.getCpparam() + "_" + params.getChannel())
                .build();
        BeanUtil.copyProperties(params, req);
        return jsgyService.getVerifyCode(req, proDTO);
    }
    
    /**
     * 验证验证码
     */
    @PostMapping("/checkVerifyCode")
    public JsgyResponse checkVerifyCode(@RequestParam HashMap<String, String> params) {
        JsgyCheckCodeReq req = JsgyCheckCodeReq.builder()
                .linkid(params.get("linkid"))
                .vcode(params.get("vcode"))
                .build();
        return jsgyService.checkVerifyCode(req);
    }
}