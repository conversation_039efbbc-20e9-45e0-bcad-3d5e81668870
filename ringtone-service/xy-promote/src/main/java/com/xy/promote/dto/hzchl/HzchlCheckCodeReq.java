package com.xy.promote.dto.hzchl;

import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 杭州铖晗联验证码校验请求实体
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class HzchlCheckCodeReq {
    /**
     * 产品ID
     */
    private String productId;
    
    /**
     * 链接ID
     */
    private String linkid;
    
    /**
     * 验证码
     */
    private String vcode;

    /**
     * 从ZlllgMessageDTO创建HzchlCheckCodeReq实例
     *
     * @param reqDTO 请求DTO对象
     * @param productId 产品ID
     * @param linkId 链接ID
     * @return HzchlCheckCodeReq实例
     */
    public static HzchlCheckCodeReq fromReqDTO(ZlllgMessageDTO reqDTO, String productId, String linkId) {
        return HzchlCheckCodeReq.builder()
                .productId(productId)
                .linkid(linkId)
                .vcode(reqDTO.getBiz().getSmsCode())
                .build();
    }
}
