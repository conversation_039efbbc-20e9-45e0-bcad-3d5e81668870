package com.xy.promote.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 长沙云蓑响应码映射枚举
 * 将长沙云蓑的响应码映射到众联流量果的响应码和描述
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
@Getter
@AllArgsConstructor
public enum CsysAndZlllgResCode {

  SUCCESS(0, "成功", "0000", "短信发送成功"),
  PROVINCE_LIMIT_REACHED(101, "该省份已到量", "E010", "该省份达到今日限量，明天9点可继续办理"),
  OTHER_FAILURE(1, "其他失败", "E101", "其他失败（运营商或其他系统返回失败）");

  /**
   * 长沙云蓑响应码（0表示成功，其他为失败）
   */
  private final Integer code;

  /**
   * 长沙云蓑结果消息
   */
  private final String resultMsg;

  /**
   * 对应的众联流量果响应码
   */
  private final String respCode;

  /**
   * 对应的众联流量果响应描述
   */
  private final String respDesc;

  /**
   * 根据响应码获取枚举
   *
   * @param code 响应码
   * @return 对应的枚举值，如果不存在则返回null
   */
  public static CsysAndZlllgResCode getByCode(Integer code) {
    for (CsysAndZlllgResCode resCode : values()) {
      if (resCode.getCode().equals(code)) {
        return resCode;
      }
    }
    return null;
  }

  /**
   * 根据响应码获取枚举，如果不存在则返回默认的OTHER_FAILURE
   *
   * @param code 响应码
   * @return 对应的枚举值，如果不存在则返回OTHER_FAILURE
   */
  public static CsysAndZlllgResCode getByCodeWithDefault(Integer code) {
    CsysAndZlllgResCode result = getByCode(code);
    return result != null ? result : OTHER_FAILURE;
  }

  /**
   * 根据响应码获取响应码，如果不存在则返回默认的"E101"
   *
   * @param code 响应码
   * @return 对应的响应码，如果不存在则返回"E101"
   */
  public static String getRespCodeByCode(Integer code) {
    CsysAndZlllgResCode resCode = getByCode(code);
    return resCode != null ? resCode.getRespCode() : OTHER_FAILURE.getRespCode();
  }

  /**
   * 根据响应码获取响应描述
   *
   * @param respCode 响应码
   * @return 对应的响应描述，如果不存在则返回默认值
   */
  public static String getRespDescByRespCode(String respCode) {
    for (CsysAndZlllgResCode resCode : values()) {
      if (resCode.getRespCode().equals(respCode)) {
        return resCode.getRespDesc();
      }
    }
    return OTHER_FAILURE.getRespDesc();
  }

  /**
   * 根据响应码获取枚举
   *
   * @param respCode 响应码
   * @return 对应的枚举值，如果不存在则返回null
   */
  public static CsysAndZlllgResCode getByRespCode(String respCode) {
    for (CsysAndZlllgResCode resCode : values()) {
      if (resCode.getRespCode().equals(respCode)) {
        return resCode;
      }
    }
    return null;
  }

}