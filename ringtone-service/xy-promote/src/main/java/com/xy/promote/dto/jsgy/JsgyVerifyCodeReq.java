package com.xy.promote.dto.jsgy;

import cn.hutool.core.util.StrUtil;
import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Builder
@Accessors(chain = true)
public class JsgyVerifyCodeReq {
    /**
     * 渠道编号
     */
    private String channelNo;
    
    /**
     * 价格
     */
    private String price;
    
    /**
     * 额外参数
     */
    private String cpparam;
    
    /**
     * 手机号码
     */
    private String phone;
    
    /**
     * IP地址
     */
    private String ip;
    
    /**
     * 应用包名
     */
    private String appPackage;
    
    /**
     * 应用名称
     */
    private String appName;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 平台
     */
    private String platform;
    
    public static JsgyVerifyCodeReq fromLlgReqDTO(ZlllgMessageDTO reqDTO, String pricce, String channelNo) {
        String appPackage = StrUtil.blankToDefault(reqDTO.getBiz().getSource().getAppPackage(), reqDTO.getBiz().getSource().getUserAgent());
        String appName = StrUtil.blankToDefault(reqDTO.getBiz().getSource().getAppName(), "其他");
        return JsgyVerifyCodeReq.builder()
                .channelNo(channelNo) // 这个是请求江苏冠优的渠道编号，从产品配置里面取
                .price(pricce)
                .cpparam("juliang") // 接口必填，暂时给一个默认的
                .phone(reqDTO.getBiz().getMobileNo())
                .ip(reqDTO.getBiz().getSource().getClientIp())
                .appPackage(appPackage)
                .appName(appName)
                .userAgent(reqDTO.getBiz().getSource().getUserAgent())
                //.platform(reqDTO.getBiz().getSource().getPlatform()) // 不用设置，请求短信的时候，在回传配置表里面获取
                .build();
    }
}
