package com.xy.promote.service;

import com.xy.base.core.exception.AppException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API提供商工厂，根据产品编号路由到对应的服务实现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ApiProviderFactory {
	
	private final List<ApiProviderService> apiProviders;
	private final Map<String, ApiProviderService> productProviderMap = new HashMap<>();
	
	/**
	 * 初始化产品编号与服务提供商的映射关系
	 */
	@PostConstruct
	public void init() {
		apiProviders.forEach(provider -> {
			String supportedProductNo = provider.getSupportedProductNo();
			if (supportedProductNo == null || supportedProductNo.isEmpty()) {
				log.warn("服务提供商 {} 不支持任何产品编号", provider.getClass().getSimpleName());
				return;
			}
			productProviderMap.put(supportedProductNo, provider);
			log.info("注册编号 {} 到服务提供商 {}", supportedProductNo, provider.getClass().getSimpleName());
		});
	}
	
	/**
	 * 根据产品编号获取对应的服务提供商
	 *
	 * @param productNo 产品编号
	 * @return 服务提供商实现
	 * @throws IllegalArgumentException 如果找不到对应的服务提供商
	 */
	public ApiProviderService getProvider(String productNo) {
		ApiProviderService provider = productProviderMap.get(productNo.substring(0, 2));
		if (provider == null) {
			throw new AppException("未找到" + productNo + "对应的服务");
		}
		return provider;
	}
}