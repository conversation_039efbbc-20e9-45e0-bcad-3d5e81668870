package com.xy.promote.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 服务器配置，用于获取服务器变量，区分请求来源服务器
 */
@Slf4j
@Data
@Component
public class ServerTagConfig {

  private String serverTag;

  @Value("${server.tag:#{null}}")
  private String configServerTag;

  /**
   * 应用启动时检查环境变量和配置文件
   */
  @PostConstruct
  public void init() {
    // 第一优先级：从环境变量读取
    serverTag = System.getenv("SERVER_TAG");

    if (serverTag != null && !serverTag.trim().isEmpty()) {
      log.info("应用启动成功，使用环境变量SERVER_TAG作为服务器标识: {}", serverTag);
      return;
    }

    // 第二优先级：从配置文件读取
    if (configServerTag != null && !configServerTag.trim().isEmpty()) {
      serverTag = configServerTag;
      log.info("应用启动成功，使用配置文件server.tag作为服务器标识: {}", serverTag);
      return;
    }

    // 都没有读取到，报错
    String errorMessage = "服务器标识未设置！请通过以下任一方式设置：\n" +
        "方式1 - 环境变量：\n" +
        "  Linux/Mac: export SERVER_TAG=zsgl01\n" +
        "  Windows: set SERVER_TAG=zsgl01\n" +
        "  Docker: -e SERVER_TAG=zsgl01\n" +
        "  Java启动: -DSERVER_TAG=zsgl01\n" +
        "方式2 - 配置文件：\n" +
        "  在application.yml中添加：\n" +
        "  server:\n" +
        "    tag: zsgl01";

    log.error(errorMessage);

    // 抛出运行时异常，强制用户设置服务器标识
    throw new RuntimeException("服务器标识未设置，请设置后重启应用！");
  }

  /**
   * 获取服务器IP地址
   * 优先从环境变量SERVER_TAG中读取，其次从配置文件server.tag中读取
   * 如果都不存在，启动时会抛出异常
   */
  public String getServerIp() {
    return serverTag;
  }
}