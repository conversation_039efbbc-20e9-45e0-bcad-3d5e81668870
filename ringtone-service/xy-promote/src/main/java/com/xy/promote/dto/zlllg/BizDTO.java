package com.xy.promote.dto.zlllg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * 业务节点实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class BizDTO {
    /**
     * 渠道编码
     * 业务接收方自定义（用于业务接收方区分订单渠道）
     */
    private String channelNo;
    
    /**
     * 产品编码
     * 业务接收方自定义（用于映射关联业务接收方内部产品）
     */
    private String productNo;
    
    /**
     * 手机号
     * 用于订购业务的手机号码
     */
    private String mobileNo;

    /**
     * 短信验证码
     * 对于需要短信办理的业务必填，对于三方支付等业务非必填
     */
    private String smsCode;
    
    /**
     * 用户来源
     */
    private SourceDTO source;

    /**
     * 扩展信息
     * 根据业务需要，额外交互的数据集合，内部数据不固定，业务受理方自定义，业务发起方不做处理
     * 在下单接口回调传参传递。举例场景：部分运营商在发送验证码环节会生成流水号，提交订单环节需要将该流水号传入
     * 可选
     */
    private Map extraInfo;

    /**
     * 业务接收方订单号
     */
    private String orderId;

    /**
     * 业务发起方订单号
     */
    private String outOrderId;

    /**
     * 订单状态码
     */
    private String orderStatus;

    /**
     * 消息
     */
    private String message;

    /**
     * 订单时间
     */
    private String orderingTime;

}
