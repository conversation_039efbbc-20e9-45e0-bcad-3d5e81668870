package com.xy.promote.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.xy.promote.dto.ProOrderDTO;
import com.xy.promote.dto.hzchl.HzchlCheckCodeReq;
import com.xy.promote.dto.hzchl.HzchlResponse;
import com.xy.promote.dto.hzchl.HzchlVerifyCodeReq;
import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import com.xy.promote.entity.ProInfoProduct;
import com.xy.promote.enums.HzchlAndZlllgResCode;
import com.xy.promote.enums.ZlllgOrderStatusCode;
import com.xy.promote.enums.ZlllgResponseCode;
import com.xy.promote.mapper.DliangInfoMapper;
import com.xy.promote.mapper.ProInfoProductMapper;
import com.xy.promote.service.ApiOrderService;
import com.xy.promote.service.HzchlService;
import com.xy.promote.service.ProOrderService;
import com.xy.promote.util.ApiExceptionUtil;
import com.xy.promote.util.ZlllgResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

/**
 * 杭州铖晗联API服务实现
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
@Slf4j
@Service
public class HzchlApiProviderServiceImpl extends BaseApiProviderServiceImpl {
	
	private final HzchlService hzchlService;
	
	public HzchlApiProviderServiceImpl(ApiOrderService apiOrderService,
	                                   ProInfoProductMapper proInfoProductMapper,
	                                   ProOrderService proOrderService,
	                                   DliangInfoMapper dliangInfoMapper,
	                                   HzchlService hzchlService) {
		super(apiOrderService, proInfoProductMapper, proOrderService, dliangInfoMapper);
		this.hzchlService = hzchlService;
	}
	
	/**
	 * 获取该服务提供商对应的pro标识
	 * 用于到量检测
	 */
	@Override
	protected String getProIdentifier() {
		return "hzchl";
	}
	
	/**
	 * 获取该服务提供商支持的产品编号
	 *
	 * @return 产品编号
	 */
	@Override
	public String getSupportedProductNo() {
		return "DA";
	}
	
	/**
	 * 发送短信验证码
	 *
	 * @param reqDTO 请求DTO
	 * @return 响应DTO
	 */
	@Override
	public ZlllgMessageDTO sendSms(ZlllgMessageDTO reqDTO) {
		// 进行省份到量检测和运营商检测
		checkDaoliang(reqDTO);
		
		// 查询并验证产品信息
		ProInfoProduct proInfoProduct = getProductInfo(reqDTO.getBiz().getProductNo());
		validateProductInfo(proInfoProduct, reqDTO);
		
		String productId = JSONUtil.parseObj(proInfoProduct.getParams()).getStr("productId");  // 获取产品ID
		String channelNo = JSONUtil.parseObj(proInfoProduct.getParams()).getStr("channelNo");  // 获取渠道号
		String price = JSONUtil.parseObj(proInfoProduct.getParams()).getStr("price");  // 获取价格
		
		ProOrderDTO proOrderDTO = ProOrderDTO.fromReqDTO(reqDTO, proInfoProduct.getPro());
		HzchlVerifyCodeReq hzchlVerifyCodeReq = HzchlVerifyCodeReq.fromReqDTO(reqDTO, productId, channelNo, price);
		HzchlResponse response = hzchlService.getVerifyCode(hzchlVerifyCodeReq, proOrderDTO);
		
		String status = response.getStatus();
		String linkId = Optional.ofNullable(response.getLinkid()).orElse(IdWorker.getTimeId());
		String respCode = HzchlAndZlllgResCode.getRespCodeByStatus(status);
		String respDesc = Optional.ofNullable(response.getMsg()).orElse(HzchlAndZlllgResCode.getRespDescByRespCode(respCode));
		
		// 构建众联流量果接口响应
		ZlllgMessageDTO res = ZlllgResponseUtil.buildZlllgResponse(reqDTO, respCode, respDesc,
				bizBuilder -> bizBuilder.extraInfo(MapUtil.builder().put("linkId", linkId).build()));
		
		// 更新DTO对象存到数据库
		reqDTO.getHead().setRespCode(status);
		reqDTO.getBiz().setOrderId(linkId);
		reqDTO.getBiz().setOrderStatus(respCode);
		
		// 保存信息到数据库
		saveSmsInfo(reqDTO, response);
		
		return res;
	}
	
	/**
	 * 业务订购办理
	 *
	 * @param reqDTO 请求DTO
	 * @return 响应DTO
	 */
	@Override
	public ZlllgMessageDTO orderSubmit(ZlllgMessageDTO reqDTO) {
		String linkId = Optional.ofNullable(reqDTO.getBiz().getExtraInfo().get("linkId")).map(Object::toString)
				.orElse(null);
		ApiExceptionUtil.throwIfEmpty(linkId, reqDTO, ZlllgResponseCode.PARAM_VALIDATION_FAILED);
		
		// 查询产品信息
		ProInfoProduct proInfoProduct = getProductInfo(reqDTO.getBiz().getProductNo());
		validateProductInfo(proInfoProduct, reqDTO);
		
		String productId = JSONUtil.parseObj(proInfoProduct.getParams()).getStr("productId");  // 获取产品ID
		
		// 创建验证码校验请求并发送请求到杭州铖晗联
		HzchlResponse response = hzchlService.checkVerifyCode(HzchlCheckCodeReq.fromReqDTO(reqDTO, productId, linkId));
		
		String status = response.getStatus();
		String respCode = Objects.equals(status, "1") ? ZlllgResponseCode.SUCCESS.getCode() : ZlllgResponseCode.OTHER_SYSTEM_FAILURE.getCode();
		String respDesc = Optional.ofNullable(response.getMsg()).orElse(HzchlAndZlllgResCode.getRespDescByRespCode(respCode));
		
		// 构建众联流量果接口响应
		ZlllgMessageDTO res = ZlllgResponseUtil.buildZlllgResponse(reqDTO, respCode, respDesc,
				bizBuilder -> bizBuilder
						.orderId(linkId)
						.outOrderId(reqDTO.getBiz().getOutOrderId())
						.orderStatus(ZlllgOrderStatusCode.PROCESSING.getCode()));
		
		// 更新DTO对象存到数据库
		reqDTO.getHead().setRespCode(status);
		reqDTO.getBiz().setOrderId(linkId);
		reqDTO.getBiz().setOrderStatus(respCode);
		
		// 更新信息到数据库
		updateVerifyCodeInfo(reqDTO, response);
		
		return res;
	}
}
