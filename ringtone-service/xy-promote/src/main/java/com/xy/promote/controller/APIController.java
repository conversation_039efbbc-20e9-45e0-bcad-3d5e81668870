package com.xy.promote.controller;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xy.base.core.annotation.DirectOutput;
import com.xy.promote.annotation.SignatureValidation;
import com.xy.promote.dto.zlllg.BizDTO;
import com.xy.promote.dto.zlllg.HeadDTO;
import com.xy.promote.dto.zlllg.SourceDTO;
import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import com.xy.promote.dto.zsgl.ZsglOrderSubmitReq;
import com.xy.promote.dto.zsgl.ZsglRes;
import com.xy.promote.entity.ApiOrder;
import com.xy.promote.enums.ZlllgResponseCode;
import com.xy.promote.exception.ApiException;
import com.xy.promote.service.ApiOrderService;
import com.xy.promote.service.ApiProviderFactory;
import com.xy.promote.service.ApiProviderService;
import com.xy.promote.dto.zsgl.ZsglSendSmsReq;
import com.xy.promote.util.ApiExceptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * 存量业务标准请求接口
 * 标准化接口，根据产品编号路由到不同的具体实现
 *
 * <AUTHOR>
 * @since 2025-04-24 14:32:29
 */
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class APIController {

	private final ApiProviderFactory apiProviderFactory;
	private final ApiOrderService apiOrderService;

	/**
	 * 发送短信验证码接口
	 */
	@PostMapping("/v1/sendSms")
	@SignatureValidation
	@DirectOutput
	public ZlllgMessageDTO sendSms(@RequestBody ZlllgMessageDTO reqDTO) {
		// log.info("发送短信验证码请求，产品编号：{}", productNo);
		try {
			String productNo = reqDTO.getBiz().getProductNo();
			// 根据产品编号前两位获取对应的服务提供商实现
			ApiProviderService providerService = apiProviderFactory.getProvider(productNo);

			// 委托给具体实现处理
			return providerService.sendSms(reqDTO);
		} catch (Exception e) {
			// 转换为业务异常抛出，交由全局异常处理器处理
			if (e instanceof ApiException) {
				throw e;
			}
			log.error("发送短信验证码异常: ", e);
			throw new ApiException(reqDTO, ZlllgResponseCode.OTHER_SYSTEM_FAILURE);
		}
	}

	/**
	 * 业务订购办理接口
	 */
	@PostMapping("/v1/orderSubmit")
	@SignatureValidation
	@DirectOutput
	public ZlllgMessageDTO orderSubmit(@RequestBody ZlllgMessageDTO reqDTO) {
		// log.info("业务订购办理请求，产品编号：{}", productNo);
		try {
			String productNo = reqDTO.getBiz().getProductNo();
			// 根据产品编号获取对应的服务提供商实现
			ApiProviderService providerService = apiProviderFactory.getProvider(productNo);

			// 委托给具体实现处理
			return providerService.orderSubmit(reqDTO);
		} catch (Exception e) {
			// 转换为业务异常抛出，交由全局异常处理器处理
			if (e instanceof ApiException) {
				throw e;
			}
			log.error("业务订购办理异常: ", e);
			throw new ApiException(reqDTO, ZlllgResponseCode.OTHER_SYSTEM_FAILURE);
		}
	}

	/**
	 * 订单状态查询接口
	 */
	@PostMapping("/v1/orderQuery")
	@SignatureValidation
	@DirectOutput
	public ZlllgMessageDTO orderQuery(@RequestBody ZlllgMessageDTO reqDTO) {
		try {
			String productNo = reqDTO.getBiz().getProductNo();
			if (StrUtil.isEmpty(productNo)) {
				LambdaQueryWrapper<ApiOrder> queryWrapper = new LambdaQueryWrapper<>();
				queryWrapper.eq(ApiOrder::getOutOrderId, reqDTO.getBiz().getOutOrderId());
				ApiOrder apiOrder = apiOrderService.getOne(queryWrapper);
				productNo = apiOrder.getProductNo();
			}
			// 根据产品编号获取对应的服务提供商实现
			ApiProviderService providerService = apiProviderFactory.getProvider(productNo);

			// 委托给具体实现处理
			return providerService.orderQuery(reqDTO);
		} catch (Exception e) {
			// 转换为业务异常抛出，交由全局异常处理器处理
			if (e instanceof ApiException) {
				throw e;
			}
			log.error("订单状态查询异常: ", e);
			throw new ApiException(reqDTO, ZlllgResponseCode.OTHER_SYSTEM_FAILURE);
		}
	}

	/**
	 * 前置校验接口
	 */
	@PostMapping("/v1/recommend")
	@SignatureValidation
	@DirectOutput
	public ZlllgMessageDTO recommend(@RequestBody ZlllgMessageDTO reqDTO) {
		// log.info("前置校验请求，产品编号：{}", productNo);
		try {
			String productNo = reqDTO.getBiz().getProductNo();
			// 根据产品编号获取对应的服务提供商实现
			ApiProviderService providerService = apiProviderFactory.getProvider(productNo);

			// 委托给具体实现处理
			return providerService.recommend(reqDTO);
		} catch (Exception e) {
			// 转换为业务异常抛出，交由全局异常处理器处理
			if (e instanceof ApiException) {
				throw e;
			}
			log.error("前置校验异常: ", e);
			throw new ApiException(reqDTO, ZlllgResponseCode.OTHER_SYSTEM_FAILURE);
		}
	}

	/**
	 * 简单获取短信验证码接口
	 */
	@GetMapping("/s1/sendSms")
	@DirectOutput
	public ZsglRes sendSms(ZsglSendSmsReq reqDTO) {
		try {
			ZlllgMessageDTO llgReqDTO = reqDTO.toZlllgMessageDTO();
			String productNo = llgReqDTO.getBiz().getProductNo();
			ApiProviderService providerService = apiProviderFactory.getProvider(productNo);
			ZlllgMessageDTO zlllgMessageDTO = providerService.sendSms(llgReqDTO);
			return ZsglRes.builder().build().fromZlllgMessageDTO(zlllgMessageDTO);
		} catch (ApiException e) {
			log.error("s1/sendSms API异常: {}", e.getMessage());
			return ZsglRes.error(e.getRespCode(), e.getRespDesc());
		} catch (Exception e) {
			log.error("s1/sendSms 未知异常: ", e);
			return ZsglRes.error(ZlllgResponseCode.APP_ERROR.getCode(), ZlllgResponseCode.APP_ERROR.getDescription());
		}
	}

	/**
	 * 简单业务订购办理接口
	 */
	@GetMapping("/s1/orderSubmit")
	@DirectOutput
	public ZsglRes orderSubmit(ZsglOrderSubmitReq reqDTO) {
		try {
			String linkId = reqDTO.getLinkId();
			LambdaQueryWrapper<ApiOrder> queryWrapper = new LambdaQueryWrapper<ApiOrder>()
					.eq(ApiOrder::getOrderId, linkId);
			ApiOrder apiOrder = apiOrderService.getBaseMapper().selectOne(queryWrapper);
			ApiExceptionUtil.throwIfNull(apiOrder, null, ZlllgResponseCode.ORDER_NOT_EXIST);
			ZlllgMessageDTO llgReqDTO = ZlllgMessageDTO.builder()
					.head(
							HeadDTO.builder()
									.appId(Optional.ofNullable(apiOrder.getAppId()).orElse(""))
									.build())
					.biz(
							BizDTO.builder()
									.channelNo(apiOrder.getChannelNo())
									.productNo(apiOrder.getProductNo())
									.mobileNo(apiOrder.getMobileNo())
									.smsCode(reqDTO.getSmsCode())
									.outOrderId(linkId)
											.source(SourceDTO.builder()
															.appName(Optional.ofNullable(apiOrder.getAppName()).orElse(""))
															.appPackage(Optional.ofNullable(apiOrder.getAppPackage()).orElse(""))
															.build())
									.extraInfo(MapUtil.builder().put("linkId", linkId).build())
							.build())
					.build();
			String productNo = llgReqDTO.getBiz().getProductNo();
			ApiProviderService providerService = apiProviderFactory.getProvider(productNo);
			ZlllgMessageDTO zlllgMessageDTO = providerService.orderSubmit(llgReqDTO);
			return ZsglRes.builder().build().fromZlllgMessageDTOSummit(zlllgMessageDTO);
		} catch (ApiException e) {
			log.error("s1/orderSubmit API异常: {}", e.getMessage());
			return ZsglRes.error(e.getRespCode(), e.getRespDesc());
		} catch (Exception e) {
			log.error("s1/orderSubmit 未知异常: ", e);
			return ZsglRes.error(ZlllgResponseCode.APP_ERROR.getCode(), ZlllgResponseCode.APP_ERROR.getDescription());
		}
	}

}