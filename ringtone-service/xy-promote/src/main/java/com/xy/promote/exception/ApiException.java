package com.xy.promote.exception;

import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import com.xy.promote.enums.ZlllgResponseCode;
import lombok.Getter;

/**
 * API接口异常类
 */
@Getter
public class ApiException extends RuntimeException {

  private final ZlllgMessageDTO reqDTO;
  private final String respCode;
  private final String respDesc;

  public ApiException(ZlllgMessageDTO reqDTO, ZlllgResponseCode responseCode) {
    super(responseCode.getDescription());
	  this.reqDTO = reqDTO;
	  this.respCode = responseCode.getCode();
    this.respDesc = responseCode.getDescription();
  }

  public ApiException(ZlllgMessageDTO reqDTO, String respCode, String respDesc) {
    super(respDesc);
    this.reqDTO = reqDTO;
    this.respCode = respCode;
    this.respDesc = respDesc;
  }

  public ApiException(ZlllgMessageDTO reqDTO, String respCode, String respDesc, Throwable cause) {
    super(respDesc, cause);
    this.reqDTO = reqDTO;
    this.respCode = respCode;
    this.respDesc = respDesc;
  }
}