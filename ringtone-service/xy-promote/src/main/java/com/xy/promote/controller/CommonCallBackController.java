package com.xy.promote.controller;

import com.xy.base.core.annotation.DirectOutput;
import com.xy.promote.dto.CallBackDTO;
import com.xy.promote.entity.ProOrder;
import com.xy.promote.service.CommonCallBackService;
import com.xy.promote.service.ProOrderService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

import static cn.hutool.core.text.CharSequenceUtil.nullToEmpty;

/**
 * <AUTHOR>
 * @since 2024/11/5 15:43
 */
@Slf4j
@RestController
@RequestMapping("/callback")
@Validated
@AllArgsConstructor
public class CommonCallBackController {

    private final CommonCallBackService callBackService;
    public static ProOrderService proOrderService;


    /**
     * 馨宁
     */
    @DirectOutput
    @GetMapping("/xnahlt")
    public String xnahlt(XnCallback xn, HttpServletRequest request) {
        log.info("收到回调：" + request.getRequestURL().toString());
        CallBackDTO dto = new CallBackDTO(xn.getPhone(), xn.getCpparam(), xn.getLinkid(), xn.getStatus());
        return callBackService.dealCallBack(dto, request, "/callback/xnahlt");

    }

    /**
     * 杭州铖晗联
     */
    @DirectOutput
    @GetMapping("/hzchl")
    public String hzchl(ZchCallback xn, HttpServletRequest request) {
        log.info("收到回调：" + request.getRequestURL().toString());
        CallBackDTO dto = new CallBackDTO(xn.getPhone(), xn.getCpparam(), xn.getLinkid(), xn.getStatus());
        return callBackService.dealCallBack(dto, request, "/callback/hzchl");

    }

    /**
     * 乐只君子
     */
    @DirectOutput
    @GetMapping("/lzjz")
    public String lzjz(LzjzCallback call, HttpServletRequest request) {
        log.info("收到回调：" + request.getRequestURL().toString());
        CallBackDTO dto = new CallBackDTO(call.getMobile(), call.getExtdata(), call.getOrderNo(), call.getStatus());
        return callBackService.dealCallBack(dto, request, "/callback/lzjz");
    }

    /**
     * 爱奇
     */
    @DirectOutput
    @GetMapping("/aq")
    public String aq(AqCallback call, HttpServletRequest request) {
        log.info("收到回调：" + request.getRequestURL().toString());
        if (call.getPayStatus() != null && call.getPayStatus().equals(2)) {
            ProOrder proOrder = proOrderService.lambdaQuery().eq(ProOrder::getOrderId, call.getBusOrderId()).one();
            if (proOrder != null) {
                CallBackDTO dto = new CallBackDTO(proOrder.getPhone(), null, call.getBusOrderId(), "1");
                return callBackService.dealCallBack(dto, request, "/callback/aq");
            }
        }
        return "false";
    }

    /**
     * 浙江有田
     * */
    @DirectOutput
    @GetMapping("/zjyt")
    public String zjyt(zjytCallback call, HttpServletRequest request) {
        log.info("收到回调：" + request.getRequestURL().toString());

        String status = "0".equals(call.getOrderStatus()) ? "1" : "0";
        CallBackDTO dto = new CallBackDTO(call.getPhone(), null, call.getLinkId(), status);

        callBackService.dealCallBack(dto, request, "/callback/zjyt");
        return "ok";
    }

    /**
     * 北京众智
     * */
    @DirectOutput
    @PostMapping("/bjzz")
    public String bjzz(BjzzCallback call, HttpServletRequest request) {
        log.info("收到回调：" + request.getRequestURL().toString());
        log.info("回调参数：" + call.params());

        String status = "success".equals(call.getStatus()) ? "1" : "0";
        CallBackDTO dto = new CallBackDTO(call.getMobile(), call.getExtraData(), call.getOrderId(), status, call.params());

        callBackService.dealCallBack(dto, request, "/callback/bjzz");
        return "ok";
    }

    /**
     * 北京众智
     * */
    @DirectOutput
    @PostMapping("/bjzz_new")
    public String bjzz_new(@RequestParam Map<String, String> params, HttpServletRequest request) {
        log.info("收到北京众智回调：" + request.getRequestURL().toString());

        String status = "success".equals(params.get("status")) ? "1" : "0";
//        CallBackDTO dto = new CallBackDTO(call.getMobile(), call.getExtraData(), call.getOrderId(), status);
        CallBackDTO dto = new CallBackDTO(params.get("mobile"), params.get("extraData"), params.get("orderId"), status);

        callBackService.dealCallBack(dto, request, "/callback/bjzz");
        return "ok";
    }
    
    /**
     * 长沙云蓑
     */
    @DirectOutput
    @GetMapping("/csys")
    public String csys(@RequestParam Map<String, String> params, HttpServletRequest request) {
	    log.info("收到长沙云蓑回调：{}{}", request.getRequestURL().toString(), params);
        
        String mobile = params.get("mobile");
        String cpparam = params.get("extra");
        String orderNo = params.get("orderNo");
        String status = "1".equals(params.get("status")) ? "1" : "0";
        
        //CallBackDTO dto = new CallBackDTO(params.getMobile(), params.getExtra(), params.getOrderNo(), params.getStatus());
        CallBackDTO dto = new CallBackDTO(mobile,cpparam,orderNo,status);
        callBackService.dealCallBack(dto, request, "/callback/csys");
        
        return "OK";
    }
    
    @Data
    @ToString
    static class CsysCallback {
        private String mobile;
        private String extra;
        private String orderNo;
        private String status;
    }
    
    @Data
    static class XnCallback {
        private String status;
        private String msg;
        private String price;
        private String cpparam;
        private String linkid;
        private String channelNo;
        private String phone;
    }

    @Data
    static class ZchCallback {
        private String status;
        private String msg;
        private String price;
        private String cpparam;
        private String linkid;
        private String channelNo;
        private String phone;
    }

    @Data
    static class LzjzCallback {
        private String mobile;
        private String extdata;
        private String orderNo;
        private String amount;
        private String status;
    }

    @Data
    static class AqCallback {
        private String busOrderId;
        private Integer payStatus;
        private Long timeStamp;
        private String msg;
    }

    @Data
    static class zjytCallback {
        private String linkId;
        private String orderTime;
        private String orderStatus;
        private String phone;
        private String resultMsg;
    }

    @Data
    @ToString
    static class BjzzCallback {
        private String orderId;
        private String launchId;
        private String status;
        private String createTime;
        private String extraData;
        private String mobile;
        private String code;
        private String message;

        public String params() {
            // 参数拼接处理
            StringBuilder paramStr = new StringBuilder();
            try {
                paramStr.append("orderId=").append(nullToEmpty(orderId));
                paramStr.append("&launchId=").append(nullToEmpty(launchId));
                paramStr.append("&createTime=").append(nullToEmpty(createTime));
                paramStr.append("&extraData=").append(nullToEmpty(extraData));
                paramStr.append("&mobile=").append(nullToEmpty(mobile));
                paramStr.append("&status=").append(nullToEmpty(status));
                paramStr.append("&code=").append(nullToEmpty(code));
                paramStr.append("&message=").append(nullToEmpty(message));
            } catch (Exception e) {
                e.printStackTrace();
                return this.toString();
            }
            return paramStr.toString();
        }
    }
}
