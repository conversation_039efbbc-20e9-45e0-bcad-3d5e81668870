package com.xy.promote.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xy.promote.dto.CallBackDTO;
import com.xy.promote.entity.LogApi;
import com.xy.promote.entity.ProOrder;
import com.xy.promote.service.CommonCallBackService;
import com.xy.promote.service.LogApiService;
import com.xy.promote.service.ProOrderService;
import com.xy.promote.util.AdReportUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class CommonCallbackServiceImpl implements CommonCallBackService {

    private final LogApiService logApiService;
    private final ProOrderService proOrderService;
    private final ObjectMapper mapper;

    @Override
    public String dealCallBack(CallBackDTO dto, HttpServletRequest request, String url) {
        try {
            // 业务逻辑处理
            if ("1".equals(dto.getStatus())) {

                ProOrder channelReport = proOrderService.lambdaQuery().eq(ProOrder::getPhone, dto.getPhone())
                        .eq(ProOrder::getOrderId, dto.getOrderNo())
                        .eq(ProOrder::getReportStatus, 0)
                        .orderByDesc(ProOrder::getCreateTime)
                        .last("limit 1").one();

                log.info("get report record {}", channelReport);
                if (null != channelReport) {
                    if (channelReport.getReportStatus() == 0) {
                        boolean huichuan = AdReportUtil.doSuccessReport(channelReport.getCallback(), channelReport.getPro(), channelReport.getPlatform(), channelReport.getUrl());
                        channelReport.setReportStatus(1);
                        if (huichuan) {
                            channelReport.setHuichuanStatus(1);
                        }
                        channelReport.setUpdateTime(new Date());
                        proOrderService.updateById(channelReport);
                        log.info("report successful: {}", channelReport);
                    } else {
                        log.info("report is nodelay, no more report is need");
                    }
                } else {
                    log.warn("未获取到回传记录：{}", request);
                }

            }
            // 获取请求者的 IP 地址
//            String ipAddress = ServletUtil.getClientIP(httpRequest);
//            log.info("jsgy callback method callback is invoked, request: " + request);
//            log.info("Requester IP Address: " + ipAddress);
//            jsgyService.save(request);

            LogApi log = new LogApi();
            log.setMethod("GET");
            log.setPhone(dto.getPhone());
            log.setTiming(0);
            log.setId(IdUtil.getSnowflake().nextId());
            log.setCreateTime(LocalDateTime.now());
            log.setIp((ServletUtil.getClientIP(request)));
            // 获取 request 中的 QueryString
            String queryString = request.getQueryString();
            // 对 QueryString 中的中文进行 url 解码，当前中文示例：resultCode=1&resultMsg=%E6%B5%99%E6%B1%9F%E6%9C%89%E7%94%B0%E7%94%A8%E6%88%B7%E5%8F%91%E5%B1%95%E6%95%B0%E9%87%8F%E5%B7%B2%E8%BE%BE%E5%BD%93%E6%97%A5%E4%B8%8A%E9%99%90
            if(StringUtils.hasText(queryString)) {
                queryString = java.net.URLDecoder.decode(queryString, "UTF-8");
                log.setRequest(queryString);
            }
            else {
                log.setRequest(dto.getParams());
            }
            log.setType(1);
            log.setUrl(url);

            logApiService.save(log);

            return "success";
        } catch (Exception e) {
            // 处理异常情况
            log.error("捕获到异常: ", e);
            return "failed";
        }
    }
}




