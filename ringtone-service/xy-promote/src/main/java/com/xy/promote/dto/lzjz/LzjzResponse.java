package com.xy.promote.dto.lzjz;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 乐只君子API响应实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class LzjzResponse {
  /**
   * 响应状态码
   */
  private Integer code;
  /**
   * 订单ID
   */
  private String orderid;
  /**
   * 错误信息
   */
  private String errmsg;
}