package com.xy.promote.controller;

import cn.hutool.extra.servlet.ServletUtil;
import com.xy.promote.dto.ProOrderDTO;
import com.xy.promote.dto.hzchl.HzchlCheckCodeReq;
import com.xy.promote.dto.hzchl.HzchlResponse;
import com.xy.promote.dto.hzchl.HzchlVerifyCodeReq;
import com.xy.promote.service.HzchlService;
import com.xy.promote.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;

/**
 * 杭州铖晗联
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
@Slf4j
@RestController
@RequestMapping("/hzchl")
@RequiredArgsConstructor
public class HzchlController {

    private final HzchlService hzchlService;


    /**
     * 获取验证码
     */
    @PostMapping("/getVerifyCode")
    public HzchlResponse getVerifyCode(@RequestParam HashMap<String, String> params, HttpServletRequest request) {
        // 请求参数
        HzchlVerifyCodeReq req = HzchlVerifyCodeReq.builder()
                .productId(params.get("productId"))
                .channelNo(params.get("channelNo"))
                .price(params.get("price"))
                .phone(params.get("phone"))
                .cpparam("juliang")
                .ip(ServletUtil.getClientIP(request))
                .appPackage(StringUtil.extractPackageName(request.getHeader("user-agent")))
                .appName("巨量")
                .userAgent(request.getHeader("user-agent"))
                .build();

        // 订单参数
        ProOrderDTO proDTO = ProOrderDTO.builder()
                .ua(request.getHeader("user-agent"))
                .pro(params.get("pro"))
                .build();

        return hzchlService.getVerifyCode(req, proDTO);
    }

    /**
     * 验证验证码
     */
    @PostMapping("/checkVerifyCode")
    public HzchlResponse checkVerifyCode(@RequestParam HashMap<String, String> params) {
        HzchlCheckCodeReq req = HzchlCheckCodeReq.builder()
                .productId(params.get("productId"))
                .linkid(params.get("linkid"))
                .vcode(params.get("vcode"))
                .build();

        return hzchlService.checkVerifyCode(req);
    }

}
