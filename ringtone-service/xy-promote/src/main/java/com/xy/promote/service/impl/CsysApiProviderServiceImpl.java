package com.xy.promote.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.xy.promote.dto.ProOrderDTO;
import com.xy.promote.dto.csys.CsysCheckCodeReq;
import com.xy.promote.dto.csys.CsysResponse;
import com.xy.promote.dto.csys.CsysVerifyCodeReq;
import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import com.xy.promote.entity.ProInfoProduct;
import com.xy.promote.enums.CsysAndZlllgResCode;
import com.xy.promote.enums.ZlllgOrderStatusCode;
import com.xy.promote.enums.ZlllgResponseCode;
import com.xy.promote.mapper.DliangInfoMapper;
import com.xy.promote.mapper.ProInfoProductMapper;
import com.xy.promote.service.ApiOrderService;
import com.xy.promote.service.CsysService;
import com.xy.promote.service.ProOrderService;
import com.xy.promote.util.ApiExceptionUtil;
import com.xy.promote.util.ZlllgResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

/**
 * 长沙云蓑API服务实现
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
@Slf4j
@Service
public class CsysApiProviderServiceImpl extends BaseApiProviderServiceImpl {

  private final CsysService csysService;

  public CsysApiProviderServiceImpl(ApiOrderService apiOrderService,
      ProInfoProductMapper proInfoProductMapper,
      ProOrderService proOrderService,
      DliangInfoMapper dliangInfoMapper,
      CsysService csysService) {
    super(apiOrderService, proInfoProductMapper, proOrderService, dliangInfoMapper);
    this.csysService = csysService;
  }

  /**
   * 获取该服务提供商对应的pro标识
   * 用于到量检测
   */
  @Override
  protected String getProIdentifier() {
    return "csys";
  }

  /**
   * 获取该服务提供商支持的产品编号
   *
   * @return 产品编号
   */
  @Override
  public String getSupportedProductNo() {
    return "KA";
  }

  /**
   * 发送短信验证码
   *
   * @param reqDTO 请求DTO
   * @return 响应DTO
   */
  @Override
  public ZlllgMessageDTO sendSms(ZlllgMessageDTO reqDTO) {
    // 进行省份到量检测和运营商检测
    checkDaoliang(reqDTO);

    // 查询并验证产品信息
    ProInfoProduct proInfoProduct = getProductInfo(reqDTO.getBiz().getProductNo());
    validateProductInfo(proInfoProduct, reqDTO);

    String mchid = JSONUtil.parseObj(proInfoProduct.getParams()).getStr("mchid"); // 获取商户ID
    String cid = JSONUtil.parseObj(proInfoProduct.getParams()).getStr("cid"); // 获取业务ID

    ProOrderDTO proOrderDTO = ProOrderDTO.fromReqDTO(reqDTO, proInfoProduct.getPro());
    CsysVerifyCodeReq csysVerifyCodeReq = CsysVerifyCodeReq.fromReqDTO(reqDTO, mchid, cid);
    CsysResponse response = csysService.getVerifyCode(csysVerifyCodeReq, proOrderDTO);

    Integer code = response.getCode();
    String orderNo = Optional.ofNullable(response.getOrderNo()).orElse(IdWorker.getTimeId());
    String respCode = CsysAndZlllgResCode.getRespCodeByCode(code);
    String respDesc = Optional.ofNullable(response.getMsg())
        .orElse(CsysAndZlllgResCode.getRespDescByRespCode(respCode));

    // 构建众联流量果接口响应
    ZlllgMessageDTO res = ZlllgResponseUtil.buildZlllgResponse(reqDTO, respCode, respDesc,
        bizBuilder -> bizBuilder.extraInfo(MapUtil.builder().put("linkId", orderNo).build()));

    // 更新DTO对象存到数据库
    reqDTO.getHead().setRespCode(code.toString());
    reqDTO.getBiz().setOrderId(orderNo);
    reqDTO.getBiz().setOrderStatus(respCode);

    // 保存信息到数据库
    saveSmsInfo(reqDTO, response);

    return res;
  }

  /**
   * 业务订购办理
   *
   * @param reqDTO 请求DTO
   * @return 响应DTO
   */
  @Override
  public ZlllgMessageDTO orderSubmit(ZlllgMessageDTO reqDTO) {
    String orderNo = Optional.ofNullable(reqDTO.getBiz().getExtraInfo().get("linkId")).map(Object::toString)
        .orElse(null);
    ApiExceptionUtil.throwIfEmpty(orderNo, reqDTO, ZlllgResponseCode.PARAM_VALIDATION_FAILED);

    // 查询产品信息
    ProInfoProduct proInfoProduct = getProductInfo(reqDTO.getBiz().getProductNo());
    validateProductInfo(proInfoProduct, reqDTO);

    // 创建验证码校验请求并发送请求到长沙云蓑
    CsysResponse response = csysService.checkVerifyCode(CsysCheckCodeReq.fromReqDTO(reqDTO, orderNo));

    Integer code = response.getCode();
    String respCode = Objects.equals(code, 0) ? ZlllgResponseCode.SUCCESS.getCode()
        : ZlllgResponseCode.OTHER_SYSTEM_FAILURE.getCode();
    String respDesc = Optional.ofNullable(response.getMsg())
        .orElse(CsysAndZlllgResCode.getRespDescByRespCode(respCode));

    // 构建众联流量果接口响应
    ZlllgMessageDTO res = ZlllgResponseUtil.buildZlllgResponse(reqDTO, respCode, respDesc,
        bizBuilder -> bizBuilder
            .orderId(orderNo)
            .outOrderId(reqDTO.getBiz().getOutOrderId())
            .orderStatus(ZlllgOrderStatusCode.PROCESSING.getCode()));

    // 更新DTO对象存到数据库
    reqDTO.getHead().setRespCode(code.toString());
    reqDTO.getBiz().setOrderId(orderNo);
    reqDTO.getBiz().setOrderStatus(respCode);

    // 更新信息到数据库
    updateVerifyCodeInfo(reqDTO, response);

    return res;
  }
}