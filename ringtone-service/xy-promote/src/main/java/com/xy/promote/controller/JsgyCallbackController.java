package com.xy.promote.controller;


import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xy.base.core.annotation.DirectOutput;
import com.xy.promote.entity.LogApi;
import com.xy.promote.entity.OrderJsgy;
import com.xy.promote.entity.ProOrder;
import com.xy.promote.service.LogApiService;
import com.xy.promote.service.OrderJsgyService;
import com.xy.promote.service.ProOrderService;
import com.xy.promote.util.AdReportUtil;
import com.xy.promote.vo.GengChuangCallbackRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;


@Slf4j
@RestController
@RequestMapping("/pro/jsgy")
@Validated
@AllArgsConstructor
public class JsgyCallbackController {

    private final LogApiService logApiService;
    private final ProOrderService proOrderService;
    private final OrderJsgyService jsgyService;
    private final ObjectMapper mapper = new ObjectMapper();

    /**
     * 江苏冠优回调
     *
     * @return
     */
    private static final String IP_AUTH = "*************"; // IP鉴权地址，如果需要的话
    private static final String DAILY_LIMIT_REACHED_MESSAGE_1 = "没有找到合适的代码";
    private static final String DAILY_LIMIT_REACHED_MESSAGE_2 = "该省份今日已到量";

    @DirectOutput
    @GetMapping("/callback")
    public String syncPaymentResult(
            HttpServletRequest httpRequest,
            @RequestParam String status,
            @RequestParam String msg,
            @RequestParam String price,
            @RequestParam String cpparam,
            @RequestParam String linkid,
            @RequestParam String channelNo,
            @RequestParam String phone,
            @RequestParam(required = false) String spnumber,
            @RequestParam(required = false) String momsg,
            @RequestParam(required = false) String flag,
            @RequestParam(required = false) String mobile) {

        try {
            OrderJsgy request = new OrderJsgy(msg, price, status, channelNo, linkid, phone, cpparam);
            // 业务逻辑处理
            if ("1".equals(status)) {
                // 计费成功
                processNewSuccess(request);
            }
            // 获取请求者的 IP 地址
            String ipAddress = ServletUtil.getClientIP(httpRequest);
            log.info("jsgy callback method callback is invoked, request: " + request);
            log.info("Requester IP Address: " + ipAddress);
            jsgyService.save(request);

            LogApi log = new LogApi();
            log.setMethod("GET");
            log.setTiming(0);
            log.setId(IdUtil.getSnowflake().nextId());
            log.setCreateTime(LocalDateTime.now());
            log.setIp((ServletUtil.getClientIP(httpRequest)));
            log.setRequest(httpRequest.getQueryString());
            log.setType(1);
            log.setUrl("/pro/jsgy/callback");

            logApiService.save(log);

            return "success";
        } catch (Exception e) {
            // 处理异常情况
            e.printStackTrace();
            log.error("捕获到异常: ", e);
            return "failed";
        }
    }

    // 处理回传
    private void doHuichuan(OrderJsgy request, boolean success) {
        LambdaQueryWrapper<ProOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProOrder::getPhone, request.getPhone())
                .eq(ProOrder::getOrderId, request.getLinkid())
                .eq(ProOrder::getReportStatus, 0)
                .last("ORDER BY create_time desc LIMIT 1"); // 限制只查询一条记录
        ProOrder channelReport = proOrderService.getOne(queryWrapper);
        log.info("get report record {}", channelReport);
        if (null != channelReport) {
            if (channelReport.getReportStatus() == 0) {
                // 新增成功时，回传并更新状态
                if (success) {
                    boolean huichuan = AdReportUtil.doSuccessReport(channelReport.getCallback(), channelReport.getPro(), channelReport.getPlatform(), channelReport.getUrl());
                    channelReport.setReportStatus(1);
                    if (huichuan) {
                        channelReport.setHuichuanStatus(1);
                    }
                    channelReport.setUpdateTime(new Date());
                    proOrderService.updateById(channelReport);
                }
                // 新增失败时，仅回传
                else {
                }
                log.info("report successful: {}", channelReport);
            } else {
                log.info("report is nodelay, no more report is need");
            }
        } else {
            log.warn("未获取到回传记录：{}", request);
        }
    }

    /**
     * 处理新增且成功的用户
     */
    private void processNewSuccess(OrderJsgy request) {
        // 处理回传
        doHuichuan(request, true);
    }

}
