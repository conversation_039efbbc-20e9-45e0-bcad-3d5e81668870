package com.xy.promote.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xy.promote.dto.ProOrderDTO;
import com.xy.promote.dto.jsgy.JsgyCheckCodeReq;
import com.xy.promote.dto.jsgy.JsgyResponse;
import com.xy.promote.dto.jsgy.JsgyVerifyCodeReq;
import com.xy.promote.entity.ConfigHuichuan;
import com.xy.promote.service.JsgyService;
import com.xy.promote.util.PromoteUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

/**
 * 江苏冠优服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class JsgyServiceImpl implements JsgyService{
	private final ObjectMapper mapper;
	private static final String GET_VERIFY_CODE_API = "http://sp.jsguanyou.com/wxxy/v1first.do";
	private static final String CHECK_VERIFY_CODE_API = "http://sp.jsguanyou.com/wxxy/twoty.do";
	/**
	 * 调用江苏冠优接口 - 获取验证码
	 * @param params 请求参数
	 * @param orderParams 订单参数
	 * @return 响应结果
	 */
	@Override
	public JsgyResponse getVerifyCode(JsgyVerifyCodeReq params, ProOrderDTO orderParams) {
		ConfigHuichuan config = PromoteUtils.getConfig(params.getChannelNo(), orderParams.getSource());
		params.setPlatform(getAppName(config == null ? "" : config.getPlatform()));  // 获取配置的推广平台（用户向江苏冠优提交的请求中包含渠道号和来源）)
		String url = HttpUtil.urlWithForm(GET_VERIFY_CODE_API, BeanUtil.beanToMap(params), StandardCharsets.UTF_8, true);
		return getResponse(url, orderParams, true);
	}

	/**
	 * 调用江苏冠优接口 - 校验验证码
	 * @param params 验证码参数
	 * @return 响应结果
	 */
	@Override
	public JsgyResponse checkVerifyCode(JsgyCheckCodeReq params) {
		String url = HttpUtil.urlWithForm(CHECK_VERIFY_CODE_API, BeanUtil.beanToMap(params), StandardCharsets.UTF_8, true);
		return getResponse(url, null, false);
	}
	
	/**
	 * 统一请求接口方法
	 * @param url 请求的接口地址
	 * @param orderParams 订单参数
	 * @param record 是否记录订单（只有请求验证码成功，并且次参数为true才进行记录）
	 * @return 响应结果
	 */
	private JsgyResponse getResponse(String url, ProOrderDTO orderParams, boolean record) {
		log.info("准备请求：" + url);
		long start = System.currentTimeMillis();
		try (HttpResponse response = HttpUtil.createGet(url)
				.contentType("application/json")
				.execute()) {
			
			String responseStr = response.body();
			log.info("interfaceAction {},{}", url, responseStr);
			JsgyResponse res = mapper.readValue(responseStr, JsgyResponse.class);
			// 记录API调用日志
			PromoteUtils.insertLogApi(url,url, responseStr, "GET", (int) (System.currentTimeMillis() - start));
			// 如果状态为1，表示请求成功
			if ("1".equals(res.getStatus())) {
				if (record) {
					orderParams.setOrderId(res.getLinkid());
					PromoteUtils.insertProOrder(orderParams);
				}
			}
			return res;
		} catch (JsonProcessingException e) {
			log.error("JSON处理异常: ", e);
			throw new RuntimeException(e);
		}
	}

	/**
	 * 获取推广平台名称
	 * @param platForm 推广平台
	 * @return 推广平台名称
	 */
	private String getAppName(String platForm) {
		switch (platForm) {
			case "ks":
				return "快手";
			case "gdt":
				return "广点通";
			case "douyin":
			default:
				return "巨量";
		}
	}

}
