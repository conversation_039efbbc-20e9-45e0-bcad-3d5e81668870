package com.xy.promote.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.base.core.util.IdUtils;
import com.xy.promote.entity.LogApi;
import com.xy.promote.service.LogApiService;
import com.xy.promote.mapper.LogApiMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
* <AUTHOR>
* @description 针对表【log_api】的数据库操作Service实现
* @createDate 2024-09-13 17:34:01
*/
@Service
public class LogApiServiceImpl extends ServiceImpl<LogApiMapper, LogApi>implements LogApiService{
	
	@Override
	public LogApi insertLogApi(String url, String req, String res, String method, Integer timing) {
		LogApi log = new LogApi();
		log.setId(IdUtils.nextId());
		log.setCreateTime(LocalDateTime.now());
		log.setUrl(url);
		log.setType(0);
		log.setTiming(timing);
		log.setMethod(method);
		log.setResponse(res);
		log.setRequest(req);
		this.save(log);
		return log;
	}
}




