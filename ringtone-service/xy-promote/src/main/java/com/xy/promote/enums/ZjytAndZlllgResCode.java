package com.xy.promote.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单状态枚举
 * 表示订单处理结果的状态码和对应消息，同时映射到对应的ResponseCode
 */
@Getter
@AllArgsConstructor
public enum ZjytAndZlllgResCode {

    SUCCESS(200, "成功", "0000", "短信发送成功"),
    VERIFICATION_CODE_ERROR(40001, "验证码错误", "E001", "验证码错误"),
    NO_VERIFICATION_CODE_REQUESTED(109, "该手机号还未请求验证码", "E002", "手机号未请求验证码"),
    VERIFICATION_CODE_FORMAT_ERROR(40002, "验证码格式错误", "E002", "验证码格式不正确"),
    PROVINCE_LIMIT_REACHED(114, "省份到量", "E010", "该省份达到今日限量，明天可继续办理"),
    PRODUCT_LIMIT_REACHED(104, "当前产品已达投放上限", "E011", "该产品达到今日限量，明天可继续办理"),
    BLACKLISTED_NUMBER(103, "黑名单号码暂不支持订购", "E003", "黑名单号码无法处理"),
    PRODUCT_OFFLINE(105, "投放产品已下线", "E012", "产品已下架，请暂停推广"),
    CHANNEL_OFFLINE(106, "当前渠道已下线（联系商务处理）", "E004", "渠道不存在或已关闭"),
    LINK_OFFLINE(111, "当前投放链接已下线", "E004", "链接已失效"),
    LINK_NOT_FOUND(101, "当前投放链接已下线", "E004", "链接已失效"),
    OUT_OF_SERVICE_PERIOD(115, "非投放时段", "E005", "非服务时段，请稍后再试"),
    OTHER_FAILURE(-1, "其他失败", "E101", "其他失败（运营商或其他系统返回失败）"),
    NO_MATCHING_PRODUCT(124, "无匹配产品", "E101", "其他失败（运营商或其他系统返回失败）");

    /**
     * 状态码
     */
    private final int code;

    /**
     * 结果消息
     */
    private final String resultMsg;

    /**
     * 对应的响应码
     */
    private final String respCode;

    /**
     * 对应的响应描述
     */
    private final String respDesc;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ZjytAndZlllgResCode getByCode(int code) {
        for (ZjytAndZlllgResCode status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据状态码获取枚举，如果不存在则返回默认的OTHER_FAILURE
     *
     * @param code 状态码
     * @return 对应的枚举值，如果不存在则返回OTHER_FAILURE
     */
    public static ZjytAndZlllgResCode getByCodeWithDefault(int code) {
        ZjytAndZlllgResCode result = getByCode(code);
        return result != null ? result : OTHER_FAILURE;
    }

    /**
     * 根据状态码获取响应码，如果不存在则返回默认的"E101"
     *
     * @param code 状态码
     * @return 对应的响应码，如果不存在则返回"E101"
     */
    public static String getRespCodeByCode(int code) {
        ZjytAndZlllgResCode status = getByCode(code);
        return status != null ? status.getRespCode() : OTHER_FAILURE.getRespCode();
    }

    /**
     * 根据状态码获取响应描述
     *
     * @param respCode 状态码
     * @return 对应的响应描述，如果不存在则返回默认值
     */
    public static String getRespDescByResCode(String respCode) {
        for (ZjytAndZlllgResCode status : values()) {
            if (status.getRespCode().equals(respCode)) {
                return status.getRespDesc();
            }
        }
        return OTHER_FAILURE.getRespDesc();
    }

    /**
     * 根据响应码获取枚举
     *
     * @param respCode 响应码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ZjytAndZlllgResCode getByRespCode(String respCode) {
        for (ZjytAndZlllgResCode status : values()) {
            if (status.getRespCode().equals(respCode)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 获取响应信息对象
     *
     * @return 包含respCode和respDesc的响应信息对象
     */
    public ResponseInfo getResponseInfo() {
        return new ResponseInfo(this.respCode, this.respDesc);
    }

    @Override
    public String toString() {
        return "OrderStatus{" +
                "code=" + code +
                ", resultMsg='" + resultMsg + '\'' +
                ", respCode='" + respCode + '\'' +
                ", respDesc='" + respDesc + '\'' +
                '}';
    }

    /**
     * 响应信息类
     * 用于返回respCode和respDesc的组合
     */
    @Getter
    @AllArgsConstructor
    public static class ResponseInfo {
        /**
         * 响应码
         */
        private final String respCode;

        /**
         * 响应描述
         */
        private final String respDesc;

        @Override
        public String toString() {
            return "{\"respCode\": \"" + respCode + "\", \"respDesc\": \"" + respDesc + "\"}";
        }
    }
}