package com.xy.promote.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.base.core.util.IdUtils;
import com.xy.promote.dto.ProOrderDTO;
import com.xy.promote.entity.ConfigHuichuan;
import com.xy.promote.entity.PackageFilterBase;
import com.xy.promote.entity.PhoneBlacklist;
import com.xy.promote.entity.ProOrder;
import com.xy.promote.mapper.PackageFilterBaseMapper;
import com.xy.promote.mapper.PhoneBlacklistMapper;
import com.xy.promote.mapper.ProOrderMapper;
import com.xy.promote.service.ConfigHuichuanService;
import com.xy.promote.service.ProOrderService;
import com.xy.promote.util.StringUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【pro_order】的数据库操作Service实现
* @createDate 2024-09-13 18:19:28
*/
@Service
@AllArgsConstructor
public class ProOrderServiceImpl extends ServiceImpl<ProOrderMapper, ProOrder>implements ProOrderService{
	public static ConfigHuichuanService huichuanService;
	final private PhoneBlacklistMapper phoneBlacklistMapper;
	final private PackageFilterBaseMapper packageFilterBaseMapper;
	
	/**
	 * 插入推广订单
	 * @param proOrderDTO 推广订单数据传输对象
	 * @return 插入后的推广订单实体
	 */
	@Override
	public ProOrder insertProOrder(ProOrderDTO proOrderDTO) {
		ProOrder order = ProOrder.builder()
				.id(IdUtils.nextId())
				.reportStatus(0)
				.huichuanStatus(0)
				.createTime(new Date())
				.updateTime(new Date())
				.build();
		BeanUtil.copyProperties(proOrderDTO, order);
		// 去配置表中获取回传参数
		LambdaQueryWrapper<ConfigHuichuan> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(ConfigHuichuan::getChannel, order.getChannel())
				.eq(ConfigHuichuan::getSource, order.getSource())
				.last("ORDER BY create_time desc LIMIT 1");
		ConfigHuichuan one = huichuanService.getOne(queryWrapper);
		order.setCallback(one.getParamName());  // 设置回传参数名
		order.setPlatform(one.getPlatform());  // 设置平台
		order.setApp(StringUtil.extractPackageName(order.getUa()));
		this.save(order);
		return order;
	}
	
	/**
	 * 手机号码是否在黑名单中
	 *
	 * @param phone 手机号码
	 * @return true:在黑名单中 false:不在黑名单中
	 */
	@Override
	public boolean isInBlacklist(String phone) {
		PhoneBlacklist phoneBlacklist = phoneBlacklistMapper.selectOne(new LambdaQueryWrapper<PhoneBlacklist>().eq(PhoneBlacklist::getPhone, phone).last("LIMIT 1"));
		return BeanUtil.isNotEmpty(phoneBlacklist);
	}
	
	/**
	 * 检测包名是否存在
	 * @param appPackage 包名
	 * @return true:存在 false:不存在
 	 */
	@Override
	public boolean isAppExist(String appPackage) {
		PackageFilterBase one = packageFilterBaseMapper.selectOne(new LambdaQueryWrapper<PackageFilterBase>().eq(PackageFilterBase::getPkg, appPackage).last("LIMIT 1"));
		return ObjectUtil.isNotEmpty(one);
	}
}




