package com.xy.promote.controller;


import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xy.base.core.annotation.DirectOutput;
import com.xy.base.core.response.Result;
import com.xy.promote.entity.LogApi;
import com.xy.promote.entity.ProOrder;
import com.xy.promote.service.LogApiService;
import com.xy.promote.service.ProOrderService;
import com.xy.promote.util.AdReportUtil;
import com.xy.promote.vo.GengChuangCallbackRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;

import com.fasterxml.jackson.databind.JsonNode;

import java.time.LocalDateTime;


@Slf4j
@RestController
@RequestMapping("/pro/gengchuang")
@Validated
@AllArgsConstructor
public class GengChuangCallbackController {

    private final LogApiService logApiService;
    private final ProOrderService proOrderService;
    private final ObjectMapper mapper = new ObjectMapper();

    /**
     * 电信10元包回调
     * @return
     */
    @DirectOutput
    @PostMapping(value = "/callback")
    public String callback(HttpServletRequest httpRequest) {
        try {
            // 从请求中读取字节流
            InputStream inputStream = httpRequest.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            StringBuilder requestBody = new StringBuilder();
            String line;

            while ((line = reader.readLine()) != null) {
                requestBody.append(line);
            }

            // 将字节流转换为字符串
            String body = requestBody.toString();

            log.info("gengchuang callback method  body: " + body);

            // 解析请求体
            Map<String, String> parameters = parseParameters(body);

            log.info("gengchuang callback method  parameters: " + parameters);

            // 验证必需参数
            /*
            if (!parameters.containsKey("resCode") || !parameters.containsKey("channelId")
                    || !parameters.containsKey("orderId") || !parameters.containsKey("phone")
                    || !parameters.containsKey("sign")) {
                // return Result.error(500, "缺少必需的参数");
                return "failed";
            }
             */

            // 从参数中提取值
            Integer resCode = Integer.parseInt(parameters.get("resCode"));
            Integer channelId = Integer.parseInt(parameters.get("channelId"));
            String orderId = parameters.get("orderId");
            String phone = parameters.get("phone");
            String extData = parameters.getOrDefault("extData", null);
            String efftime = parameters.getOrDefault("efftime", null);
            String description = parameters.getOrDefault("description", null);
            String sign = parameters.get("sign");

            GengChuangCallbackRequest request = new GengChuangCallbackRequest(resCode, channelId, orderId, phone,  sign,  extData,  efftime,  description);
            // 获取请求者的 IP 地址
            String ipAddress = ServletUtil.getClientIP(httpRequest);
            log.info("gengchuang callback method callback is invoked, request: " + request);
            log.info("Requester IP Address: " + ipAddress);

            if (request.getResCode() == 1) {
                processNewSuccess(request);
            }

            LogApi log = new LogApi();
            log.setMethod("POST");
            log.setTiming(0);
            log.setId(IdUtil.getSnowflake().nextId());
            log.setCreateTime(LocalDateTime.now());
            log.setIp((ServletUtil.getClientIP(httpRequest)));
            log.setRequest(body);
            log.setType(1);
            log.setUrl("/pro/gengchuang/callback");

            logApiService.save(log);

            // 返回成功响应
            // return Result.success("success");
             return "success";
        } catch (IOException | NumberFormatException e) {
            // 处理异常情况
            e.printStackTrace();
            log.error("捕获到异常: ", e);
            // return Result.error(500, "处理请求失败");
             return "failed";
        }

    }

    private Map<String, String> parseParameters(String body) throws IOException {
        JsonNode jsonNode = mapper.readTree(body);
        Iterator<Map.Entry<String, JsonNode>> fields = jsonNode.fields();

        Map<String, String> parameters = new HashMap<>();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> field = fields.next();
            parameters.put(field.getKey(), field.getValue().asText());
        }

        return parameters;
    }

    /**
     * 电信10元包回调
     */
    @PostMapping("/callback_old")
    public void callback_old(HttpServletRequest httpRequest, @RequestBody GengChuangCallbackRequest request) throws JsonProcessingException {
        // 获取请求者的 IP 地址
        String ipAddress = ServletUtil.getClientIP(httpRequest);
        log.info("gengchuang callback method callback is invoked, request: " + request);
        log.info("Requester IP Address: " + ipAddress);

        try {
            if (request.getResCode() == 1) {
                processNewSuccess(request);
            }else{
                // processNewFail(request);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("捕获到异常: ", e);
        }

        // 返回响应
        LogApi log = new LogApi();
        log.setMethod("POST");
        log.setTiming(0);
        log.setId(IdUtil.getSnowflake().nextId());
        log.setCreateTime(LocalDateTime.now());
        log.setIp((ServletUtil.getClientIP(httpRequest)));
        log.setRequest(mapper.writeValueAsString(request));
        log.setType(1);
        log.setUrl("/pro/gengchuang/callback");

        logApiService.save(log);
    }

    // 处理回传
    private void doHuichuan(GengChuangCallbackRequest request, boolean success) {
        LambdaQueryWrapper<ProOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProOrder::getPhone, request.getPhone())
                .eq(ProOrder::getOrderId, request.getOrderId())
                .eq(ProOrder::getReportStatus, 0)
                .last("ORDER BY create_time desc LIMIT 1"); // 限制只查询一条记录
        ProOrder channelReport = proOrderService.getOne(queryWrapper);
        log.info("get report record {}", channelReport);
        if (null != channelReport) {
            if (channelReport.getReportStatus() == 0) {
                // 新增成功时，回传并更新状态
                if (success) {
                    boolean huichuan = AdReportUtil.doSuccessReport(channelReport.getCallback(), channelReport.getPro(), channelReport.getPlatform(), channelReport.getUrl());
                    channelReport.setReportStatus(1);
                    if (huichuan) {
                        channelReport.setHuichuanStatus(1);
                    }
                    channelReport.setUpdateTime(new Date());
                    proOrderService.updateById(channelReport);
                }
                // 新增失败时，仅回传
                else {
                }
                log.info("report successful: {}", channelReport);
            } else {
                log.info("report is nodelay, no more report is need");
            }
        } else {
            log.warn("未获取到回传记录：{}", request);
        }
    }

    /**
     * 处理新增且成功的用户
     */
    private void processNewSuccess(GengChuangCallbackRequest request) {
        // 处理回传
        doHuichuan(request, true);
    }

}
