package com.xy.promote.dto.zjyt;

import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * 验证码校验请求实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Builder
@Accessors(chain = true)
public class CheckCodeRequest {
    /**
     * 唯一标识
     */
    private String linkId;
    /**
     * 业务代码
     */
    private String code;
    /**
     * 手机号码
     */
    private String billNum;
    
    /**
     * 从ZlllgMessageDTO创建CheckCodeRequest实例
     * 
     * @param reqDTO 请求DTO对象
     * @param linkId 唯一标识
     * @return CheckCodeRequest实例
     */
    public static CheckCodeRequest fromReqDTO(ZlllgMessageDTO reqDTO, String linkId) {
        return CheckCodeRequest.builder()
                .linkId(linkId)
                .billNum(reqDTO.getBiz().getMobileNo())
                .code(reqDTO.getBiz().getSmsCode())
                .build();
    }
}