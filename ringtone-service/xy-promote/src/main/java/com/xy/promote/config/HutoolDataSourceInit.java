package com.xy.promote.config;

import cn.hutool.db.ds.DSFactory;
import cn.hutool.db.ds.GlobalDSFactory;
import cn.hutool.setting.Setting;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

@Component
public class HutoolDataSourceInit implements ApplicationRunner {
    @Autowired
    private DataSourceConfig dataSourceConfig;

    @Override
    public void run(ApplicationArguments args) {
        Setting setting = new Setting();
        // 将Spring Boot的配置转换为Hutool的Setting格式
        setting.set("url", dataSourceConfig.getUrl());
        setting.set("user", dataSourceConfig.getUsername());
        setting.set("pass", dataSourceConfig.getPassword());
        setting.set("driver", dataSourceConfig.getDriverClassName());

        // 设置全局数据源
        GlobalDSFactory.set(DSFactory.create(setting));
    }
}
