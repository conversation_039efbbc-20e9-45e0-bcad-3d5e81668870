package com.xy.promote.dto.hzchl;

import cn.hutool.core.util.StrUtil;
import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 杭州铖晗联验证码请求实体
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class HzchlVerifyCodeReq {
    /**
     * 产品ID
     */
    private String productId;
    
    /**
     * 渠道号
     */
    private String channelNo;
    
    /**
     * 价格
     */
    private String price;
    
    /**
     * 手机号码
     */
    private String phone;
    
    /**
     * 自定义参数
     */
    private String cpparam;
    
    /**
     * 用户IP地址
     */
    private String ip;
    
    /**
     * 应用包名
     */
    private String appPackage;
    
    /**
     * 应用名称
     */
    private String appName;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * platform
     */
    private String platform;

    /**
     * 从ZlllgMessageDTO创建HzchlVerifyCodeReq实例
     *
     * @param reqDTO 请求DTO对象
     * @param productId 产品ID
     * @param channelNo 渠道号
     * @param price 价格
     * @return HzchlVerifyCodeReq实例
     */
    public static HzchlVerifyCodeReq fromReqDTO(ZlllgMessageDTO reqDTO, String productId, String channelNo, String price) {
        String appPackage = StrUtil.emptyToDefault(reqDTO.getBiz().getSource().getAppPackage(), "com.xy.promote");
        String appName = StrUtil.emptyToDefault(reqDTO.getBiz().getSource().getAppName(), "其他");
        return HzchlVerifyCodeReq.builder()
                .productId(productId)
                .channelNo(channelNo)
                .price(price)
                .phone(reqDTO.getBiz().getMobileNo())
                .cpparam("juliang")
                .ip(reqDTO.getBiz().getSource().getClientIp())
                .appPackage(appPackage)
                .appName(appName)
                .platform(reqDTO.getBiz().getSource().getPlatform())
                .userAgent(reqDTO.getBiz().getSource().getUserAgent())
                .build();
    }
}
