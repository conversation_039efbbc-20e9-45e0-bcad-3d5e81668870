package com.xy.promote.dto.csys;

import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 长沙云蓑验证码校验请求实体
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class CsysCheckCodeReq {
    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 验证码
     */
    private String vCode;

    /**
     * 从ZlllgMessageDTO创建CsysCheckCodeReq实例
     *
     * @param reqDTO  请求DTO对象
     * @param orderNo 订单号
     * @return CsysCheckCodeReq实例
     */
    public static CsysCheckCodeReq fromReqDTO(ZlllgMessageDTO reqDTO, String orderNo) {
        return CsysCheckCodeReq.builder()
                .orderNo(orderNo)
                .vCode(reqDTO.getBiz().getSmsCode())
                .build();
    }
}