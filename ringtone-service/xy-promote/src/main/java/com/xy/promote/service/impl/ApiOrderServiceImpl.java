package com.xy.promote.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.promote.config.ServerTagConfig;
import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import com.xy.promote.entity.ApiOrder;
import com.xy.promote.mapper.ApiOrderMapper;
import com.xy.promote.service.ApiOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class ApiOrderServiceImpl extends ServiceImpl<ApiOrderMapper, ApiOrder> implements ApiOrderService {

    @Autowired
    private ServerTagConfig serverConfig;

    /**
     * 插入信息
     *
     */
    @Override
    public boolean sendSmsSaveInfo(ZlllgMessageDTO req, String response) {
        if (ObjectUtil.isNull(req) || ObjectUtil.isNull(req.getBiz()) || ObjectUtil.isNull(req.getHead())) {
            return false;
        }
        ApiOrder.ApiOrderBuilder builder = ApiOrder.builder()
                .appId(req.getHead().getAppId())
                .operationType("SMS")
                .orderStatus(req.getHead().getRespCode())
                .channelNo(req.getBiz().getChannelNo())
                .orderId(req.getBiz().getOrderId())
                .outOrderStatus(req.getBiz().getOrderStatus())
                .productNo(req.getBiz().getProductNo())
                .mobileNo(req.getBiz().getMobileNo())
                .remark(response)
                .server(serverConfig.getServerIp());
        
        // 获取Source数据
        Optional.ofNullable(req.getBiz().getSource())
                .ifPresent(source -> builder
                        .appName(source.getAppName())
                        .userAgent(source.getUserAgent())
                        .clientIp(source.getClientIp())
                        .appPackage(source.getAppPackage())
                        .platform(source.getPlatform())
                        .pageUrl(source.getPageUrl()));
        return save(builder.build());
    }

    /**
     * 更新方法
     *
     */
    @Override
    public int checkVerifyCodeLaterUpdate(ZlllgMessageDTO req, String response) {
        if (ObjectUtil.isNull(req) || ObjectUtil.isNull(req.getBiz())) {
            return 0;
        }

        LambdaQueryWrapper<ApiOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ApiOrder::getOrderId, req.getBiz().getOrderId());
        
        ApiOrder.ApiOrderBuilder order = ApiOrder.builder()
                .operationType("ORDER")
                .orderStatus(req.getHead().getRespCode())
                .outOrderId(req.getBiz().getOutOrderId())
                .outOrderStatus(req.getBiz().getOrderStatus())
                .remark(response)
                .server(serverConfig.getServerIp());

        // 获取Source数据
        Optional.ofNullable(req.getBiz().getSource())
                .ifPresent(source -> order
                        .appName(source.getAppName())
                        .userAgent(source.getUserAgent())
                        .appPackage(source.getAppPackage())
                        .platform(source.getPlatform())
                        .pageUrl(source.getPageUrl()));
        ApiOrder orderInfo = order.build();
        return baseMapper.update(orderInfo, queryWrapper);
    }
}
