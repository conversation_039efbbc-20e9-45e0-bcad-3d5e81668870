package com.xy.promote.controller;

import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xy.base.core.enhancer.JacksonMaker;
import com.xy.base.core.exception.AppException;
import com.xy.base.core.util.IdUtils;
import com.xy.promote.entity.ConfigHuichuan;
import com.xy.promote.util.PromoteUtils;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 辰华无线-呼叫秀业务
 *
 * <AUTHOR>
 * @since 2024/10/12
 */
@Slf4j
@RestController
@RequestMapping("/chhjx")
@RequiredArgsConstructor
public class ChhjxController {

    private final ObjectMapper mapper;

    /**
     * 获取验证码
     */
    @PostMapping("/getVerifyCode")
    public Response getVerifyCode(@RequestParam HashMap<String, String> params, HttpServletRequest request) {

        ConfigHuichuan config = PromoteUtils.getConfig(params.get("channel"), params.get("source"));


        Map<String, String> body = getBaseMap(params.get("channelCode"), params.get("phone"), request);

        body.put("orderSeq", IdUtils.nextIdWithPrefix("D"));
        body.put("platform", "巨量");

        return getResponse("https://www.chwx.top/hw/orders/sendSms", body, params, true);
    }

    /**
     * 验证验证码
     */
    @PostMapping("/checkVerifyCode")
    public Response checkVerifyCode(@RequestParam HashMap<String, String> params, HttpServletRequest request) {

        Map<String, String> body = getBaseMap(params.get("channelCode"), params.get("phone"), request);

        body.put("orderSeq", params.get("orderSeq"));
        body.put("checkCode", params.get("vcode"));
        body.put("platform", "巨量");

        return getResponse("https://www.chwx.top/hw/orders/createOrder", body, params, false);
    }

    private Response getResponse(String url, Map<String, String> body, Map<String, String> params, boolean record) {
        log.info("准备请求：" + url);
        log.info("参数：" + JacksonMaker.writeValueAsString(body));
        long start = System.currentTimeMillis();
        try (HttpResponse response = HttpUtil.createPost(url)
                .contentType("application/json")
                .body(JacksonMaker.writeValueAsString(body))
                .execute()) {

            String responseStr = response.body();
            log.info("interfaceAction {},{}", url, responseStr);
            Response res = mapper.readValue(responseStr, Response.class);

            PromoteUtils.insertLogApi(url, JacksonMaker.writeValueAsString(body), responseStr, "POST", (int) (System.currentTimeMillis() - start));
            if (res.success) {
                if (record) {
                    params.put("orderId", res.data.toString());
                    params.put("pro", "nmch_hjx");
                    params.put("ua", body.get("userAgent"));
                    PromoteUtils.insertProOrder(params);
                }
                return res;
            }
            throw new AppException(res.msg);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private Map<String, String> getBaseMap(String channelCode, String phone, HttpServletRequest request) {
        Map<String, String> body = new HashMap<>();

        body.put("channelCode", channelCode);
        body.put("pmChannel", "1816681205411655606");
        body.put("userIp", ServletUtil.getClientIP(request));
        body.put("userAgent", request.getHeader("user-agent"));
        body.put("mobile", phone);

        return body;

    }


    @Data
    public static class Response {
        private Boolean success;
        private String code;
        private String msg;
        private Object data;
    }

}
