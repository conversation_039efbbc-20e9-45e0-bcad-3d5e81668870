package com.xy.promote.dto.gzxn;

import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import lombok.Builder;
import lombok.Data;

/**
 * 广州馨宁验证码验证请求
 */
@Data
@Builder
public class CheckCodeRequest {
  /**
   * 产品ID
   */
  private String productId;

  /**
   * 链接ID
   */
  private String linkid;

  /**
   * 验证码
   */
  private String vcode;

  /**
   * 从ZlllgMessageDTO创建CheckCodeRequest实例
   * 
   * @param reqDTO 请求DTO对象
   * @param linkId 链接ID
   * @return CheckCodeRequest实例
   */
  public static CheckCodeRequest fromReqDTO(ZlllgMessageDTO reqDTO, String linkId) {
    return CheckCodeRequest.builder()
            .productId("100")
            .linkid(linkId)
            .vcode(reqDTO.getBiz().getSmsCode())
            .build();
  }
}