package com.xy.promote.dto.gzxn;

import cn.hutool.core.util.StrUtil;
import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import lombok.Builder;
import lombok.Data;

/**
 * 广州馨宁获取验证码请求
 *
 */
@Data
@Builder
public class VerifyCodeRequest {
	/**
	 * 产品ID
	 */
	private String productId;
	
	/**
	 * 渠道号
	 */
	private String channelNo;
	
	/**
	 * 价格
	 */
	private String price;
	
	/**
	 * 手机号
	 */
	private String phone;
	
	/**
	 * CP参数，固定值：juliang
	 */
	private String cpparam;
	
	/**
	 * 平台
	 */
	private String platform;
	
	/**
	 * 用户IP地址
	 */
	private String ip;
	
	/**
	 * 应用包名
	 */
	private String appPackage;
	
	/**
	 * 应用名称
	 */
	private String appName;
	
	/**
	 * 用户代理
	 */
	private String userAgent;
	
	/**
	 * 从ZlllgMessageDTO创建VerifyCodeRequest实例
	 *
	 * @param reqDTO 请求DTO对象
	 * @return VerifyCodeRequest实例
	 */
	public static VerifyCodeRequest fromReqDTO(ZlllgMessageDTO reqDTO) {
		String appPackage = StrUtil.emptyToDefault(reqDTO.getBiz().getSource().getAppPackage(),
				reqDTO.getBiz().getSource().getUserAgent());
		return VerifyCodeRequest.builder()
				.phone(reqDTO.getBiz().getMobileNo())
				.ip(reqDTO.getBiz().getSource().getClientIp())
				.userAgent(reqDTO.getBiz().getSource().getUserAgent())
				.appPackage(appPackage)
				.appName(reqDTO.getBiz().getSource().getPlatform())
				.platform("巨量")
				.cpparam("juliang")
				.build();
	}
}