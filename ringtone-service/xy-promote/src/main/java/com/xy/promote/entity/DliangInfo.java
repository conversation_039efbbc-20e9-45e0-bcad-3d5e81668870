package com.xy.promote.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

@Data
@Accessors(chain = true)
@SuperBuilder
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "dliang_info")
public class DliangInfo implements Serializable {
    /**
     * 广告
     */
    @TableField(value = "pro")
    @Size(max = 255,message = "广告最大长度要小于 255")
    @NotBlank(message = "广告不能为空")
    private String pro;

    /**
     * 产品订购id
     */
    @TableField(value = "orderId")
    @Size(max = 55,message = "产品订购id最大长度要小于 55")
    @NotBlank(message = "产品订购id不能为空")
    private String orderid;

    /**
     * 提醒范围
     */
    @TableField(value = "notice")
    @Size(max = 55,message = "提醒范围最大长度要小于 55")
    @NotBlank(message = "提醒范围不能为空")
    private String notice;

    /**
     * 是否生效
     */
    @TableField(value = "`enable`")
    @NotNull(message = "是否生效不能为null")
    private Short enable;

    /**
     * 到量描述
     */
    @TableField(value = "description")
    @Size(max = 525,message = "到量描述最大长度要小于 525")
    private String description;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}