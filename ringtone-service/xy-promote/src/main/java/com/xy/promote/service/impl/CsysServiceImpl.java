package com.xy.promote.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xy.promote.dto.ProOrderDTO;
import com.xy.promote.dto.csys.CsysCheckCodeReq;
import com.xy.promote.dto.csys.CsysResponse;
import com.xy.promote.dto.csys.CsysVerifyCodeReq;
import com.xy.promote.service.CsysService;
import com.xy.promote.util.PromoteUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 长沙云蓑服务实现类
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CsysServiceImpl implements CsysService {

  private final ObjectMapper mapper;
  private static final String GET_VERIFY_CODE_API = "http://47.119.117.206:9589/api/gateway";
  private static final String CHECK_VERIFY_CODE_API = "http://47.119.117.206:9589/api/Submit";

  /**
   * 调用长沙云蓑接口 - 获取验证码
   * 
   * @param params      请求参数
   * @param orderParams 订单参数
   * @return 响应结果
   */
  @Override
  public CsysResponse getVerifyCode(CsysVerifyCodeReq params, ProOrderDTO orderParams) {
    Map<String, Object> paramMap = BeanUtil.beanToMap(params);
    String url = GET_VERIFY_CODE_API + "?" + HttpUtil.toParams(paramMap);
    return getResponse(url, orderParams, true);
  }

  /**
   * 调用长沙云蓑接口 - 校验验证码
   * 
   * @param params 验证码参数
   * @return 响应结果
   */
  @Override
  public CsysResponse checkVerifyCode(CsysCheckCodeReq params) {
    Map<String, Object> paramMap = BeanUtil.beanToMap(params);
    String url = CHECK_VERIFY_CODE_API + "?" + HttpUtil.toParams(paramMap);
    return getResponse(url, null, false);
  }

  /**
   * 统一请求接口方法
   * 
   * @param url         请求的接口地址
   * @param orderParams 订单参数
   * @param record      是否记录订单（只有请求验证码成功，并且此参数为true才进行记录）
   * @return 响应结果
   */
  private CsysResponse getResponse(String url, ProOrderDTO orderParams, boolean record) {
    log.info("准备请求：" + url);
    long start = System.currentTimeMillis();
    try (HttpResponse response = HttpUtil.createGet(url)
        .execute()) {

      String responseStr = response.body();
      log.info("interfaceAction {},{}", url, responseStr);
      CsysResponse res = mapper.readValue(responseStr, CsysResponse.class);

      // 记录API调用日志
      PromoteUtils.insertLogApi(url, "", responseStr, "GET", (int) (System.currentTimeMillis() - start));

      // 如果code为0，表示请求成功
      if (res.getCode() != null && res.getCode() == 0) {
        if (record && orderParams != null) {
          orderParams.setOrderId(res.getOrderNo());
          PromoteUtils.insertProOrder(orderParams);
        }
      }
      return res;
    } catch (JsonProcessingException e) {
      log.error("JSON处理异常: ", e);
      throw new RuntimeException(e);
    }
  }
}