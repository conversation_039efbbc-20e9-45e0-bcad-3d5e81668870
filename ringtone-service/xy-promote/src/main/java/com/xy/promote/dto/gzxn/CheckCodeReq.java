package com.xy.promote.dto.gzxn;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Builder
@Accessors(chain = true)
public class CheckCodeReq {
	private String channel;
	private String source;
	private String url;
	private String phone;
	private String productId;
	private String channelNo;
	private String price;
	private String pro;
	
	//private String projectid;
	//private String promotionid;
	//private String orderId;
	//private String ua;
}
