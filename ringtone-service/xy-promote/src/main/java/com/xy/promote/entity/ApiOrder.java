package com.xy.promote.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * 营销推广订单综合表
 */
@Data
@Accessors(chain = true)
@SuperBuilder
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "api_order")
public class ApiOrder implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 应用ID
     */
    @TableField(value = "app_id")
    private String appId;

    /**
     * 渠道编号
     */
    @TableField(value = "channel_no")
    private String channelNo;

    /**
     * 产品编号
     */
    @TableField(value = "product_no")
    private String productNo;

    /**
     * 手机号码
     */
    @TableField(value = "mobile_no")
    private String mobileNo;

    /**
     * 订单ID
     */
    @TableField(value = "order_id")
    private String orderId;

    /**
     * 外部订单ID
     */
    @TableField(value = "out_order_id")
    private String outOrderId;

    /**
     * 外部订单状态
     */
    @TableField(value = "out_order_status")
    private String outOrderStatus;

    /**
     * 订单状态码
     */
    @TableField(value = "order_status")
    private String orderStatus;

    /**
     * 操作类型：SMS-短信发送，ORDER-订单提交
     */
    @TableField(value = "operation_type")
    private String operationType;

    /**
     * 客户端IP
     */
    @TableField(value = "client_ip")
    private String clientIp;

    /**
     * 用户代理
     */
    @TableField(value = "user_agent")
    private String userAgent;

    /**
     * 应用包名
     */
    @TableField(value = "app_package")
    private String appPackage;

    /**
     * 应用名称
     */
    @TableField(value = "app_name")
    private String appName;

    /**
     * 平台名称
     */
    @TableField(value = "platform")
    private String platform;

    /**
     * 页面URL
     */
    @TableField(value = "page_url")
    private String pageUrl;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 服务器
     */
    @TableField(value = "server")
    private String server;

    private static final long serialVersionUID = 1L;
}