package com.xy.promote.controller;

import cn.hutool.extra.servlet.ServletUtil;
import com.xy.promote.dto.gzxn.CheckCodeRequest;
import com.xy.promote.dto.gzxn.GzxnResponse;
import com.xy.promote.dto.gzxn.VerifyCodeRequest;
import com.xy.promote.service.GzxnService;
import com.xy.promote.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;

/**
 * 广州馨宁API控制器
 *
 */
@Slf4j
@RestController
@RequestMapping("/gzxn")
@RequiredArgsConstructor
public class GzxnController {
    
    private final GzxnService gzxnService;
    
    /**
     * 获取验证码接口
     */
    @PostMapping("/getVerifyCode")
    public GzxnResponse getVerifyCode(@RequestBody HashMap<String, String> params, HttpServletRequest request) {
        try {
            VerifyCodeRequest req = VerifyCodeRequest.builder()
                    .productId(params.get("productId"))
                    .channelNo(params.get("channelNo"))
                    .price(params.get("price"))
                    .phone(params.get("phone"))
                    .cpparam("juliang")
                    .ip(ServletUtil.getClientIP(request))
                    .appPackage(StringUtil.extractPackageName(request.getHeader("user-agent")))
                    .appName("巨量")
                    .userAgent(request.getHeader("user-agent"))
                    .build();
            
            return gzxnService.getVerifyCode(req, params);
        } catch (Exception e) {
            log.error("获取验证码失败: {}", e.getMessage(), e);
            // 创建错误响应
            GzxnResponse errorResponse = new GzxnResponse();
            errorResponse.setStatus("0"); // 设置失败状态码
            errorResponse.setMsg(e.getMessage());
            return errorResponse;
        }
    }
    
    /**
     * 验证验证码接口
     */
    @PostMapping("/checkVerifyCode")
    public GzxnResponse checkVerifyCode(@RequestParam HashMap<String, String> params) {
        try {
            CheckCodeRequest requestBody = CheckCodeRequest.builder()
                    .productId(params.get("productId"))
                    .linkid(params.get("linkid"))
                    .vcode(params.get("vcode"))
                    .build();
            
            return gzxnService.checkVerifyCode(requestBody);
        } catch (Exception e) {
            log.error("验证验证码失败: {}", e.getMessage() ,e);
            // 创建错误响应
            GzxnResponse errorResponse = new GzxnResponse();
            errorResponse.setStatus("0"); // 设置失败状态码
            errorResponse.setMsg(e.getMessage());
            return errorResponse;
        }
    }
}
