package com.xy.promote.controller;

import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xy.base.core.enhancer.JacksonMaker;
import com.xy.base.core.exception.AppException;
import com.xy.promote.util.PromoteUtils;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 爱音乐视频彩铃包业务
 *
 * <AUTHOR>
 * @since 2024/9/13 13:56
 */
@Slf4j
@RestController
@RequestMapping("/gcdx10")
@RequiredArgsConstructor
public class Gcdx10Controller {

    private final ObjectMapper mapper;
    private static final String AUTH_KEY = "SPCL_2020";
    private static final String SECRET = "2A7D1837E08482E331360059EDA7ABBB";

    /**
     * 获取验证码
     */
    @PostMapping("/getVerifyCode")
    public HashMap<String, String> getVerifyCode(@RequestParam HashMap<String, String> params, HttpServletRequest request) {

        Map<String, Object> body = getBaseMap();

        body.put("method", "getVerifyCode");

        HashMap<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("phone", params.get("phone"));
        paramsMap.put("channelId", Integer.parseInt(params.get("channelId")));
        paramsMap.put("extData", params.get("extData"));
        body.put("params", paramsMap);

        return getMusicResponse(params, request, body);
    }

    /**
     * 验证验证码
     */
    @PostMapping("/checkVerifyCode")
    public HashMap<String, String> checkVerifyCode(@RequestParam HashMap<String, String> params, HttpServletRequest request) {

        Map<String, Object> body = getBaseMap();
        body.put("method", "checkVerifyCode");

        HashMap<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("orderId", params.get("orderId"));
        paramsMap.put("vcode", params.get("vcode"));
        paramsMap.put("channelId", Integer.parseInt(params.get("channelId")));
        body.put("params", paramsMap);

        return getMusicResponse(params, request, body);
    }

    private HashMap<String, String> getMusicResponse(@RequestParam HashMap<String, String> params, HttpServletRequest request, Map<String, Object> body) {
        params.put("ua", request.getHeader("user-agent"));
        String url = "https://cailing.gengchuangz.com/interfaceAction";
        long start = System.currentTimeMillis();
        try (HttpResponse response = HttpUtil.createPost(url)
                .contentType("application/json")
                .addHeaders(getBaseHeader(request))
                .body(JacksonMaker.writeValueAsString(body))
                .execute()) {

            String responseStr = response.body();
            log.info("interfaceAction {},{}", JacksonMaker.writeValueAsString(body), responseStr);
            IMusicResponse res = mapper.readValue(responseStr, IMusicResponse.class);
            PromoteUtils.insertLogApi(url, JacksonMaker.writeValueAsString(body), responseStr, "POST", (int) (System.currentTimeMillis() - start));

            if ("00".equals(res.code) && "getVerifyCode".equals(body.get("method"))) {
                params.put("orderId", res.getData().get("orderId"));
                PromoteUtils.insertProOrder(params);

                return res.data;
            }
            throw new AppException(res.resultMsg);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private Map<String, Object> getBaseMap() {
        Map<String, Object> body = new HashMap<>();

        body.put("interId", "30007");
        body.put("version", 4);
        body.put("authKey", AUTH_KEY);
        body.put("strParam", System.currentTimeMillis() + "");
        body.put("sequence", signature(body.get("strParam").toString()));

        return body;

    }

    private Map<String, String> getBaseHeader(HttpServletRequest request) {
        Map<String, String> header = new HashMap<>();
        header.put("real-user-ip", ServletUtil.getClientIP(request));
        header.put("real-user-agent", request.getHeader("user-agent"));
        return header;
    }

    private String signature(String timestamp) {
        return DigestUtil.md5Hex("VMSappKey" + AUTH_KEY + "timestamp" + timestamp + SECRET).toUpperCase();
    }

    @Data
    public static class IMusicResponse {
        private String code;
        private String debugMsg;
        private String resultMsg;
        private HashMap<String, String> data;
    }

}
