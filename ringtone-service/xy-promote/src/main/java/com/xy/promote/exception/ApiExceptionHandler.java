package com.xy.promote.exception;

import com.xy.base.core.annotation.DirectOutput;
import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import com.xy.promote.enums.ZlllgResponseCode;
import com.xy.promote.service.DliangRecordService;
import com.xy.promote.util.ZlllgResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * API接口异常处理器
 * 专门处理APIController下的接口异常
 */
@Slf4j
@RestControllerAdvice(assignableTypes = { com.xy.promote.controller.APIController.class }) // 指定处理APIController下的异常
@Order(Ordered.HIGHEST_PRECEDENCE) // 确保最高优先级
@RequiredArgsConstructor
public class ApiExceptionHandler {

  private final DliangRecordService dliangRecord;

  @ExceptionHandler(ApiException.class)
  @DirectOutput
  public ZlllgMessageDTO handleApiException(ApiException e) {
    log.error("API接口异常: {}", e.getMessage());
    ZlllgMessageDTO response = ZlllgResponseUtil.buildZlllgResponse(
        e.getReqDTO(),
        e.getRespCode(),
        e.getRespDesc(),
        null);

    // 处理特定类型的异常并记录到dliang_record表
    if (e.getRespCode().equals(ZlllgResponseCode.PROVINCE_DAILY_LIMIT.getCode())) {
      dliangRecord.saveDliangRecord(e.getReqDTO(), null);
    }
    if (e.getRespCode().equals(ZlllgResponseCode.PRODUCT_DAILY_LIMIT.getCode())) {
      dliangRecord.saveDliangRecord(e.getReqDTO(), "all");
    }
    return response;
  }

  @ExceptionHandler(DuplicateKeyException.class)
  @DirectOutput
  public ZlllgMessageDTO handleDuplicateKeyException(DuplicateKeyException e) {
    log.error("API接口数据重复异常: {}", e.getMessage());

    return ZlllgResponseUtil.buildZlllgResponse(
        null,
        ZlllgResponseCode.ORDER_NOT_EXIST.getCode(), // 使用合适的错误码
        "记录已存在",
        null);
  }

  @ExceptionHandler(DataAccessException.class)
  @DirectOutput
  public ZlllgMessageDTO handleDataAccessException(DataAccessException e) {
    log.error("API接口数据访问异常: {}", e.getMessage());

    return ZlllgResponseUtil.buildZlllgResponse(
        null,
        ZlllgResponseCode.OTHER_SYSTEM_FAILURE.getCode(),
        "数据库操作异常",
        null);
  }

  @ExceptionHandler(Exception.class)
  @DirectOutput
  public ZlllgMessageDTO handleException(Exception e) {
    log.error("API接口未知异常: ", e);

    return ZlllgResponseUtil.buildZlllgResponse(
        null,
        ZlllgResponseCode.APP_ERROR.getCode(),
        ZlllgResponseCode.APP_ERROR.getDescription(),
        null);
  }

}