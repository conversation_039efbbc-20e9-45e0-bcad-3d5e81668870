package com.xy.promote.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2025/6/19 14:44
 */
@Data
@Accessors(chain = true)
@SuperBuilder
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "package_filter_base")
public class PackageFilterBase implements Serializable {
	private static final long serialVersionUID = 1L;
	
	/**
	 * 包名
	 */
	@TableId(value = "pkg", type = IdType.ASSIGN_ID)
	@Size(max = 255, message = "包名最大长度要小于 255")
	@NotBlank(message = "包名不能为空")
	private String pkg;
	
	/**
	 * 名称
	 */
	@TableField(value = "`name`")
	@Size(max = 255, message = "名称最大长度要小于 255")
	private String name;
	
	/**
	 * 用于，半角逗号间隔
	 */
	@TableField(value = "usefor")
	@Size(max = 555, message = "用于，半角逗号间隔最大长度要小于 555")
	private String usefor;
	
	/**
	 * 不用于，半角逗号间隔
	 */
	@TableField(value = "nofor")
	@Size(max = 555, message = "不用于，半角逗号间隔最大长度要小于 555")
	private String nofor;
	
    /**
	 * 创建时间
	 */
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	
    /**
	 * 修改时间
	 */
	@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;
}