package com.xy.promote.service;

import com.xy.promote.dto.lzjz.LzjzCheckCodeRequest;
import com.xy.promote.dto.lzjz.LzjzResponse;
import com.xy.promote.dto.lzjz.LzjzVerifyCodeRequest;

import java.util.Map;

/**
 * 乐只君子业务接口
 */
public interface LzjzService {
	
	/**
	 * 获取验证码
	 *
	 * @param request 请求参数
	 * @param params  额外参数
	 * @return API响应
	 */
	LzjzResponse getVerifyCode(LzjzVerifyCodeRequest request, Map<String, String> params);
	
	/**
	 * 验证短信验证码
	 *
	 * @param request 请求参数
	 * @param params  额外参数
	 * @return API响应
	 */
	LzjzResponse checkVerifyCode(LzjzCheckCodeRequest request, Map<String, String> params);
}