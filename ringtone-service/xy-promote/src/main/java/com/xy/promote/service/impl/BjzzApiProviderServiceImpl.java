package com.xy.promote.service.impl;

import cn.hutool.core.map.MapUtil;
import com.xy.promote.dto.bjzz.BjzzResponse;
import com.xy.promote.dto.zjyt.CheckCodeRequest;
import com.xy.promote.dto.zjyt.VerifyCodeRequest;
import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import com.xy.promote.entity.ProInfoProduct;
import com.xy.promote.enums.BjzzAndZlllgResCode;
import com.xy.promote.enums.ZlllgOrderStatusCode;
import com.xy.promote.mapper.DliangInfoMapper;
import com.xy.promote.mapper.ProInfoProductMapper;
import com.xy.promote.service.ApiOrderService;
import com.xy.promote.service.BjzzService;
import com.xy.promote.service.ProOrderService;
import com.xy.promote.util.ZlllgResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

/**
 * 北京众智API服务实现
 */
@Slf4j
@Service
public class BjzzApiProviderServiceImpl extends BaseApiProviderServiceImpl {

	private final BjzzService bjzzService;

	public BjzzApiProviderServiceImpl(ApiOrderService apiOrderService,
			BjzzService bjzzService,
			ProInfoProductMapper proInfoProductMapper,
			ProOrderService proOrderService,
			DliangInfoMapper dliangInfoMapper) {
		super(apiOrderService, proInfoProductMapper, proOrderService, dliangInfoMapper);
		this.bjzzService = bjzzService;
	}

	@Override
	public String getSupportedProductNo() {
		// 北京众智支持的产品列表
		return "JA";
	}

	@Override
	protected String getProIdentifier() {
		return "bjzz";
	}

	@Override
	public ZlllgMessageDTO sendSms(ZlllgMessageDTO reqDTO) {
		// 查询并验证产品信息
		ProInfoProduct proInfoProduct = getProductInfo(reqDTO.getBiz().getProductNo());
		validateProductInfo(proInfoProduct, reqDTO);

		// 构建请求参数
		Map<String, String> request = buildMapRequestParams(reqDTO, proInfoProduct);

		// 发送请求到北京众智
		BjzzResponse response = bjzzService.getVerifyCode(VerifyCodeRequest.fromReqDTO(reqDTO), request);

		String responseCode = response.getMsgValue("code");
		BjzzAndZlllgResCode code = BjzzAndZlllgResCode.getByCodeWithDefault(responseCode);
		String orderId = response.getMsgValue("orderId");
		String respCode = code.getRespCode();

		// 众联流量果接口响应
		ZlllgMessageDTO res = ZlllgResponseUtil.buildZlllgResponse(
				reqDTO,
				respCode,
				code.getRespDesc(),
				bizBuilder -> bizBuilder.extraInfo(MapUtil.builder().put("linkId", orderId).build()));

		// 更新DTO对象存到数据库
		reqDTO.getHead().setRespCode(response.getCode());
		reqDTO.getBiz().setOrderId(orderId);
		reqDTO.getBiz().setOrderStatus(respCode);
		// 保存信息到数据库
		saveSmsInfo(reqDTO, response);
		return res;
	}

	@Override
	public ZlllgMessageDTO orderSubmit(ZlllgMessageDTO reqDTO) {
		String orderId = Optional.ofNullable(reqDTO.getBiz().getExtraInfo().get("linkId")).map(Object::toString)
				.orElse(null);

		// 去请求北京众智接口
		CheckCodeRequest codeRequest = CheckCodeRequest.fromReqDTO(reqDTO, orderId);

		BjzzResponse response = bjzzService.checkVerifyCode(codeRequest);

		String responseCode = response.getMsgValue("code");
		BjzzAndZlllgResCode code = BjzzAndZlllgResCode.getByCodeWithDefault(responseCode);
		String respCode = code.getRespCode();
		String respDesc = code.getRespDesc();

		// 响应众联流量果接口
		ZlllgMessageDTO res = ZlllgResponseUtil.buildZlllgResponse(
				reqDTO,
				respCode,
				respDesc,
				bizBuilder -> bizBuilder
						.orderId(orderId)
						.outOrderId(reqDTO.getBiz().getOutOrderId())
						.orderStatus(ZlllgOrderStatusCode.PROCESSING.getCode()));

		// 更新DTO对象存到数据库
		reqDTO.getHead().setRespCode(response.getCode());
		reqDTO.getBiz().setOrderId(orderId);
		reqDTO.getBiz().setOrderStatus(respCode);
		// 更新信息到数据库
		updateVerifyCodeInfo(reqDTO, response);
		return res;
	}

}