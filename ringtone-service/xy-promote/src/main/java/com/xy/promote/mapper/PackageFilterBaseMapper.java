package com.xy.promote.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xy.promote.entity.PackageFilterBase;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2025/6/19 14:44
 */
@Mapper
public interface PackageFilterBaseMapper extends BaseMapper<PackageFilterBase> {
	/**
	 * update record
	 *
	 * @param record the updated record
	 * @return update count
	 */
	int updateByPrimaryKey(PackageFilterBase record);
}