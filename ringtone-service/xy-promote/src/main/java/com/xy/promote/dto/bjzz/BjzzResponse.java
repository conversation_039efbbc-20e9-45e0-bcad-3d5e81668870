package com.xy.promote.dto.bjzz;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.xy.base.core.enhancer.JacksonMaker;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 浙江有田API响应实体
 */
@Slf4j
@Data
public class BjzzResponse {
  private String code;
  private String msg;
  private Map<String, String> data;

  public String getMsgValue(String key) {
    JsonNode node = null;
    try {
      node = JacksonMaker.uniMapper().readTree(this.msg);
      JsonNode code = node.get(key);
      return code.textValue();
    } catch (JsonProcessingException e) {
      e.printStackTrace();
      log.error("捕获到异常: ", e);
    }
    return null;
  }
}