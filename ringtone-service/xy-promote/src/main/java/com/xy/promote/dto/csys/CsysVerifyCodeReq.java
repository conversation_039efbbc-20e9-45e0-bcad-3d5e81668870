package com.xy.promote.dto.csys;

import cn.hutool.core.util.StrUtil;
import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 长沙云蓑验证码请求实体
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class CsysVerifyCodeReq {
    /**
     * 商户ID
     */
    private String mchid;

    /**
     * 业务ID
     */
    private String cid;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 透传参数
     */
    private String cpparam;

    /**
     * 客户端IP
     */
    private String userip;

    /**
     * App名称
     */
    private String appName;

    /**
     * App包名
     */
    private String appPackage;

    /**
     * 用户浏览器信息
     */
    private String userua;

    /**
     * 从ZlllgMessageDTO创建CsysVerifyCodeReq实例
     *
     * @param reqDTO 请求DTO对象
     * @param mchid  商户ID
     * @param cid    业务ID
     * @return CsysVerifyCodeReq实例
     */
    public static CsysVerifyCodeReq fromReqDTO(ZlllgMessageDTO reqDTO, String mchid, String cid) {
        String appName = StrUtil.blankToDefault(reqDTO.getBiz().getSource().getAppName(),"其他");
        String appPackage = StrUtil.blankToDefault(reqDTO.getBiz().getSource().getAppPackage(),"com.xy.promote");
        return CsysVerifyCodeReq.builder()
                .mchid(mchid)
                .cid(cid)
                .mobile(reqDTO.getBiz().getMobileNo())
                .cpparam("juliang")
                .userip(reqDTO.getBiz().getSource().getClientIp())
                .appName(appName)
                .appPackage(appPackage)
                .userua(reqDTO.getBiz().getSource().getUserAgent())
                .build();
    }
}