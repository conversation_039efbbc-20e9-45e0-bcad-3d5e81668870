package com.xy.promote.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.xy.promote.dto.gzxn.CheckCodeRequest;
import com.xy.promote.dto.gzxn.GzxnResponse;
import com.xy.promote.dto.gzxn.VerifyCodeRequest;
import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import com.xy.promote.entity.ProInfoProduct;
import com.xy.promote.enums.GzxnAndZlllgResCode;
import com.xy.promote.enums.ZlllgOrderStatusCode;
import com.xy.promote.enums.ZlllgResponseCode;
import com.xy.promote.mapper.DliangInfoMapper;
import com.xy.promote.mapper.ProInfoProductMapper;
import com.xy.promote.service.ApiOrderService;
import com.xy.promote.service.GzxnService;
import com.xy.promote.service.ProOrderService;
import com.xy.promote.util.ApiExceptionUtil;
import com.xy.promote.util.ZlllgResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 广州馨宁API服务实现
 *
 * <AUTHOR>
 * @since 2024/12/1
 */
@Slf4j
@Service
public class GzxnApiProviderServiceImpl extends BaseApiProviderServiceImpl {

	private final GzxnService gzxnService;

	public GzxnApiProviderServiceImpl(ApiOrderService apiOrderService,
			GzxnService gzxnService,
			ProInfoProductMapper proInfoProductMapper,
			ProOrderService proOrderService,
			DliangInfoMapper dliangInfoMapper) {
		super(apiOrderService, proInfoProductMapper, proOrderService, dliangInfoMapper);
		this.gzxnService = gzxnService;
	}

	@Override
	public String getSupportedProductNo() {
		// 广州馨宁
		return "CA";
	}

	@Override
	protected String getProIdentifier() {
		return "gzxn";
	}

	@Override
	public ZlllgMessageDTO sendSms(ZlllgMessageDTO reqDTO) {
		// 进行省份到量检测和运营商检测
		checkDaoliang(reqDTO);

		// 查询并验证产品信息
		ProInfoProduct proInfoProduct = getProductInfo(reqDTO.getBiz().getProductNo());
		validateProductInfo(proInfoProduct, reqDTO);

		// 构建请求参数
		Map<String, String> request = buildMapRequestParams(reqDTO, proInfoProduct);

		// 创建验证码请求DTO
		VerifyCodeRequest verifyCodeRequest = VerifyCodeRequest.fromReqDTO(reqDTO);

		Optional.ofNullable(proInfoProduct.getParams()).ifPresent(params -> {
			JSON paramsJson = JSONUtil.parseObj(params);
			verifyCodeRequest.setProductId(paramsJson.getByPath("productId").toString());
			verifyCodeRequest.setPrice(paramsJson.getByPath("price").toString());
		});
		verifyCodeRequest.setChannelNo(proInfoProduct.getProduct());

		// 发送请求到广州馨宁
		GzxnResponse gzxnResponse = gzxnService.getVerifyCode(verifyCodeRequest, request);

		String status = gzxnResponse.getStatus();
		String linkId = Optional.ofNullable(gzxnResponse.getLinkid()).orElse(IdWorker.getTimeId());
		String respCode = GzxnAndZlllgResCode.getRespCodeByStatus(status);
		String respDesc = Optional.ofNullable(gzxnResponse.getMsg()).orElse(GzxnAndZlllgResCode.getRespDescByRespCode(respCode));

		// 构建众联流量果接口响应
		ZlllgMessageDTO res = ZlllgResponseUtil.buildZlllgResponse(reqDTO, respCode, respDesc,
				bizBuilder -> bizBuilder.extraInfo(MapUtil.builder().put("linkId", linkId).build()));

		// 更新DTO对象存到数据库
		reqDTO.getHead().setRespCode(status);
		reqDTO.getBiz().setOrderId(linkId);
		reqDTO.getBiz().setOrderStatus(respCode);

		// 保存信息到数据库
		saveSmsInfo(reqDTO, gzxnResponse);

		return res;
	}

	@Override
	public ZlllgMessageDTO orderSubmit(ZlllgMessageDTO reqDTO) {
		String linkId = Optional.ofNullable(reqDTO.getBiz().getExtraInfo().get("linkId")).map(Object::toString)
				.orElse("");
		ApiExceptionUtil.throwIfEmpty(linkId, reqDTO, ZlllgResponseCode.PARAM_VALIDATION_FAILED);

		// 查询产品信息
		ProInfoProduct proInfoProduct = getProductInfo(reqDTO.getBiz().getProductNo());

		// 创建验证码校验请求
		CheckCodeRequest checkCodeRequest = CheckCodeRequest.fromReqDTO(reqDTO, linkId);

		// 发送请求到广州馨宁
		GzxnResponse gzxnResponse = gzxnService.checkVerifyCode(checkCodeRequest);

		String status = gzxnResponse.getStatus();
		String respCode = Objects.equals(status, "1") ? ZlllgResponseCode.SUCCESS.getCode()
				: ZlllgResponseCode.OTHER_SYSTEM_FAILURE.getCode();
		String respDesc = Optional.ofNullable(gzxnResponse.getMsg())
				.orElse(GzxnAndZlllgResCode.getRespDescByRespCode(respCode));

		// 构建众联流量果接口响应
		ZlllgMessageDTO res = ZlllgResponseUtil.buildZlllgResponse(reqDTO, respCode, respDesc,
				bizBuilder -> bizBuilder
						.orderId(linkId)
						.outOrderId(reqDTO.getBiz().getOutOrderId())
						.orderStatus(ZlllgOrderStatusCode.PROCESSING.getCode()));

		// 更新DTO对象存到数据库
		reqDTO.getHead().setRespCode(status);
		reqDTO.getBiz().setOrderId(linkId);
		reqDTO.getBiz().setOrderStatus(respCode);

		// 更新信息到数据库
		updateVerifyCodeInfo(reqDTO, gzxnResponse);

		return res;
	}
}