package com.xy.promote.controller;

import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xy.base.core.exception.AppException;
import com.xy.base.core.util.IdUtils;
import com.xy.promote.util.PromoteUtils;
import com.xy.promote.util.StringUtil;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 爱奇
 *
 * <AUTHOR>
 * @since 2024/11/21 13:34
 */
@Slf4j
@RestController
@RequestMapping("/aq")
@RequiredArgsConstructor
public class AqController {

    private final ObjectMapper mapper;

    private final static String APP_KEY = "kmxi4asf";
    private final static String APP_SECRET = "8a5y0ahonw0y";

    private final static String CALL_BACK = "https://pro.widelink.net.cn/promote/callback/aq";

    /**
     * 获取验证码
     */
    @PostMapping("/getVerifyCode")
    public Response getVerifyCode(@RequestParam HashMap<String, String> params, HttpServletRequest request) {

        String time = System.currentTimeMillis() + "";

        Map<String, String> header = new HashMap<>();
        header.put("client-ip", ServletUtil.getClientIP(request));
        header.put("user-agent", request.getHeader("user-agent"));
        header.put("x-platform-name", "巨量");
        header.put("x-app-package", StringUtil.extractPackageName(request.getHeader("user-agent")));
        header.put("x-app-name", "zs");

        String orderId = IdUtils.nextIdWithPrefix("aq");
        Map<String, String> body = new HashMap<>();
        body.put("appKey", APP_KEY);
        body.put("phone", params.get("phone"));
        body.put("channelCode", params.get("channelCode"));
        body.put("timestamp", time);
        body.put("busOrderId", orderId);
        body.put("busCallbackUrl", CALL_BACK);
        body.put("sign", sign(params.get("phone"), params.get("channelCode"), time, orderId, CALL_BACK));


        return getResponse("https://api.aikuaipai.com/mall/tg/huaFei/create/order", body, params, header, true);
    }

    /**
     * 验证验证码
     */
    @PostMapping("/checkVerifyCode")
    public Response checkVerifyCode(@RequestParam HashMap<String, String> params) {

        String time = System.currentTimeMillis() + "";


        Map<String, String> body = new HashMap<>();
        body.put("appKey", APP_KEY);
        body.put("phone", params.get("phone"));
        body.put("smsCode", params.get("vcode"));
        body.put("timestamp", time);
        body.put("sign", sign(params.get("phone"), params.get("vcode"), time));

        return getResponse("https://api.aikuaipai.com/mall/tg/huaFei/checkCode", body, params, null, false);
    }

    @SneakyThrows
    private Response getResponse(String url, Map<String, String> body, Map<String, String> params, Map<String, String> header, boolean record) {

        String reqBody = mapper.writeValueAsString(body);
        log.info("准备请求：{}; 参数{}", url, reqBody);
        long start = System.currentTimeMillis();
        try (HttpResponse response = HttpUtil.createPost(url)
                .contentType("application/json")
                .headerMap(header, true)
                .body(reqBody)
                .execute()) {

            String responseStr = response.body();
            log.info("请求返回 {},{}", url, responseStr);
            Response res = mapper.readValue(responseStr, Response.class);

            PromoteUtils.insertLogApi(url, "", responseStr, "GET", (int) (System.currentTimeMillis() - start));
            if (res.code.equals(200)) {
                if (record) {
                    params.put("orderId", body.get("busOrderId"));
                    params.put("ua", header.get("user-agent"));
                    PromoteUtils.insertProOrder(params);
                }
                res.data = body.get("busOrderId");
                return res;
            }
            throw new AppException(res.msg);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private String sign(String... strs) {
        StringBuilder sb = new StringBuilder();
        sb.append(APP_KEY);
        sb.append(APP_SECRET);
        for (String str : strs) {
            sb.append(str);
        }
        return DigestUtil.md5Hex(sb.toString());
    }


    @Data
    public static class Response {
        private Integer code;
        private String msg;
        private Object data;
    }
}
