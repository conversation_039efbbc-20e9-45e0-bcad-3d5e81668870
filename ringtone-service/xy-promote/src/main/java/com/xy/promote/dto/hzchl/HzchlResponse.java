package com.xy.promote.dto.hzchl;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 杭州铖晗联API响应实体
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class HzchlResponse {
    /**
     * 状态码
     */
    private String status;
    
    /**
     * 响应消息
     */
    private String msg;
    
    /**
     * 链接ID
     */
    private String linkid;
    
    /**
     * 支付URL
     */
    private String payUrl;
}
