package com.xy.promote.service;

import com.xy.promote.dto.ProOrderDTO;
import com.xy.promote.entity.ProOrder;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【pro_order】的数据库操作Service
* @createDate 2024-09-13 18:19:28
*/
public interface ProOrderService extends IService<ProOrder> {
	/**
	 * 插入推广订单
	 * @param proOrderDTO 推广订单数据传输对象
	 * @return 插入后的推广订单实体
	 */
	ProOrder insertProOrder(ProOrderDTO proOrderDTO);
	
	/**
	 * 手机号码是否在黑名单中
	 * @param phone 手机号码
	 * @return true:在黑名单中 false:不在黑名单中
	 */
	boolean isInBlacklist(String phone);
	
	/**
	 * 应用是否已经存在
	 * @param appPackage 应用包名
	 * @return true:已经存在 false:不存在
	 */
	boolean isAppExist(String appPackage);
}
