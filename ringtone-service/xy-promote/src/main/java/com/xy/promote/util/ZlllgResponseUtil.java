package com.xy.promote.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xy.promote.dto.zlllg.BizDTO;
import com.xy.promote.dto.zlllg.HeadDTO;
import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import com.xy.promote.service.ProductPromotionCompanyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.function.Consumer;

/**
 * 众联流量果响应工具类
 */
@Slf4j
@Component
public class ZlllgResponseUtil {

  private static ObjectMapper objectMapper;
  private static ProductPromotionCompanyService productPromotionCompanyService;

  @Autowired
  public ZlllgResponseUtil(ObjectMapper objectMapper, ProductPromotionCompanyService productPromotionCompanyService) {
    ZlllgResponseUtil.objectMapper = objectMapper;
    ZlllgResponseUtil.productPromotionCompanyService = productPromotionCompanyService;
  }

  /**
   * 创建ZlllgMessageDTO响应
   * 
   * @param reqDTO      请求DTO
   * @param respCode    响应码
   * @param respDesc    响应描述
   * @param bizConsumer BizDTO构建器的自定义配置
   * @return 构建的ZlllgMessageDTO响应
   */
  public static ZlllgMessageDTO buildZlllgResponse(ZlllgMessageDTO reqDTO, String respCode, String respDesc,
      Consumer<BizDTO.BizDTOBuilder> bizConsumer) {
    HeadDTO.HeadDTOBuilder headBuilder = HeadDTO.builder()
        .appId(reqDTO != null && reqDTO.getHead() != null ? reqDTO.getHead().getAppId() : "100000")
        .timestamp(reqDTO != null && reqDTO.getHead() != null ? reqDTO.getHead().getTimestamp()
            : System.currentTimeMillis() + "")
        .transId(reqDTO != null && reqDTO.getHead() != null ? reqDTO.getHead().getTransId()
            : java.util.UUID.randomUUID().toString().replace("-", ""))
        .respCode(respCode)
        .respDesc(respDesc);

    BizDTO.BizDTOBuilder bizBuilder = BizDTO.builder();
    if (bizConsumer != null) {
      bizConsumer.accept(bizBuilder);
    }
    ZlllgMessageDTO responseDTO = ZlllgMessageDTO.builder()
        .head(headBuilder.build())
        .biz(bizBuilder.build())
        .build();

    // 添加签名
    addSignature(responseDTO);

    return responseDTO;
  }

  /**
   * 为响应添加签名
   * 
   * @param responseDTO 响应DTO
   */
  public static void addSignature(ZlllgMessageDTO responseDTO) {
    try {
      if (responseDTO != null && responseDTO.getHead() != null && responseDTO.getHead().getSign() == null
          && objectMapper != null) {
        // 获取appId，根据appId动态获取secretKey
        String appId = responseDTO.getHead().getAppId();
        String secretKey = productPromotionCompanyService != null
            ? productPromotionCompanyService.getSecretKeyByAppId(appId)
            : null;

        if (StringUtils.hasText(secretKey)) {
          Map<String, Object> params = objectMapper.convertValue(responseDTO, Map.class);
          String sign = ZlllgSignUtil.generateSignature(params, secretKey);
          responseDTO.getHead().setSign(sign);
        } else {
          log.warn("无法为响应添加签名: 未找到appId {} 对应的secretKey", appId);
        }
      }
    } catch (Exception e) {
      // 签名生成失败时记录日志但不影响响应返回
      log.error("生成签名失败: " + e.getMessage(), e);
    }
  }
}