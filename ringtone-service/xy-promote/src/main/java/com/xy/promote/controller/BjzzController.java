package com.xy.promote.controller;

import com.xy.promote.dto.bjzz.BjzzResponse;
import com.xy.promote.dto.zjyt.ZjytResponse;
import com.xy.promote.service.BjzzService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 北京众智汇融
 */
@Slf4j
@RestController
@RequestMapping("/bjzz")
@RequiredArgsConstructor
public class BjzzController {
    
    private final BjzzService bjzzService;

    /**
     * 下发验证码接口
     */
    @PostMapping("/getVerifyCode")
    public BjzzResponse getVerifyCode(@RequestParam Map<String, String> params, HttpServletRequest request) {
        return bjzzService.getVerifyCode(params, request);
    }

    /**
     * 短信验证接口
     */
    @PostMapping("/checkVerifyCode")
    public BjzzResponse checkVerifyCode(@RequestParam Map<String, String> params) {
        return bjzzService.checkVerifyCode(params);
    }
}
