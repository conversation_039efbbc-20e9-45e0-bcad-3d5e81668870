package com.xy.promote.dto.zsgl;

import cn.hutool.core.map.MapUtil;
import com.xy.promote.dto.zlllg.ZlllgMessageDTO;

import com.xy.promote.enums.ZlllgResponseCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/7/7 18:22
 */
@Data
@Accessors(chain = true)
@SuperBuilder
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ZsglRes {
  private String code;
  private String msg;
  private String data;

  public static ZsglRes success(String data) {
    return ZsglRes.builder().code("0000").msg("success").data(data).build();
  }

  public static ZsglRes error(String code, String msg) {
    return ZsglRes.builder().code(code).msg(msg).build();
  }

  public static ZsglRes error(String msg) {
    return ZsglRes.builder().code("9999").msg(msg).build();
  }

  public static ZsglRes error(String code, String msg, String data) {
    return ZsglRes.builder().code(code).msg(msg).data(data).build();
  }

  public ZsglRes fromZlllgMessageDTO(ZlllgMessageDTO zlllgMessageDTO) {
    Map extraInfo = zlllgMessageDTO.getBiz().getExtraInfo();
    if (MapUtil.isNotEmpty(extraInfo)) {
      Object firstValue = extraInfo.values().iterator().next();
      data = String.valueOf(firstValue);
    }
    code = zlllgMessageDTO.getHead().getRespCode();
    msg = zlllgMessageDTO.getHead().getRespDesc();
    return this;
  }
  
  public ZsglRes fromZlllgMessageDTOSummit(ZlllgMessageDTO zlllgMessageDTO) {
    if (zlllgMessageDTO.getHead().getRespCode().equals(ZlllgResponseCode.SUCCESS.getCode())){
      code = zlllgMessageDTO.getBiz().getOrderStatus();
      data = "true";
    }else {
      code = zlllgMessageDTO.getHead().getRespCode();
    }
    msg = zlllgMessageDTO.getHead().getRespDesc();
    return this;
  }
  
}
