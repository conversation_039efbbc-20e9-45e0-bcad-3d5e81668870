package com.xy.promote.dto.lzjz;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 乐只君子验证码请求实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class LzjzVerifyCodeRequest {
  /**
   * 业务代码
   */
  private String biz;
  /**
   * 渠道
   */
  private String ditch;
  /**
   * 手机号码
   */
  private String mobile;
  /**
   * 自定义参数
   */
  private String cpparam;
  /**
   * 用户IP地址
   */
  private String ipAddress;
  /**
   * 应用包名
   */
  private String appPackage;
  /**
   * 应用名称
   */
  private String appName;
  /**
   * 来源URL
   */
  private String sourceUrl;
  /**
   * 用户代理
   */
  private String userAgent;
}