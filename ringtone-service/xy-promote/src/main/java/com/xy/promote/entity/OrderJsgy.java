package com.xy.promote.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * @TableName order_jsgy
 */
@TableName(value = "order_jsgy")
@Data
public class OrderJsgy implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 成功错误信息（success/fail）
     */
    private String msg;

    /**
     * 金额（单位分）
     */
    private String price;

    /**
     * 1为计费成功，其他失败
     */
    private String status;

    /**
     * 渠道编号
     */
    private String channelNo;

    /**
     * 订单id  （去重）
     */
    private String linkid;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 获取验证码请求提交的透传参数
     */
    private String cpparam;

    /**
     * 创建日期
     */
    private Date createTime;

    /**
     * 修改日期
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    public OrderJsgy(String msg, String price, String status, String channelNo, String linkid, String phone, String cpparam) {
        this.msg = msg;
        this.price = price;
        this.status = status;
        this.channelNo = channelNo;
        this.linkid = linkid;
        this.phone = phone;
        this.cpparam = cpparam;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        OrderJsgy other = (OrderJsgy) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getMsg() == null ? other.getMsg() == null : this.getMsg().equals(other.getMsg()))
                && (this.getPrice() == null ? other.getPrice() == null : this.getPrice().equals(other.getPrice()))
                && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
                && (this.getChannelNo() == null ? other.getChannelNo() == null : this.getChannelNo().equals(other.getChannelNo()))
                && (this.getLinkid() == null ? other.getLinkid() == null : this.getLinkid().equals(other.getLinkid()))
                && (this.getPhone() == null ? other.getPhone() == null : this.getPhone().equals(other.getPhone()))
                && (this.getCpparam() == null ? other.getCpparam() == null : this.getCpparam().equals(other.getCpparam()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getMsg() == null) ? 0 : getMsg().hashCode());
        result = prime * result + ((getPrice() == null) ? 0 : getPrice().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getChannelNo() == null) ? 0 : getChannelNo().hashCode());
        result = prime * result + ((getLinkid() == null) ? 0 : getLinkid().hashCode());
        result = prime * result + ((getPhone() == null) ? 0 : getPhone().hashCode());
        result = prime * result + ((getCpparam() == null) ? 0 : getCpparam().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", msg=").append(msg);
        sb.append(", price=").append(price);
        sb.append(", status=").append(status);
        sb.append(", channelNo=").append(channelNo);
        sb.append(", linkid=").append(linkid);
        sb.append(", phone=").append(phone);
        sb.append(", cpparam=").append(cpparam);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}