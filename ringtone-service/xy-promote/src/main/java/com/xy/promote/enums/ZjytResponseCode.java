package com.xy.promote.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单状态枚举
 * 表示订单处理结果的状态码和对应消息
 */
@Getter
@AllArgsConstructor
public enum ZjytResponseCode {
    
    SUCCESS(0, "成功"),
    SUCCESS_200(200, "成功"),
    VERIFICATION_CODE_ERROR(40001, "验证码错误"),
    NO_VERIFICATION_CODE_REQUESTED(109, "该手机号还未请求验证码"),
    VERIFICATION_CODE_FORMAT_ERROR(40002, "验证码格式错误"),
    PROVINCE_LIMIT_REACHED(114, "省份到量"),
    PRODUCT_LIMIT_REACHED(104, "当前产品已达投放上限"),
    BLACKLISTED_NUMBER(103, "黑名单号码暂不支持订购"),
    PRODUCT_OFFLINE(105, "投放产品已下线"),
    CHANNEL_OFFLINE(106, "当前渠道已下线（联系商务处理）"),
    LINK_OFFLINE(111, "当前投放链接已下线"),
    OUT_OF_SERVICE_PERIOD(115, "非投放时段"),
    NO_MATCHING_PRODUCT(124, "无匹配产品"),
    VERIFICATION_CODE_EXPIRED(13200, "验证码已过期");

    /**
     * 状态码
     */
    private final int code;
    
    /**
     * 结果消息
     */
    private final String resultMsg;
    
    /**
     * 根据状态码获取枚举
     * 
     * @param code 状态码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ZjytResponseCode getByCode(int code) {
        for (ZjytResponseCode status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }
    
    @Override
    public String toString() {
        return "OrderStatus{" +
                "code=" + code +
                ", resultMsg='" + resultMsg + '\'' +
                '}';
    }
}