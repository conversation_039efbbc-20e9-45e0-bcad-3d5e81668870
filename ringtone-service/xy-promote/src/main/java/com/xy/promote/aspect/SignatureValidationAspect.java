package com.xy.promote.aspect;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xy.base.core.exception.AppException;
import com.xy.promote.annotation.SignatureValidation;
import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import com.xy.promote.enums.ZlllgResponseCode;
import com.xy.promote.service.ProductPromotionCompanyService;
import com.xy.promote.util.ApiExceptionUtil;
import com.xy.promote.util.ZlllgSignUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.util.Map;

/**
 * 签名校验切面
 * 处理带有 SignatureValidation 注解的方法，自动进行签名验证
 * 并为响应对象自动添加签名
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class SignatureValidationAspect {

  private final ObjectMapper objectMapper;
  private final ProductPromotionCompanyService productPromotionCompanyService;

  /**
   * 环绕通知，拦截带有 SignatureValidation 注解的方法
   * 1. 验证请求签名 (如果有签名才验证，或根据required参数决定)
   * 2. 为响应对象自动添加签名
   */
  @Around("@annotation(com.xy.promote.annotation.SignatureValidation)")
  public Object validateSignature(ProceedingJoinPoint joinPoint) throws Throwable {
    MethodSignature signature = (MethodSignature) joinPoint.getSignature();
    Method method = signature.getMethod();
    SignatureValidation annotation = method.getAnnotation(SignatureValidation.class);

    // 获取方法名称和类名称，用于日志记录
    String methodName = method.getName();
    String className = method.getDeclaringClass().getSimpleName();

    // 是否强制要求签名
    boolean isRequired = annotation.required();

    Object[] args = joinPoint.getArgs();
    String secretKey;

    // 遍历参数，找到 ZlllgMessageDTO 类型的参数进行签名校验
    for (Object arg : args) {
      if (arg instanceof ZlllgMessageDTO) {
        ZlllgMessageDTO reqDTO = (ZlllgMessageDTO) arg;

        // 获取appId用于查找对应的secretKey
        String appId = reqDTO.getHead() != null ? reqDTO.getHead().getAppId() : null;

        // 获取密钥，优先使用注解中的密钥，如果为空则根据appId动态获取
        if (StringUtils.hasText(annotation.secretKey())) {
          secretKey = annotation.secretKey();
        } else {
          secretKey = productPromotionCompanyService.getSecretKeyByAppId(appId);
           //如果没有找到对应的secretKey，拒绝请求
          if (!StringUtils.hasText(secretKey)) {
            log.debug("签名校验失败: 未找到appId {} 对应的secretKey", appId);
            //log.error("签名校验失败: 未找到appId {} 对应的secretKey", appId);
            //ApiExceptionUtil.throwApiException(reqDTO, ZlllgResponseCode.SIGNATURE_ERROR, "无效的appId");
          }
        }

        // 获取原始签名
        String originalSign = reqDTO.getHead() != null ? reqDTO.getHead().getSign() : null;
        boolean hasSignature = StringUtils.hasText(originalSign);

        // 判断是否需要进行签名校验
        if (isRequired && !hasSignature) {
          // 如果必须验签但没有签名，则拒绝请求
          log.error("签名校验失败: 请求需要签名但未提供签名");
          throw new AppException(503, "非法请求：缺少签名");
        } else if (hasSignature) {
          // 如果有签名，则进行校验
          log.debug("检测到签名，将进行校验: {}, appId: {}", originalSign, appId);

          // 清除签名字段，不参与校验
          reqDTO.getHead().setSign(null);

          // 转换为Map
          Map<String, Object> params = objectMapper.convertValue(reqDTO, Map.class);

          // 生成签名
          String calculatedSign = ZlllgSignUtil.generateSignature(params, secretKey);
          log.debug("use secretKey for appId: {}, request sign: {}, verify sign: {}",appId, originalSign, calculatedSign);

          // 比较签名
          if (!originalSign.equals(calculatedSign)) {
            log.error("签名校验失败: 非法请求签名：{},appId: {},原始签名: {},非法参数: {}", calculatedSign, originalSign, appId,JSONUtil.toJsonStr(params));
            ApiExceptionUtil.throwApiException(reqDTO, ZlllgResponseCode.SIGNATURE_ERROR);
          }
          log.debug("签名校验通过: {}.{}, appId: {}", className, methodName, appId);
          // 为了让后续处理仍能访问到原始签名，可以选择是否需要还原签名
          reqDTO.getHead().setSign(originalSign);
        } else {
          // 没有签名且不是必须验签，跳过校验
          log.debug("未检测到签名且不要求签名，跳过签名校验: {}.{}", className, methodName);
        }
      }
    }

    // 签名校验通过，继续执行原方法
    Object result = joinPoint.proceed();

    // 如果返回值是 ZlllgMessageDTO 类型，自动为其添加签名
    if (result instanceof ZlllgMessageDTO) {
      ZlllgMessageDTO resDTO = (ZlllgMessageDTO) result;

      // 为响应对象添加签名
      if (resDTO.getHead() != null && resDTO.getHead().getSign() == null) {
        // 获取当前响应对应的appId和secretKey
        String responseAppId = resDTO.getHead().getAppId();
        String responseSecretKey = StringUtils.hasText(annotation.secretKey()) ? annotation.secretKey() : productPromotionCompanyService.getSecretKeyByAppId(responseAppId);

        if (StringUtils.hasText(responseSecretKey)) {
          // 使用与客户端相同的方式生成签名
          resDTO.getHead().setSign(
              ZlllgSignUtil.generateSignature(objectMapper.convertValue(resDTO, Map.class), responseSecretKey));
          log.debug("为响应添加签名: {}, appId: {}", resDTO.getHead().getSign(), responseAppId);
        }/*  else {
          log.warn("无法为响应添加签名: 未找到appId {} 对应的secretKey", responseAppId);
        } */
      }
    }

    return result;
  }
}