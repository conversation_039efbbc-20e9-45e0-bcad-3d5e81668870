package com.xy.promote.dto.csys;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 长沙云蓑响应实体
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class CsysResponse {
    /**
     * 响应码，0表示成功
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String msg;
    
    /**
     * 订单号，仅在获取验证码成功时返回
     */
    private String orderNo;
}