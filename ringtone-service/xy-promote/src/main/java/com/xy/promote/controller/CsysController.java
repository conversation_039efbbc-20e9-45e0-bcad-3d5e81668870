package com.xy.promote.controller;

import cn.hutool.extra.servlet.ServletUtil;
import com.xy.promote.dto.ProOrderDTO;
import com.xy.promote.dto.csys.CsysCheckCodeReq;
import com.xy.promote.dto.csys.CsysResponse;
import com.xy.promote.dto.csys.CsysVerifyCodeReq;
import com.xy.promote.service.CsysService;
import com.xy.promote.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;

/**
 * 长沙云蓑
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
@Slf4j
@RestController
@RequestMapping("/csys")
@RequiredArgsConstructor
public class CsysController {

  private final CsysService csysService;

  /**
   * 获取验证码
   */
  @PostMapping("/getVerifyCode")
  public CsysResponse getVerifyCode(@RequestParam HashMap<String, String> params, HttpServletRequest request) {
    // 请求参数
    CsysVerifyCodeReq req = CsysVerifyCodeReq.builder()
        .mchid(params.get("mchid"))
        .cid(params.get("cid"))
        .mobile(params.get("phone"))
        .cpparam("juliang")
        .userip(ServletUtil.getClientIP(request))
        .appPackage(StringUtil.extractPackageName(request.getHeader("user-agent")))
        .appName("巨量")
        .userua(request.getHeader("user-agent"))
        .build();

    // 订单参数
    ProOrderDTO proDTO = ProOrderDTO.builder()
        .ua(request.getHeader("user-agent"))
        .pro(params.get("pro"))
        .build();

    return csysService.getVerifyCode(req, proDTO);
  }

  /**
   * 验证验证码
   */
  @PostMapping("/checkVerifyCode")
  public CsysResponse checkVerifyCode(@RequestParam HashMap<String, String> params) {
    CsysCheckCodeReq req = CsysCheckCodeReq.builder()
        .orderNo(params.get("orderNo"))
        .vCode(params.get("vCode"))
        .build();

    return csysService.checkVerifyCode(req);
  }

}