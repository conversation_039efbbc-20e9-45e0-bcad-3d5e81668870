package com.xy.promote.service;

import com.xy.promote.dto.bjzz.BjzzResponse;
import com.xy.promote.dto.zjyt.CheckCodeRequest;
import com.xy.promote.dto.zjyt.VerifyCodeRequest;
import com.xy.promote.dto.zjyt.ZjytResponse;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 浙江有田业务接口
 */
public interface BjzzService {

  /**
   * 获取验证码
   * 
   * @param params  请求参数
   * @param request HTTP请求
   * @return API响应
   */
  BjzzResponse getVerifyCode(Map<String, String> params, HttpServletRequest request);

  BjzzResponse getVerifyCode(VerifyCodeRequest req, Map<String, String> request);

  /**
   * 验证短信验证码
   * 
   * @param params 请求参数
   * @return API响应
   */
  BjzzResponse checkVerifyCode(Map<String, String> params);

  BjzzResponse checkVerifyCode(CheckCodeRequest codeRequest);
}