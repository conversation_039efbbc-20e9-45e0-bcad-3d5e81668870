package com.xy.promote.util;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xy.base.core.exception.AppException;
import com.xy.base.core.util.IdUtils;
import com.xy.promote.dto.ProOrderDTO;
import com.xy.promote.entity.ConfigHuichuan;
import com.xy.promote.entity.LogApi;
import com.xy.promote.entity.ProOrder;
import com.xy.promote.service.ConfigHuichuanService;
import com.xy.promote.service.LogApiService;
import com.xy.promote.service.ProOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;


/**
 * <AUTHOR>
 * @since 2024/3/25 11:46
 */
@Component
@RequiredArgsConstructor
public class PromoteUtils {


    private final ProOrderService myProOrderService;
    private final ConfigHuichuanService initHuichuanService;
    private final LogApiService myLogApiService;

    public static ProOrderService proOrderService;
    public static ConfigHuichuanService huichuanService;
    private static LogApiService logApiService;


    @PostConstruct
    void init() {
        proOrderService = myProOrderService;
        huichuanService = initHuichuanService;
        logApiService = myLogApiService;
    }

    public static ConfigHuichuan getConfig(String channel, String source) {
        return huichuanService.lambdaQuery().eq(ConfigHuichuan::getChannel, channel)
                .eq(ConfigHuichuan::getSource, source)
                .last("ORDER BY create_time desc LIMIT 1")
                .one();
    }

    private static void setParams(ProOrder order, Map<String, String> params) {
        LambdaQueryWrapper<ConfigHuichuan> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConfigHuichuan::getChannel, order.getChannel())
                .eq(ConfigHuichuan::getSource, order.getSource())
                .last("ORDER BY create_time desc LIMIT 1");
        ConfigHuichuan one = huichuanService.getOne(queryWrapper);

        order.setCallback(params.get(one.getParamName()));
        order.setPlatform(one.getPlatform());
        order.setApp(StringUtil.extractPackageName(order.getUa()));
    }

    public static ProOrder insertProOrder(Map<String, String> params) {

        ProOrder order = new ProOrder();
        order.setId(IdUtils.nextId());
        order.setChannel(params.get("channel"));
        order.setPhone(params.get("phone"));
        order.setSource(params.get("source"));
        order.setReportStatus(0);
        order.setHuichuanStatus(0);
        order.setPro(params.get("pro"));
        order.setPlatform(params.get("platform"));
        order.setProjectid(params.get("projectid"));
        order.setPromotionid(params.get("promotionid"));
        order.setOrderId(params.get("orderId"));
        order.setUrl(params.get("url"));
        order.setUa(params.get("ua"));
        order.setCreateTime(new Date());
        order.setUpdateTime(new Date());
        setParams(order, params);
        // 检验手机号码是否在黑名单中，如果在就抛异常
        AppException.tnt(proOrderService.isInBlacklist(order.getPhone()), "办理失败，请联系管理员");
        // 校验APP包名是否在黑名单中，如果在就抛异常
        AppException.tnt(proOrderService.isAppExist(order.getApp()), "该APP无法发展"); proOrderService.save(order);
        return order;
    }
    
    public static ProOrder insertProOrder(ProOrderDTO proOrderDTO) {
        ProOrder order = ProOrder.builder()
                .id(IdUtils.nextId())
                .reportStatus(0)
                .huichuanStatus(0)
                .createTime(new Date())
                .updateTime(new Date())
                .build();
        BeanUtil.copyProperties(proOrderDTO, order);
        // 去配置表中获取回传信息
        LambdaQueryWrapper<ConfigHuichuan> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConfigHuichuan::getChannel, order.getChannel())
                .eq(ConfigHuichuan::getSource, order.getSource())
                .last("ORDER BY create_time desc LIMIT 1");
        ConfigHuichuan one = huichuanService.getOne(queryWrapper);
        // 设置回传参数，原先是从config_huichuan表中获取param_name，然后再从map中通过param_name获取到Callback，现在直接从proOrderDTO带过来直接设置，这里就不做再次设置了
        //order.setCallback(one.getParamName());
        order.setPlatform(one.getPlatform());  // 设置平台
        order.setApp(StringUtil.extractPackageName(order.getUa()));
        // 检验手机号码是否在黑名单中，如果在就抛异常
        AppException.tnt(proOrderService.isInBlacklist(order.getPhone()), "办理失败，请联系管理员");
        // 校验APP包名是否在黑名单中，如果在就抛异常
        AppException.tnt(proOrderService.isAppExist(order.getApp()), "该APP无法发展");
        proOrderService.save(order);
        return order;
    }

    public static LogApi insertLogApi(String url, String req, String res, String method, Integer timing) {

        LogApi log = new LogApi();

        log.setId(IdUtils.nextId());
        log.setCreateTime(LocalDateTime.now());
        log.setUrl(url);
        log.setType(0);
        log.setTiming(timing);
        log.setMethod(method);
        log.setResponse(res);
        log.setRequest(req);

        logApiService.save(log);
        return log;
    }

    public static LogApi insertLogApi(String phone, String url, String req, String res, String method, Integer timing) {

        LogApi log = new LogApi();

        log.setId(IdUtils.nextId());
        log.setPhone(phone);
        log.setCreateTime(LocalDateTime.now());
        log.setUrl(url);
        log.setType(0);
        log.setTiming(timing);
        log.setMethod(method);
        log.setResponse(res);
        log.setRequest(req);

        logApiService.save(log);
        return log;
    }

}
