package com.xy.promote.service;

import com.xy.promote.dto.ProOrderDTO;
import com.xy.promote.dto.csys.CsysCheckCodeReq;
import com.xy.promote.dto.csys.CsysResponse;
import com.xy.promote.dto.csys.CsysVerifyCodeReq;

/**
 * 长沙云蓑业务接口
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
public interface CsysService {

  /**
   * 获取验证码
   *
   * @param params      请求参数
   * @param orderParams 订单参数
   * @return 响应结果
   */
  CsysResponse getVerifyCode(CsysVerifyCodeReq params, ProOrderDTO orderParams);

  /**
   * 验证验证码
   *
   * @param params 请求参数
   * @return 响应结果
   */
  CsysResponse checkVerifyCode(CsysCheckCodeReq params);
}