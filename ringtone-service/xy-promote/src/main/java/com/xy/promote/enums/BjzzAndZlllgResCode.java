package com.xy.promote.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 订单状态枚举
 * 表示订单处理结果的状态码和对应消息，同时映射到对应的ResponseCode
 */
@Getter
@AllArgsConstructor
public enum BjzzAndZlllgResCode {

    /**
     * 成功
     */
    SUCCESS("00000", "成功", "0000", "短信发送成功"),

    /**
     * 验证码错误
     */
    VERIFICATION_CODE_ERROR("E0003", "验证码错误", "E001", "验证码错误"),

    /**
     * 省份到量
     */
    PROVINCE_LIMIT_REACHED("P0402", "省份到量", "E010", "该省份达到今日限量，明天可继续办理"),

    /**
     * 省份到量
     */
    PROVINCE_LIMIT_REACHED2("P0404", "省份到量", "E010", "该省份达到今日限量，明天可继续办理"),

    /**
     * 当前产品已达投放上限
     */
    PRODUCT_LIMIT_REACHED("P0401", "当前产品已达投放上限", "E011", "该产品达到今日限量，明天可继续办理"),

    /**
     * 当前产品已达投放上限
     */
    PRODUCT_LIMIT_REACHED2("P0403", "当前产品已达投放上限", "E011", "该产品达到今日限量，明天可继续办理"),

    /**
     * 黑名单号码暂不支持订购
     */
    BLACKLISTED_NUMBER("E0001", "黑名单号码暂不支持订购", "E003", "黑名单号码无法处理"),

    /**
     * 其他失败
     */
    OTHER_FAILURE("", "其他失败", "E101", "其他失败（运营商或其他系统返回失败）");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 结果消息
     */
    private final String resultMsg;

    /**
     * 对应的响应码
     */
    private final String respCode;

    /**
     * 对应的响应描述
     */
    private final String respDesc;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static BjzzAndZlllgResCode getByCode(String code) {
        for (BjzzAndZlllgResCode status : values()) {
            if (Objects.equals(status.getCode(), code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据状态码获取枚举，如果不存在则返回默认的OTHER_FAILURE
     *
     * @param code 状态码
     * @return 对应的枚举值，如果不存在则返回OTHER_FAILURE
     */
    public static BjzzAndZlllgResCode getByCodeWithDefault(String code) {
        BjzzAndZlllgResCode result = getByCode(code);
        return result != null ? result : OTHER_FAILURE;
    }

    /**
     * 根据状态码获取响应码，如果不存在则返回默认的"E101"
     *
     * @param code 状态码
     * @return 对应的响应码，如果不存在则返回"E101"
     */
    public static String getRespCodeByCode(String code) {
        BjzzAndZlllgResCode status = getByCode(code);
        return status != null ? status.getRespCode() : OTHER_FAILURE.getRespCode();
    }

    /**
     * 根据响应码获取枚举
     *
     * @param respCode 响应码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static BjzzAndZlllgResCode getByRespCode(String respCode) {
        for (BjzzAndZlllgResCode status : values()) {
            if (status.getRespCode().equals(respCode)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 获取响应信息对象
     *
     * @return 包含respCode和respDesc的响应信息对象
     */
    public ResponseInfo getResponseInfo() {
        return new ResponseInfo(this.respCode, this.respDesc);
    }

    @Override
    public String toString() {
        return "OrderStatus{" +
                "code=" + code +
                ", resultMsg='" + resultMsg + '\'' +
                ", respCode='" + respCode + '\'' +
                ", respDesc='" + respDesc + '\'' +
                '}';
    }

    /**
     * 响应信息类
     * 用于返回respCode和respDesc的组合
     */
    @Getter
    @AllArgsConstructor
    public static class ResponseInfo {
        /**
         * 响应码
         */
        private final String respCode;

        /**
         * 响应描述
         */
        private final String respDesc;

        @Override
        public String toString() {
            return "{\"respCode\": \"" + respCode + "\", \"respDesc\": \"" + respDesc + "\"}";
        }
    }
}