package com.xy.promote.service;

import com.xy.promote.dto.ProOrderDTO;
import com.xy.promote.dto.jsgy.JsgyCheckCodeReq;
import com.xy.promote.dto.jsgy.JsgyResponse;
import com.xy.promote.dto.jsgy.JsgyVerifyCodeReq;

/**
 * 江苏冠优业务接口
 *
 * <AUTHOR>
 * @since 2025-06-12 11:37:56
 */
public interface JsgyService {

	/**
	 * 获取验证码
	 *
	 * @param params 请求参数
	 * @param request 请求对象
	 * @return 响应结果
	 */
	JsgyResponse getVerifyCode(JsgyVerifyCodeReq params, ProOrderDTO orderParams);

	/**
	 * 验证验证码
	 *
	 * @param params 请求参数
	 * @return 响应结果
	 */
	JsgyResponse checkVerifyCode(JsgyCheckCodeReq params);
}
