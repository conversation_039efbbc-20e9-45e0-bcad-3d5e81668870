package com.xy.promote.dto;

import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Builder
@Accessors(chain = true)
public class ProOrderDTO {
	/**
	 * 渠道编号
	 */
	private String channel;
	/**
	 * 手机号码
	 */
	private String phone;
	/**
	 * 渠道来源
	 */
	private String source;
	/**
	 * 推广产品
	 */
	private String pro;
	/**
	 * 请求地址
	 */
	private String url;
	/**
	 * User-Agent
	 */
	private String ua;
	/**
	 * 订单ID
	 * 唯一标识订单
	 */
	private String orderId;
	/**
	 * 回调参数
	 */
	private String callback;
	/**
	 * 平台
	 * 非必填
	 */
	private String platform;
	/**
	 * 项目ID
	 * 非必填
	 */
	private String projectid;
	/**
	 * 推广ID
	 * 非必填
	 */
	private String promotionid;
	
	/**
	 * 流量果请求DTO转换为ProOrderDTO实例
	 * @param reqDTO 流量果请求DTO对象
	 * @param pro 推广产品标识
	 * @return ProOrderDTO实例
	 */
	public static ProOrderDTO fromReqDTO(ZlllgMessageDTO reqDTO, String pro) {
			return ProOrderDTO.builder()
			.channel(reqDTO.getBiz().getChannelNo())
			.phone(reqDTO.getBiz().getMobileNo())
			.source("api91")
			.pro(pro)
			.callback(reqDTO.getHead().getTransId()) // 系统中暂时没有用到这个参数，暂且用TransId代替
			.url(reqDTO.getBiz().getSource().getPageUrl())
			.ua(reqDTO.getBiz().getSource().getUserAgent())
			.build();
	}
}
