package com.xy.promote.service;

import com.xy.promote.dto.ProOrderDTO;
import com.xy.promote.dto.hzchl.HzchlCheckCodeReq;
import com.xy.promote.dto.hzchl.HzchlResponse;
import com.xy.promote.dto.hzchl.HzchlVerifyCodeReq;

/**
 * 杭州铖晗联业务接口
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
public interface HzchlService {

    /**
     * 获取验证码
     *
     * @param params 请求参数
     * @param orderParams 订单参数
     * @return 响应结果
     */
    HzchlResponse getVerifyCode(HzchlVerifyCodeReq params, ProOrderDTO orderParams);

    /**
     * 验证验证码
     *
     * @param params 请求参数
     * @return 响应结果
     */
    HzchlResponse checkVerifyCode(HzchlCheckCodeReq params);
}
