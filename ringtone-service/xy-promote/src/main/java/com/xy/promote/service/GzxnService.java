package com.xy.promote.service;

import com.xy.promote.dto.gzxn.CheckCodeRequest;
import com.xy.promote.dto.gzxn.GzxnResponse;
import com.xy.promote.dto.gzxn.VerifyCodeRequest;

import java.util.Map;

/**
 * 广州馨宁业务接口
 *
 * <AUTHOR>
 * @since 2024/12/1
 */
public interface GzxnService {
	
	/**
	 * 获取验证码
	 *
	 * @param reqDTO  验证码请求参数
	 * @param params  前端请求参数
	 * @return API响应
	 */
	GzxnResponse getVerifyCode(VerifyCodeRequest reqDTO, Map<String, String> params);
	
	/**
	 * 验证短信验证码
	 *
	 * @param params 请求参数
	 * @return API响应
	 */
	GzxnResponse checkVerifyCode(CheckCodeRequest params);
}