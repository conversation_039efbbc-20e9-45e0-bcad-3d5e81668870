package com.xy.promote.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 签名验证注解
 * 应用于Controller层方法，自动进行签名校验
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface SignatureValidation {
  /**
   * 签名密钥，默认为空，在配置中统一指定
   */
  String secretKey() default "";

  /**
   * 签名字段在请求对象中的路径，默认为 head.sign
   */
  String signField() default "head.sign";

  /**
   * 清除签名字段后的路径（用于签名验证前需要置空签名字段）
   */
  String clearSignField() default "head.sign";

  /**
   * 是否强制要求签名验证
   * 如果为true，则请求必须包含签名且验证通过才能处理
   * 如果为false，则没有签名时跳过验证，有签名时验证
   * 默认为false，即有签名才验证
   */
  boolean required() default false;
}