package com.xy.promote.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 接口响应码枚举
 * 接口响应码(head->respCode)
 */
@Getter
@AllArgsConstructor
public enum ZlllgResponseCode {
    
    SUCCESS("0000", "请求成功", ""),
    APP_ERROR("E001", "应用错误", ""),
    PARAM_VALIDATION_FAILED("E002", "参数校验不通过", ""),
    SIGNATURE_ERROR("E003", "签名错误", ""),
    ORDER_NOT_EXIST("E004", "订单不存在", ""),
    E005("E005", "", ""),
    E006("E006", "", ""),
    E007("E007", "", ""),
    E008("E008", "", ""),
    E009("E009", "", ""),
    PROVINCE_DAILY_LIMIT("E010", "该省份达到今日限量，明天可继续办理", "将流量果平台该产品对应省份设置今日到量，明日恢复"),
    PRODUCT_DAILY_LIMIT("E011", "该产品达到今日限量，明天可继续办理", "将流量果平台该产品设置今日到量，明日恢复"),
    PRODUCT_OFFLINE("E012", "产品已下架，请暂停推广", "将流量果平台广告暂停"),
    PROVINCE_PRODUCT_OFFLINE("E013", "该省份已下架，暂停发展该省份", "将流量果平台该产品暂停该省份的定向"),
    E014("E014", "", ""),
    E015("E015", "", ""),
    E016("E016", "", ""),
    E017("E017", "", ""),
    OTHER_SYSTEM_FAILURE("E101", "其他失败（运营商或其他系统返回失败）", "");
    
    /**
     * 响应码
     */
    private final String code;
    
    /**
     * 响应描述
     */
    private final String description;
    
    /**
     * 流量果平台广告操作
     */
    private final String operation;
    
    /**
     * 根据响应码获取枚举
     * 
     * @param code 响应码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ZlllgResponseCode getByCode(String code) {
        for (ZlllgResponseCode respCode : values()) {
            if (respCode.getCode().equals(code)) {
                return respCode;
            }
        }
        return null;
    }
    
    @Override
    public String toString() {
        return "ResponseCode{" +
                "code='" + code + '\'' +
                ", description='" + description + '\'' +
                ", operation='" + operation + '\'' +
                '}';
    }
}