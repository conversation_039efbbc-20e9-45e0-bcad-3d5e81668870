package com.xy.promote.dto.zsgl;

import com.xy.promote.dto.zlllg.BizDTO;
import com.xy.promote.dto.zlllg.HeadDTO;
import com.xy.promote.dto.zlllg.SourceDTO;
import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/7/7 18:09
 */
@Data
@Accessors(chain = true)
@SuperBuilder
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ZsglSendSmsReq implements Serializable {
	/**
	 * 应用ID
	 */
	private String appId;
	/**
	 * 渠道号
	 */
	private String channelNo;
	/**
	 * 产品号
	 */
	private String productNo;
	/**
	 * 手机号
	 */
	private String mobileNo;
	/**
	 * 用户代理
	 */
	private String userAgent;
	/**
	 * 客户端IP
	 */
	private String clientIp;
	/**
	 * 应用包名
	 */
	private String appPackage;
	/**
	 * 应用名称
	 */
	private String appName;
	/**
	 * 平台
	 */
	private String platform;
	/**
	 * 页面URL
	 */
	private String pageUrl;

	/**
	 * 转换为ZlllgMessageDTO
	 *
	 * @return ZlllgMessageDTO对象
	 */
	public ZlllgMessageDTO toZlllgMessageDTO() {
		return ZlllgMessageDTO.builder()
				.head(
						HeadDTO.builder()
								.appId(this.appId)
								.build()
				)
				.biz(BizDTO.builder()
						.channelNo(this.channelNo)
						.productNo(this.productNo)
						.mobileNo(this.mobileNo)
						.source(
								SourceDTO.builder()
										.userAgent(this.userAgent)
										.clientIp(this.clientIp)
										.appPackage(this.appPackage)
										.appName(this.appName)
										.platform(this.platform)
										.pageUrl(this.pageUrl)
										.build()
						).build()
				).build();
		}
	}
