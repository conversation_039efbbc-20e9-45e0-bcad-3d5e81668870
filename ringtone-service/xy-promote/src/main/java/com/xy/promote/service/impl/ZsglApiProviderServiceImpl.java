package com.xy.promote.service.impl;

import com.xy.promote.dto.zjyt.CheckCodeRequest;
import com.xy.promote.dto.zjyt.ZjytResponse;
import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import com.xy.promote.enums.ZlllgOrderStatusCode;
import com.xy.promote.enums.ZlllgResponseCode;
import com.xy.promote.mapper.DliangInfoMapper;
import com.xy.promote.mapper.ProInfoProductMapper;
import com.xy.promote.service.ApiOrderService;
import com.xy.promote.service.ProOrderService;
import com.xy.promote.util.ZlllgResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 众视广联API服务实现
 * 不同的服务提供商实现不同的结构
 */
@Slf4j
@Service
public class ZsglApiProviderServiceImpl extends BaseApiProviderServiceImpl {

  /**
   * 该公司支持的产品编号列表
   */
  private static final String SUPPORTED_PRODUCT_NO = "DE";

  public ZsglApiProviderServiceImpl(ApiOrderService apiOrderService,
      ProInfoProductMapper proInfoProductMapper,
      ProOrderService proOrderService,
      DliangInfoMapper dliangInfoMapper) {
    super(apiOrderService, proInfoProductMapper, proOrderService, dliangInfoMapper);
  }

  @Override
  public String getSupportedProductNo() {
    return SUPPORTED_PRODUCT_NO;
  }

  @Override
  protected String getProIdentifier() {
    return "zsgl";
  }

  @Override
  public ZlllgMessageDTO sendSms(ZlllgMessageDTO reqDTO) {
    log.info("示例公司发送短信验证码API调用，待实现");
    // TODO: 实现该公司的短信发送逻辑

    return ZlllgResponseUtil.buildZlllgResponse(
        reqDTO,
        ZlllgResponseCode.SUCCESS.getCode(),
        "待实现",
        bizBuilder -> bizBuilder.mobileNo(reqDTO.getBiz().getMobileNo()));
  }

  @Override
  public ZlllgMessageDTO orderSubmit(ZlllgMessageDTO reqDTO) {
    String linkId = null;

    // 去请求浙江有田接口
    CheckCodeRequest codeRequest = CheckCodeRequest.builder()
        .linkId(linkId)
        .billNum(reqDTO.getBiz().getMobileNo())
        .code(reqDTO.getBiz().getSmsCode())
        .build();

    ZjytResponse zjytResponse = ZjytResponse.builder()
        .code("200")
        .build();

    String code = zjytResponse.getCode();
    String respCode = Objects.equals(code, "200") ? ZlllgResponseCode.SUCCESS.getCode()
        : ZlllgResponseCode.OTHER_SYSTEM_FAILURE.getCode();
    String respDesc = Objects.requireNonNull(ZlllgResponseCode.getByCode(respCode)).getDescription();

    // 响应众联流量果接口
    ZlllgMessageDTO res = ZlllgResponseUtil.buildZlllgResponse(
        reqDTO,
        respCode,
        respDesc,
        bizBuilder -> bizBuilder
            .orderId(linkId)
            .outOrderId(reqDTO.getBiz().getOutOrderId())
            .orderStatus(ZlllgOrderStatusCode.SUCCESS.getCode()));

    // 更新DTO对象存到数据库
    reqDTO.getHead().setRespCode(respCode);
    reqDTO.getBiz().setOrderId(linkId);
    // 更新信息到数据库
    updateVerifyCodeInfo(reqDTO, zjytResponse);
    return res;
  }

  @Override
  public ZlllgMessageDTO orderQuery(ZlllgMessageDTO reqDTO) {
    log.info("示例公司订单查询API调用，待实现");
    // TODO: 实现该公司的订单查询逻辑

    return ZlllgResponseUtil.buildZlllgResponse(
        reqDTO,
        ZlllgResponseCode.SUCCESS.getCode(),
        "待实现",
        bizBuilder -> bizBuilder.mobileNo(reqDTO.getBiz().getMobileNo()));
  }

  @Override
  public ZlllgMessageDTO recommend(ZlllgMessageDTO reqDTO) {
    log.info("示例公司前置校验API调用，待实现");
    // TODO: 实现该公司的前置校验逻辑

    return ZlllgResponseUtil.buildZlllgResponse(
        reqDTO,
        ZlllgResponseCode.SUCCESS.getCode(),
        "待实现",
        bizBuilder -> bizBuilder.mobileNo(reqDTO.getBiz().getMobileNo()));
  }
}