package com.xy.promote.service.impl;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.xy.promote.dto.zjyt.CheckCodeRequest;
import com.xy.promote.dto.zjyt.VerifyCodeRequest;
import com.xy.promote.dto.zjyt.ZjytResponse;
import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import com.xy.promote.entity.ProInfoProduct;
import com.xy.promote.enums.ZjytAndZlllgResCode;
import com.xy.promote.enums.ZlllgOrderStatusCode;
import com.xy.promote.enums.ZlllgResponseCode;
import com.xy.promote.mapper.DliangInfoMapper;
import com.xy.promote.mapper.ProInfoProductMapper;
import com.xy.promote.service.ApiOrderService;
import com.xy.promote.service.ProOrderService;
import com.xy.promote.service.ZjytService;
import com.xy.promote.util.ApiExceptionUtil;
import com.xy.promote.util.ZlllgResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 浙江有田API服务实现
 */
@Slf4j
@Service
public class ZjytApiProviderServiceImpl extends BaseApiProviderServiceImpl {

	private final ZjytService zjytService;

	public ZjytApiProviderServiceImpl(ApiOrderService apiOrderService,
			ZjytService zjytService,
			ProInfoProductMapper proInfoProductMapper,
			ProOrderService proOrderService,
			DliangInfoMapper dliangInfoMapper) {
		super(apiOrderService, proInfoProductMapper, proOrderService, dliangInfoMapper);
		this.zjytService = zjytService;
	}

	@Override
	public String getSupportedProductNo() {
		// 浙江有田支持的产品列表
		return "HA";
	}

	@Override
	protected String getProIdentifier() {
		return "zjyt";
	}

	@Override
	public ZlllgMessageDTO sendSms(ZlllgMessageDTO reqDTO) {
		// 进行省份到量检测和运营商检测
		checkDaoliang(reqDTO);

		// 查询并验证产品信息
		ProInfoProduct proInfoProduct = getProductInfo(reqDTO.getBiz().getProductNo());
		validateProductInfo(proInfoProduct, reqDTO);

		// 构建请求参数
		Map<String, String> request = buildMapRequestParams(reqDTO, proInfoProduct);

		// 发送请求到浙江有田
		ZjytResponse zjytResponse = zjytService.addOrder(VerifyCodeRequest.fromReqDTO(reqDTO), request);

		String code = zjytResponse.getCode();
		String linkId = Optional.ofNullable(zjytResponse.getData()).orElse(new HashMap<>()).getOrDefault("linkId",IdWorker.getTimeId());
		String respCode = ZjytAndZlllgResCode.getRespCodeByCode(Integer.parseInt(code));
		String respDesc = Optional.ofNullable(zjytResponse.getMsg()).orElse(ZjytAndZlllgResCode.getRespDescByResCode(respCode));
		// 众联流量果接口响应
		ZlllgMessageDTO res = ZlllgResponseUtil.buildZlllgResponse(reqDTO, respCode, respDesc,
				bizBuilder -> bizBuilder.extraInfo(MapUtil.builder().put("linkId", linkId).build()));
		// 更新DTO对象存到数据库
		reqDTO.getHead().setRespCode(code);
		reqDTO.getBiz().setOrderId(linkId);
		reqDTO.getBiz().setOrderStatus(respCode);
		// 保存信息到数据库
		saveSmsInfo(reqDTO, zjytResponse);
		return res;
	}

	@Override
	public ZlllgMessageDTO orderSubmit(ZlllgMessageDTO reqDTO) {
		String linkId = Optional.ofNullable(reqDTO.getBiz().getExtraInfo().get("linkId")).map(Object::toString)
				.orElse(null);
		ApiExceptionUtil.throwIfEmpty(linkId, reqDTO, ZlllgResponseCode.PARAM_VALIDATION_FAILED);
		// 去请求浙江有田接口
		CheckCodeRequest codeRequest = CheckCodeRequest.fromReqDTO(reqDTO, linkId);

		// 发送请求到浙江有田
		ZjytResponse zjytResponse = zjytService.submitCode(codeRequest);

		String code = zjytResponse.getCode();
		String respCode = Objects.equals(code, "200") ? ZlllgResponseCode.SUCCESS.getCode(): ZlllgResponseCode.OTHER_SYSTEM_FAILURE.getCode();
		String respDesc = Optional.ofNullable(zjytResponse.getMsg()).orElse(ZjytAndZlllgResCode.getRespDescByResCode(respCode));

		// 响应众联流量果接口
		ZlllgMessageDTO res = ZlllgResponseUtil.buildZlllgResponse(reqDTO, respCode, respDesc,
				bizBuilder -> bizBuilder
						.orderId(linkId)
						.outOrderId(reqDTO.getBiz().getOutOrderId())
						.orderStatus(ZlllgOrderStatusCode.PROCESSING.getCode()));
		// 更新DTO对象存到数据库
		reqDTO.getHead().setRespCode(code);
		reqDTO.getBiz().setOrderId(linkId);
		reqDTO.getBiz().setOrderStatus(respCode);
		// 更新信息到数据库
		updateVerifyCodeInfo(reqDTO, zjytResponse);
		return res;
	}

}