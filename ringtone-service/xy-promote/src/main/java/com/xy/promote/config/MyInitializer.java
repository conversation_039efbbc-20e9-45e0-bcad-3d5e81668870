package com.xy.promote.config;

import com.xy.base.core.util.DbUtil;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

@Component
public class MyInitializer implements ApplicationRunner {

    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 在应用程序启动完成后执行的初始化逻辑
        System.out.println("Application started. Performing initialization tasks...");
        DbUtil.init();
    }
}