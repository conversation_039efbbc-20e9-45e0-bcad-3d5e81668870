package com.xy.promote.dto.zlllg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 扩展信息实体类
 * 可以根据业务需要添加其他字段
 * 由于文档描述"内部数据不固定"，实际使用时可能需要添加更多字段
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class ExtraInfoDTO {
    /**
     * 短信验证码密钥
     * 示例中的值
     */
    private String smsKey;

    /**
     * linkId
     */
    private String linkId;

}
