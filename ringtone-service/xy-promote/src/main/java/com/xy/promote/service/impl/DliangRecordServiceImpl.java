package com.xy.promote.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.lib.migu.util.PhoneInfoUtil;
import com.xy.lib.migu.vo.PhoneLocationType;
import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import com.xy.promote.entity.DliangRecord;
import com.xy.promote.entity.ProInfoProduct;
import com.xy.promote.mapper.DliangRecordMapper;
import com.xy.promote.mapper.ProInfoProductMapper;
import com.xy.promote.service.DliangRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@RequiredArgsConstructor
public class DliangRecordServiceImpl extends ServiceImpl<DliangRecordMapper, DliangRecord> implements DliangRecordService{
    private final ProInfoProductMapper proInfo;
    
    @Override
    public void saveDliangRecord(ZlllgMessageDTO reqDTO, String province) {
        if(StrUtil.isBlank(province)){
            // 查询号码归属地和运营商
            PhoneLocationType phoneLocationType = Objects.requireNonNull(PhoneInfoUtil.phoneInfo(reqDTO.getBiz().getMobileNo()));
            province = phoneLocationType.getLocation().getProvince();
        }
	    // 查询pro
        ProInfoProduct proInfoProduct = proInfo.selectOne(new LambdaQueryWrapper<ProInfoProduct>().eq(ProInfoProduct::getProductNo, reqDTO.getBiz().getProductNo()));
        String pro = proInfoProduct.getPro();

        this.save(DliangRecord.builder()
                .phone(reqDTO.getBiz().getMobileNo())
                .channel(reqDTO.getBiz().getChannelNo())
                .source("llg91")
                .pro(pro)
                .notice(province)
                .build());
    }
}
