package com.xy.promote.vo;

import java.io.Serializable;

public class GengChuangCallbackRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    // 必须参数
    private Integer resCode;
    private Integer channelId;
    private String orderId;
    private String phone;
    private String extData;
    private String sign;

    // 可选参数
    private String efftime;
    private String description;

    // 无参构造函数
    public GengChuangCallbackRequest() {
    }

    // 全参构造函数
    public GengChuangCallbackRequest(Integer resCode, Integer channelId, String orderId, String phone, String sign, String extData, String efftime, String description) {
        this.resCode = resCode;
        this.channelId = channelId;
        this.orderId = orderId;
        this.phone = phone;
        this.sign = sign;
        this.extData = extData;
        this.efftime = efftime;
        this.description = description;
    }

    // Getter 和 Setter 方法
    public Integer getResCode() {
        return resCode;
    }

    public void setResCode(Integer resCode) {
        this.resCode = resCode;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getExtData() {
        return extData;
    }

    public void setExtData(String extData) {
        this.extData = extData;
    }

    public String getEfftime() {
        return efftime;
    }

    public void setEfftime(String efftime) {
        this.efftime = efftime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    @Override
    public String toString() {
        return "GengChuangCallbackRequest{" +
                "resCode=" + resCode +
                ", channelId=" + channelId +
                ", orderId='" + orderId + '\'' +
                ", phone='" + phone + '\'' +
                ", extData='" + extData + '\'' +
                ", efftime='" + efftime + '\'' +
                ", description='" + description + '\'' +
                ", sign='" + sign + '\'' +
                '}';
    }
}
