package com.xy.promote.dto.zlllg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors; /**
 * 用户来源实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class SourceDTO {
    /**
     * 用户代理信息
     */
    private String userAgent;
    
    /**
     * 客户端IP地址
     */
    private String clientIp;
    
    /**
     * 应用包名
     */
    private String appPackage;
    
    /**
     * 应用名称
     */
    private String appName;
    
    /**
     * 平台
     */
    private String platform;
    
    /**
     * 页面URL
     */
    private String pageUrl;
}
