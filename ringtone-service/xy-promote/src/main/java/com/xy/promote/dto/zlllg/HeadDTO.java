package com.xy.promote.dto.zlllg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors; /**
 * 报文头实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class HeadDTO {
    /**
     * 应用ID，由流量平台分配
     */
    private String appId;
    
    /**
     * 请求发起时间
     * 格式：yyyy-MM-dd HH24:mm:ss 举例：2024-12-01 23:59:30
     */
    private String timestamp;
    
    /**
     * 请求流水号（不可重复）
     */
    private String transId;
    
    /**
     * 消息签名
     * 参考md5签名鉴权章节
     */
    private String sign;

    /**
     * 返回状态码
     * 详见接口返回状态码
     * 必需
     */
    private String respCode;

    /**
     * 返回描述
     * 必需
     */
    private String respDesc;

}
