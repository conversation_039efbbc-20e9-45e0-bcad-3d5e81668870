package com.xy.promote.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 江苏冠优订单状态枚举
 * 表示订单处理结果的状态码和对应消息，同时映射到对应的ResponseCode
 */
@Getter
@AllArgsConstructor
public enum JsgyAndZlllgResCode {
  /*
   * {"msg":"没有找到合适的代码","status":"106"}
   * {"linkid":"136071821","msg":"获取验证码成功！","status":"1"}
   * {"linkid":"136096546","msg":"101","status":"0"}
   * 
   * {"linkid":"136096546","msg":"河南移动-存量验证码效验订单编号不存在","status":"0"}
   * {"linkid":"136096666","msg":"输入短信验证码有误。","status":"0"}
   */
  SUCCESS("1", "成功", "0000", "短信发送成功"),
  VERIFICATION_CODE_ERROR("0", "输入短信验证码有误", "E001", "验证码错误"),
  //PHONE_FORMAT_ERROR("0", "手机号格式错误", "E002", "手机号格式不正确"),
  //BLACKLISTED_NUMBER("0", "黑名单号码", "E003", "黑名单号码无法处理"),
  //CHANNEL_OFFLINE("0", "渠道已关闭", "E004", "渠道不存在或已关闭"),
  //PROVINCE_LIMIT_REACHED("0", "省份到量", "E010", "该省份达到今日限量，明天可继续办理"),
  //PRODUCT_LIMIT_REACHED("0", "产品到量", "E011", "该产品达到今日限量，明天可继续办理"),
  //PRODUCT_OFFLINE("0", "产品下线", "E012", "产品已下架，请暂停推广"),
  OTHER_FAILURE("0", "其他失败", "E101", "其他失败（运营商或其他系统返回失败）");

  /**
   * 江苏冠优状态码（1成功，0失败）
   */
  private final String status;

  /**
   * 江苏冠优结果消息
   */
  private final String msg;

  /**
   * 对应的众联流量果响应码
   */
  private final String respCode;

  /**
   * 对应的众联流量果响应描述
   */
  private final String respDesc;

  /**
   * 根据状态码获取枚举
   *
   * @param status 状态码
   * @return 对应的枚举值，如果不存在则返回null
   */
  public static JsgyAndZlllgResCode getByStatus(String status) {
    for (JsgyAndZlllgResCode resCode : values()) {
      if (resCode.getStatus().equals(status)) {
        return resCode;
      }
    }
    return null;
  }

  /**
   * 根据状态码获取枚举，不存在则返回默认枚举
   *
   * @param status 状态码
   * @return 对应的枚举值，如果不存在则返回默认枚举
   */
  public static JsgyAndZlllgResCode getByStatusWithDefault(String status) {
    JsgyAndZlllgResCode resCode = getByStatus(status);
    return resCode != null ? resCode : OTHER_FAILURE;
  }

  /**
   * 根据状态码获取响应码，不存在则返回默认响应码
   *
   * @param status 状态码
   * @return 对应的响应码，如果不存在则返回默认响应码
   */
  public static String getRespCodeByStatus(String status) {
    JsgyAndZlllgResCode resCode = getByStatus(status);
    return resCode != null ? resCode.getRespCode() : OTHER_FAILURE.getRespCode();
  }

  /**
   * 根据响应码获取响应描述，不存在则返回默认响应描述
   * 
   * @param respCode 响应码
   * @return 对应的响应描述，如果不存在则返回默认响应描述
   */
  public static String getRespDescByRespCode(String respCode) {
    for (JsgyAndZlllgResCode resCode : values()) {
      if (resCode.getRespCode().equals(respCode)) {
        return resCode.getRespDesc();
      }
    }
    return OTHER_FAILURE.getRespDesc();
  }
}
