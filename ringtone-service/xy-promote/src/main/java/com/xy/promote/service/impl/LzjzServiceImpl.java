package com.xy.promote.service.impl;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xy.base.core.constant.ResultCodeConsts;
import com.xy.base.core.exception.AppException;
import com.xy.promote.dto.lzjz.LzjzCheckCodeRequest;
import com.xy.promote.dto.lzjz.LzjzResponse;
import com.xy.promote.dto.lzjz.LzjzVerifyCodeRequest;
import com.xy.promote.service.LzjzService;
import com.xy.promote.util.PromoteUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class LzjzServiceImpl implements LzjzService {
	
	private static final String VERIFY_CODE_API = "http://121.43.210.191/data/inter/loadcodeV2";
	private static final String CHECK_CODE_API = "http://121.43.210.191/data/inter/paysubmit";
	
	private final ObjectMapper mapper;
	
	/**
	 * 调用乐只君子接口 - 获取验证码
	 *
	 * @param request 请求参数
	 * @param params  额外参数
	 * @return API响应
	 */
	@Override
	public LzjzResponse getVerifyCode(LzjzVerifyCodeRequest request, Map<String, String> params) {
		Map<String, String> body = new HashMap<>();
		body.put("biz", request.getBiz());
		body.put("ditch", request.getDitch());
		body.put("mobile", request.getMobile());
		body.put("cpparam", request.getCpparam());
		body.put("ip_address", request.getIpAddress());
		body.put("appPackage", request.getAppPackage());
		body.put("appName", request.getAppName());
		body.put("sourceUrl", request.getSourceUrl());
		body.put("userAgent", request.getUserAgent());
		
		LzjzResponse response = sendRequest(VERIFY_CODE_API, body, params, true);
		
		return response;
	}
	
	/**
	 * 调用乐只君子接口 - 验证短信验证码
	 *
	 * @param request 请求参数
	 * @param params  额外参数
	 * @return API响应
	 */
	@Override
	public LzjzResponse checkVerifyCode(LzjzCheckCodeRequest request, Map<String, String> params) {
		Map<String, String> body = new HashMap<>();
		body.put("orderid", request.getOrderid());
		body.put("vcode", request.getVcode());
		
		return sendRequest(CHECK_CODE_API, body, params, false);
	}
	
	/**
	 * 发送API请求并处理响应
	 *
	 * @param url    API地址
	 * @param body   请求体
	 * @param params 请求参数
	 * @param record 是否记录订单
	 * @return API响应
	 */
	private LzjzResponse sendRequest(String url, Map<String, String> body, Map<String, String> params, boolean record) {
		String requestUrl = url + "?" + HttpUtil.toParams(body);
		String phone = body.get("mobile");
		
		log.info("准备请求：{}", requestUrl);
		long start = System.currentTimeMillis();
		
		try (HttpResponse response = HttpUtil.createGet(requestUrl)
				.contentType("application/json")
				.execute()) {
			
			String responseBodyStr = response.body();
			log.info("interfaceAction {},{}", requestUrl, responseBodyStr);
			
			// 记录API调用日志
			PromoteUtils.insertLogApi(phone, requestUrl, "", responseBodyStr, "GET",
					(int) (System.currentTimeMillis() - start));
			
			// 判断response的statusCode
			if (ResultCodeConsts.SUCCESS != response.getStatus()) {
				log.error("用户：{}, 接口：{},访问失败状态码：{}", phone, requestUrl, response.getStatus());
				throw new AppException("请求接口失败，状态码：" + response.getStatus());
			}
			
			LzjzResponse res = mapper.readValue(responseBodyStr, LzjzResponse.class);
			
			// 处理响应结果
			if (res.getCode().equals(1)) {
				if (record && res.getOrderid() != null) {
					// 记录订单信息
					params.put("orderId", res.getOrderid());
					params.put("ua", body.get("userAgent"));
					try {
						PromoteUtils.insertProOrder(params);
					} catch (Exception e) {
						log.error("插入乐只君子订单失败", e);
					}
				}
			}
			return res;
		} catch (JsonProcessingException e) {
			throw new RuntimeException("解析响应失败", e);
		}
	}
}