package com.xy.promote.util;

import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

@Slf4j
public class ZlllgSignUtil {

    // 扁平化方法
    public static Map<String, Object> flattenParams(Object params, String parentKey, String sep) {
        Map<String, Object> items = new LinkedHashMap<>();

        if (params instanceof Map) {
            for (Map.Entry<?, ?> entry : ((Map<?, ?>) params).entrySet()) {
                String newKey = parentKey.isEmpty() ? entry.getKey().toString() : parentKey + sep + entry.getKey();
                items.putAll(flattenParams(entry.getValue(), newKey, sep));
            }
        } else if (params instanceof List) {
            List<?> listParams = (List<?>) params;
            for (int i = 0; i < listParams.size(); i++) {
                String newKey = parentKey + "[" + i + "]";
                items.putAll(flattenParams(listParams.get(i), newKey, sep));
            }
        } else {
            items.put(parentKey, params);
        }

        return items;
    }

    // 签名生成方法
    public static String generateSignature(Map<String, Object> params, String secretKey) {
        //log.debug("开始生成签名，入参: params={}, secretKey={}", params, secretKey);

        Map<String, Object> flatParams = flattenParams(params, "", ".");
        //log.debug("扁平化后的参数: {}", flatParams);

        Map<String, Object> sortedResultParams = new LinkedHashMap<>();

        // 先对 flatParams 进行排序并用 sortedResultParams 来记录
        List<String> keys = new ArrayList<>(flatParams.keySet());
        //log.debug("排序前的key列表: {}", keys);

        keys.sort((key1, key2) -> {
            // 自定义比较: 先按主键比较，再按副键比较
            String[] parts1 = key1.split("\\.");
            String[] parts2 = key2.split("\\.");

            for (int i = 0; i < Math.min(parts1.length, parts2.length); i++) {
                int compare = parts1[i].compareTo(parts2[i]);
                if (compare != 0) {
                    return compare;
                }
            }
            return Integer.compare(parts1.length, parts2.length); // 长度比较
        });
        //log.debug("排序后的key列表: {}", keys);

        for (String key : keys) {
            sortedResultParams.put(key, flatParams.get(key));
        }
        //log.debug("排序后的参数: {}", sortedResultParams);

        // 拼接原始签名字符串
        StringBuilder rawString = new StringBuilder();
        for (Map.Entry<String, Object> entry : sortedResultParams.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value != null) { // 忽略 null 值
                String encodedKey = encode(key).toString();
                String encodedValue = encode(value).toString();
                rawString.append(encodedKey).append("=").append(encodedValue).append("&");
                //log.debug("追加参数: {}={}", encodedKey, encodedValue);
            }
        }

        rawString.append(secretKey);
        //log.debug("原始签名字符串: {}", rawString);

        String signature = sha256Hmac(rawString.toString(), secretKey).toUpperCase();
        //log.debug("生成的签名: {}", signature);

        return signature;
    }

    /**
     * url编码
     *
     * @param value
     * @return
     */
    public static Object encode(Object value) {
        try {
            String encoded = URLEncoder.encode(value.toString(), "UTF-8");
            //log.debug("URL编码 {} -> {}", value, encoded);
            return encoded;
        } catch (UnsupportedEncodingException e) {
            log.error("URL编码异常, value={}", value, e);
            return value; // 或者返回原值，让调用者判断
        }
    }

    // HMAC SHA256 签名生成
    public static String sha256Hmac(String data, String key) {
        try {
            //log.debug("SHA256HMAC 输入数据: {}", data);
            javax.crypto.Mac mac = javax.crypto.Mac.getInstance("HmacSHA256");
            mac.init(new javax.crypto.spec.SecretKeySpec(key.getBytes(), "HmacSHA256"));
            byte[] hash = mac.doFinal(data.getBytes());
            String result = bytesToHex(hash);
            //log.debug("SHA256HMAC 结果: {}", result);
            return result;
        } catch (Exception e) {
            log.error("生成HMAC SHA256签名失败", e);
            throw new RuntimeException("Could not generate HMAC hash", e);
        }
    }

    // 将字节数组转换为十六进制字符串
    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1)
                hexString.append('0');
            hexString.append(hex);
        }
        return hexString.toString();
    }
}