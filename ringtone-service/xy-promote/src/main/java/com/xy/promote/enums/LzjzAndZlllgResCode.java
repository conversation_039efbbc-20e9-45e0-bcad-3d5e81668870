package com.xy.promote.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 乐只君子响应码映射枚举
 * 将乐只君子的响应码映射到众联流量果的响应码和描述
 */
@Getter
@AllArgsConstructor
public enum LzjzAndZlllgResCode {

  SUCCESS(1, "成功", "0000", "短信发送成功"),
  PROVINCE_LIMIT_REACHED(1001, "省份到量", "E010", "该省份达到今日限量，明天可继续办理"),
  //MOBILE_AUTH_FAILED(10, "号码认证失败", "E101", "其他失败（运营商或其他系统返回失败）"),
  OTHER_FAILURE(-1, "其他失败", "E101", "其他失败（运营商或其他系统返回失败）");

  /**
   * 乐只君子状态码
   */
  private final int code;

  /**
   * 乐只君子结果消息
   */
  private final String resultMsg;

  /**
   * 对应的众联流量果响应码
   */
  private final String respCode;

  /**
   * 对应的众联流量果响应描述
   */
  private final String respDesc;

  /**
   * 根据乐只君子状态码获取枚举
   *
   * @param code 乐只君子状态码
   * @return 对应的枚举值，如果不存在则返回null
   */
  public static LzjzAndZlllgResCode getByCode(int code) {
    for (LzjzAndZlllgResCode status : values()) {
      if (status.getCode() == code) {
        return status;
      }
    }
    return null;
  }

  /**
   * 根据乐只君子状态码获取枚举，如果不存在则返回默认的OTHER_FAILURE
   *
   * @param code 乐只君子状态码
   * @return 对应的枚举值，如果不存在则返回OTHER_FAILURE
   */
  public static LzjzAndZlllgResCode getByCodeWithDefault(int code) {
    LzjzAndZlllgResCode result = getByCode(code);
    return result != null ? result : OTHER_FAILURE;
  }

  /**
   * 根据乐只君子状态码获取众联流量果响应码，如果不存在则返回默认的"E101"
   *
   * @param code 乐只君子状态码
   * @return 对应的众联流量果响应码，如果不存在则返回"E101"
   */
  public static String getRespCodeByCode(int code) {
    LzjzAndZlllgResCode status = getByCode(code);
    return status != null ? status.getRespCode() : OTHER_FAILURE.getRespCode();
  }

  /**
   * 根据众联流量果响应码获取响应描述
   *
   * @param respCode 众联流量果响应码
   * @return 对应的响应描述，如果不存在则返回默认值
   */
  public static String getRespDescByResCode(String respCode) {
    for (LzjzAndZlllgResCode status : values()) {
      if (status.getRespCode().equals(respCode)) {
        return status.getRespDesc();
      }
    }
    return OTHER_FAILURE.getRespDesc();
  }

  /**
   * 根据众联流量果响应码获取枚举
   *
   * @param respCode 众联流量果响应码
   * @return 对应的枚举值，如果不存在则返回null
   */
  public static LzjzAndZlllgResCode getByRespCode(String respCode) {
    for (LzjzAndZlllgResCode status : values()) {
      if (status.getRespCode().equals(respCode)) {
        return status;
      }
    }
    return null;
  }

  /**
   * 获取响应信息对象
   *
   * @return 包含respCode和respDesc的响应信息对象
   */
  public ResponseInfo getResponseInfo() {
    return new ResponseInfo(this.respCode, this.respDesc);
  }

  @Override
  public String toString() {
    return "LzjzResCode{" +
        "code=" + code +
        ", resultMsg='" + resultMsg + '\'' +
        ", respCode='" + respCode + '\'' +
        ", respDesc='" + respDesc + '\'' +
        '}';
  }

  /**
   * 响应信息类
   * 用于返回respCode和respDesc的组合
   */
  @Getter
  @AllArgsConstructor
  public static class ResponseInfo {
    /**
     * 响应码
     */
    private final String respCode;

    /**
     * 响应描述
     */
    private final String respDesc;

    @Override
    public String toString() {
      return "{\"respCode\": \"" + respCode + "\", \"respDesc\": \"" + respDesc + "\"}";
    }
  }
}