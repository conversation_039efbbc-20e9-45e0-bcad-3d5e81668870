package com.xy.promote.util;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.xy.base.core.util.DbUtil;

import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class TimeUtil {

    public static String currentDate() {
        // Get current date
        LocalDate currentDate = LocalDate.now();

        // Define date formatter for desired format "yyyy-MM-dd"
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // Format current date as string
        return currentDate.format(formatter);
    }

    // 获取当前时间的节假日性质：公假日：0，周末：1，工作日：-1，注意：目前仅收集了2024年数据
    public static int holidayTag(){
        String dt = currentDate();
        try {
            Entity result = Db.use().queryOne("SELECT * FROM holiday WHERE date = ?", dt);
            if (result == null) {
                return -1;
            }else{
                return result.getInt("weekend");
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return -1;
    }
}
