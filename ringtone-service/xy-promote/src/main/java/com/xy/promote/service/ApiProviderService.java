package com.xy.promote.service;

import com.xy.promote.dto.zlllg.ZlllgMessageDTO;

/**
 * 标准化API服务提供商接口
 * 不同公司的接口实现需要实现此接口
 */
public interface ApiProviderService {

    /**
     * 获取该服务提供商支持的产品编号
     * 
     * @return 产品编号
     */
    String getSupportedProductNo();

    /**
     * 发送短信验证码
     *
     * @param reqDTO 请求DTO
     * @return 响应DTO
     */
    ZlllgMessageDTO sendSms(ZlllgMessageDTO reqDTO);

    /**
     * 业务订购办理
     *
     * @param reqDTO 请求DTO
     * @return 响应DTO
     */
    ZlllgMessageDTO orderSubmit(ZlllgMessageDTO reqDTO);

    /**
     * 订单状态查询
     *
     * @param reqDTO 请求DTO
     * @return 响应DTO
     */
    ZlllgMessageDTO orderQuery(ZlllgMessageDTO reqDTO);

    /**
     * 前置校验（推荐接口）
     *
     * @param reqDTO 请求DTO
     * @return 响应DTO
     */
    ZlllgMessageDTO recommend(ZlllgMessageDTO reqDTO);
}