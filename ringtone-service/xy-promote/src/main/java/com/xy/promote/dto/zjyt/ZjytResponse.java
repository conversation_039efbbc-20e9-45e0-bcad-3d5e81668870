package com.xy.promote.dto.zjyt;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.Map;

/**
 * 浙江有田API响应实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Builder
@Accessors(chain = true)
public class ZjytResponse {
    /**
     * 响应状态码
     */
  private String code;
    /**
     * 响应消息
     */
  private String msg;
    /**
     * 响应数据
     */
  private Map<String, String> data;
}