package com.xy.promote.controller;

import cn.hutool.extra.servlet.ServletUtil;
import com.xy.promote.dto.lzjz.LzjzCheckCodeRequest;
import com.xy.promote.dto.lzjz.LzjzResponse;
import com.xy.promote.dto.lzjz.LzjzVerifyCodeRequest;
import com.xy.promote.service.LzjzService;
import com.xy.promote.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Optional;

/**
 * 乐只君子API控制器
 *
 * <AUTHOR>
 * @since 2024/11/21 13:34
 */
@Slf4j
@RestController
@RequestMapping("/lzjz")
@RequiredArgsConstructor
public class LzjzController {
	
	private final LzjzService lzjzService;
	
	/**
	 * 获取验证码接口
	 */
	@PostMapping("/getVerifyCode")
	public LzjzResponse getVerifyCode(@RequestParam HashMap<String, String> params, HttpServletRequest request) {
		LzjzVerifyCodeRequest req = LzjzVerifyCodeRequest.builder()
				.biz(params.get("biz"))
				.ditch(params.get("ditch"))
				.mobile(params.get("phone"))
				.cpparam(Optional.ofNullable(params.get("cpparam")).orElse("juliang"))
				.ipAddress(ServletUtil.getClientIP(request))
				.appPackage(StringUtil.extractPackageName(request.getHeader("user-agent")))
				.appName("巨量")
				.sourceUrl(params.get("url"))
				.userAgent(request.getHeader("user-agent"))
				.build();
		
		return lzjzService.getVerifyCode(req, params);
	}
	
	/**
	 * 验证验证码接口
	 */
	@PostMapping("/checkVerifyCode")
	public LzjzResponse checkVerifyCode(@RequestParam HashMap<String, String> params, HttpServletRequest request) {
		LzjzCheckCodeRequest req = LzjzCheckCodeRequest.builder()
				.orderid(params.get("orderid"))
				.vcode(params.get("vcode"))
				.build();
		
		return lzjzService.checkVerifyCode(req, params);
	}
}
