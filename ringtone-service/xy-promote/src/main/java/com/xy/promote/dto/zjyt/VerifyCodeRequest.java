package com.xy.promote.dto.zjyt;

import cn.hutool.core.util.StrUtil;
import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * 验证码请求实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Builder
@Accessors(chain = true)
public class VerifyCodeRequest {
    /**
     * 业务代码
     */
    private String releaseNo;
    /**
     * 手机号码
     */
    private String billNum;
    /**
     * 订购用户IP
     */
    private String userIp;
    /**
     * 订购用户User-Agent
     */
    private String userAgent;
    /**
     * 订购用户媒体名称
     */
    private String mediaName;
    /**
     * 订购用户包名
     */
    private String packageName;

    /**
     * 从ZlllgMessageDTO创建VerifyCodeRequest实例
     * 
     * @param reqDTO 请求DTO对象
     * @return VerifyCodeRequest实例
     */
    public static VerifyCodeRequest fromReqDTO(ZlllgMessageDTO reqDTO) {
        String appPackage = StrUtil.emptyToDefault(reqDTO.getBiz().getSource().getAppPackage(), reqDTO.getBiz().getSource().getUserAgent());
        return VerifyCodeRequest.builder()
                .billNum(reqDTO.getBiz().getMobileNo())
                .userIp(reqDTO.getBiz().getSource().getClientIp())
                .userAgent(reqDTO.getBiz().getSource().getUserAgent())
                .mediaName(reqDTO.getBiz().getSource().getPlatform())
                .packageName(appPackage)
                .build();
    }
}