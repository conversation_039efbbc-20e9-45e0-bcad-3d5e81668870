package com.xy.promote.service.impl;

import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xy.base.core.constant.ResultCodeConsts;
import com.xy.base.core.enhancer.JacksonMaker;
import com.xy.base.core.exception.AppException;
import com.xy.promote.dto.bjzz.BjzzResponse;
import com.xy.promote.dto.bjzz.BjzzVerifyCodeRequest;
import com.xy.promote.dto.zjyt.CheckCodeRequest;
import com.xy.promote.dto.zjyt.VerifyCodeRequest;
import com.xy.promote.service.BjzzService;
import com.xy.promote.util.PromoteUtils;
import com.xy.promote.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class BjzzServiceImpl implements BjzzService {

    private static final String ORDER_API = "https://xcx.zzhrr.com/prod-api/client/verificationCode";
    private static final String SUBMIT_API = "https://xcx.zzhrr.com/prod-api/client/placeOrder";
    private static final String RELEASE_NO = "166";

    private final ObjectMapper mapper;

    @Override
    public BjzzResponse getVerifyCode(Map<String, String> params, HttpServletRequest request) {
        BjzzVerifyCodeRequest requestBody = new BjzzVerifyCodeRequest();

        requestBody.setLaunchId(RELEASE_NO);
        requestBody.setIp(ServletUtil.getClientIP(request));
        requestBody.setPhoneNumber(params.get("phone"));
        requestBody.setUa(request.getHeader("user-agent"));
        requestBody.setPlatformName("巨量");
        requestBody.setAppPackageName(StringUtil.extractPackageName(request.getHeader("user-agent")));

        return sendRequest(params.get("phone"), ORDER_API, requestBody, params);
    }

    @Override
    public BjzzResponse getVerifyCode(VerifyCodeRequest request, Map<String, String> params) {
        BjzzVerifyCodeRequest requestBody = new BjzzVerifyCodeRequest();

        requestBody.setLaunchId(RELEASE_NO);
        requestBody.setIp(request.getUserIp());
        requestBody.setPhoneNumber(params.get("phone"));
        requestBody.setUa(request.getUserAgent());
        requestBody.setPlatformName(request.getMediaName());
        requestBody.setAppPackageName(request.getPackageName());

        return sendRequest(params.get("phone"), ORDER_API, requestBody, params);
    }

    @Override
    public BjzzResponse checkVerifyCode(Map<String, String> params) {
        HashMap<String, String> requestBody = new HashMap<>();

        requestBody.put("launchId", RELEASE_NO);
        requestBody.put("verifyCode", params.get("code"));
        requestBody.put("orderId", params.get("orderId"));

        return sendRequest(params.get("billNum"), SUBMIT_API, requestBody, params);
    }

    @Override
    public BjzzResponse checkVerifyCode(CheckCodeRequest codeRequest) {
        HashMap<String, String> requestBody = new HashMap<>();

        requestBody.put("launchId", RELEASE_NO);
        requestBody.put("verifyCode", codeRequest.getCode());
        requestBody.put("orderId", codeRequest.getLinkId());

        return sendRequest(codeRequest.getBillNum(), SUBMIT_API, requestBody, null);
    }

    /**
     * 发送API请求并处理响应
     *
     * @param phone       电话号码
     * @param url         API地址
     * @param requestBody 请求体
     * @param params      请求参数
     * @return API响应
     */
    private BjzzResponse sendRequest(String phone, String url, Object requestBody, Map<String, String> params) {
        // 将请求参数转换为 JSON 格式
        String jsonRequestBody;
        try {
            jsonRequestBody = mapper.writeValueAsString(requestBody);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON序列化失败", e);
        }

        log.info("准备请求：{}", url);
        long start = System.currentTimeMillis();
        try (HttpResponse response = HttpUtil.createPost(url)
                .contentType("application/json")
                .body(jsonRequestBody)
                .execute()) {
            String responseBodyStr = response.body();
            log.info("interfaceAction {},{}", url, responseBodyStr);
            PromoteUtils.insertLogApi(phone, url, jsonRequestBody, responseBodyStr, "POST", (int) (System.currentTimeMillis() - start));

            // 判断response的statusCode
            if (ResultCodeConsts.SUCCESS != response.getStatus()) {
                log.error("用户：{}, 接口：{},访问失败状态码：{}", phone, url, response.getStatus());
                throw new AppException("请求接口失败，状态码：" + response.getStatus());
            }
            BjzzResponse res = mapper.readValue(responseBodyStr, BjzzResponse.class);
            // 如果访问的是下发验证码接口，则将linkId保存到数据库中
            JsonNode node = JacksonMaker.uniMapper().readTree(res.getMsg());
            JsonNode code = node.get("code");
            log.info(">>>>>{},{},{}", ORDER_API, url, code.textValue());
            if (ORDER_API.equals(url) && null != code && "00000".equals(code.textValue())) {
                params.put("orderId", node.get("orderId").textValue());
                params.put("ua", requestBody instanceof BjzzVerifyCodeRequest ? ((BjzzVerifyCodeRequest) requestBody).getUa() : "");
                try {
                    PromoteUtils.insertProOrder(params);
                } catch (Exception e) {
                    log.error("插入北京众智汇融短信订单失败", e);
                }
            }
            return res;
        } catch (JsonProcessingException e) {
            throw new RuntimeException("解析响应失败", e);
        }
    }
} 