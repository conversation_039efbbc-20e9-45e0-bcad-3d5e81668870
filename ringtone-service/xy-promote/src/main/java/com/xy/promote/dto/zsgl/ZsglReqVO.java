package com.xy.promote.dto.zsgl;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 实体类 - 包含渠道和业务相关信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Builder
@Accessors(chain = true)
public class ZsglReqVO implements Serializable {
    
    /** 渠道标识 */
    @NotBlank(message = "渠道标识不能为空")
    private String channel;
    
    /** 来源标识 */
    @NotBlank(message = "来源标识不能为空")
    private String source;
    
    /** 网址 */
    //@NotBlank(message = "url不能为空")
    private String url;
    
    /** 电话号码 */
    @NotBlank(message = "电话号码不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "电话号码格式不正确")
    private String phone;
    
    /** 渠道编号 */
    @NotBlank(message = "渠道编号不能为空")
    private String channelNo;
    
    /** 价格 */
    @NotNull(message = "价格不能为空")
    @Positive(message = "价格必须为正数")
    private String price;
    
    /** 自定义参数 */
    private String cpparam;
}