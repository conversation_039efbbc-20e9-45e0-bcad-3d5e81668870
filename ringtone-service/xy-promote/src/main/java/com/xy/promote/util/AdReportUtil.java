package com.xy.promote.util;

import cn.hutool.core.util.IdUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.xy.base.core.util.DbUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class AdReportUtil {
    private static final String delimiter = "#";

    private static String getAllParams(Map<String, String> params, List<String> ignoreList, boolean printParams) {
        List<String> list = new ArrayList<>();
        if (printParams) {
            log.info("params:");
        }
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (printParams) {
                log.info("{}: {}", entry.getKey(), entry.getValue());
            }
            if (null != ignoreList && ignoreList.contains(entry.getKey())) {
                continue;
            }
            if ("url".equals(entry.getKey())) {
                continue;
            }
            list.add(String.format("%s=%s", entry.getKey(), entry.getValue()));
        }
        return String.join("&", list);
    }

    private static String getParam(Map<String, String> params, String key) {
        if (params.containsKey(key)) {
            return params.get(key);
        }
        return null;
    }

    private static String getCallbackValue(String channel, String source, String platform, String paramName, Map<String, String> params) {
        /*
        if ("shfy".equals(channel) && "dy01".equals(source)) {
            final String[] split = paramName.split(",");
            List<String> list = new ArrayList<>();
            for (String s : split) {
                list.add(params.get(s));
            }
            return String.join(delimiter, list);
        }
         */
        //clickid：对于推广Web落地页而言，在腾讯广告推广时，每次点击都会生成一个 click_id，在跳转到落地页时，会将 click_id 作为参数传入对应的 URL 中。对于腾讯广告非微信流量为URL中的参数qz_gdt的值，对于微信流量为URL中的参数gdt_vid的值。
        if ("gdt".equals(platform)) {
            if (params.containsKey("qz_gdt")) {
                return params.get("qz_gdt");
            } else if (params.containsKey("gdt_vid")) {
                return params.get("gdt_vid");
            }
        }
        return params.get(paramName);
    }

    private static void saveChannelReport(String callback, String cellphone, String source, String channel, String platform, int report_status, String ring, String params, String url, String pro, String projectid, String promotionid) {
        // 不再保存params
        // 允许的params最大长度为3300
        // if (params.length() > 3300) {
        //     params = params.substring(0, 3300);
        // }
        Entity entity = Entity.create("channel_report")
                .set("id", IdUtil.getSnowflake().nextId())
                .set("channel", channel)
                .set("report_status", report_status)
                .set("callback", callback)
                .set("phone", cellphone)
                .set("platform", platform)
                .set("source", source)
                .set("projectid", projectid)
                .set("promotionid", promotionid)
                .set("ring", ring)
                .set("url", url)
                .set("pro", pro)
                .set("params", params);
        try {
            log.info(entity.toString());
            Db.use().insert(entity);
        } catch (SQLException e) {
            e.printStackTrace();
            log.error("捕获到异常: ", e);
        }
    }

    // 仅保留zsgl流量池的正常回传
    public static boolean doSuccessReport(String callback, String pro, String platform, String url) {
        try {
            if ("ks".equals(platform)) {
                // doKuaishouReport(callback);
                return false;
            } else if ("llg".equals(platform)) {
                // doLiuliangguoReport(callback);
                return false;
            } else if ("llgapi".equals(platform)) {
                // doLiuliangguoAPIReport(callback);
                return false;
            } else if ("xwapi".equals(platform)) {
                // doXwAPIReportV2(callback, pro, true);
                return false;
            } else if("zsgl".equals(platform)){
                doZsglReport(callback, "sx7x6gersb");
                return true;
            }
            if ("douyin".equals(platform)) {
                // 根据文档：https://bytedance.larkoffice.com/docs/doccnie3Url88jhwSfrkViah1nq，该回传为老版本回传，
                // doDouyinReport2 为新版本回传，因此，仅保留新版本，去掉老版本回传
                // doDouyinReport2(callback); // 此处不再做回传动作，交由Python脚本：pros_huichuan
                return false;
            }
            if ("gdt".equals(platform)) {
                // doGuangdiantongReport(callback, url);
                return false;
            }
            if ("gzqh".equals(platform)) {
                // doGzqhAPIReport(callback, "subscribe");
                return false;
            }
            log.error("非法的回传平台：{} with callback {}", platform, callback);
        } catch (Exception e) {
            log.error("捕获到异常: ", e);
            e.printStackTrace();
        }
        return false;
    }

    public static void doFailReport(String callback, String channel, String source, String pro, String platform, String phone, String params, String url) {
        try {
            if ("xwapi".equals(platform)) {
                // doXwAPIReport(callback);
                doXwAPIReportV2(callback, pro, false);
                return;
            }
            log.error("非法的失败回传平台：{} with callback {}", platform, callback);
        } catch (Exception e) {
            log.error("捕获到异常: ", e);
            e.printStackTrace();
        }
    }

    // 处理退订回传
    public static boolean doWithdrawReport(String callback, String platform, String pro) {
        try {
            if ("gzqh".equals(platform)) {
                doGzqhAPIReport(callback, "withdraw");
                return true;
            }
            // log.error("非法的回传平台：{} with callback {}", platform, callback);
        } catch (Exception e) {
            log.error("捕获到异常: ", e);
            e.printStackTrace();
        }
        return false;
    }

    private static void doZhonglianDouyinReport(String uuid, String phone) {
        log.info("invoke doZhonglianDouyinReport using uuid={}, phone={}", uuid, phone);
        try {
            Entity order = Db.use().queryOne("select * from subscription_order where phone = ? order by create_time desc", phone);
            String orderId = order.getStr("order_number");
            String createTime = order.getStr("create_time");

            String apiUrl = "https://api.wi-fi.cn/open_api/oceanengine/micro-app/push-order";

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体
            String status = "已提交";
            String detailUrl = encodeValue("https://mtq9814.zhonglianhuashu.com/app/detail.html?orderId=" + orderId);
            String img = encodeValue("https://mtq9814.zhonglianhuashu.com/app/static/order_slt.jpg");
            String title = "视频彩铃";

            String jsonBody = "{\"uuid\":\"" + uuid + "\",\"order_id\":\"" + orderId + "\",\"create_time\":\"" + createTime + "\",\"status\":\"" + status + "\",\"detail_url\":\"" + detailUrl + "\",\"img\":\"" + img + "\",\"title\":\"" + title + "\"}";

            // 构建 HttpEntity
            HttpEntity<String> requestEntity = new HttpEntity<>(jsonBody, headers);

            NetUtil.sendHttpRequest(apiUrl, requestEntity, HttpMethod.POST);
        } catch (SQLException e) {
            e.printStackTrace();
        }

    }

    // 此回传根据文档 https://open.oceanengine.com/labels/7/docs/1696710647473167
    private static String doDouyinReport(String clickid, String phone, String fromUrl, String params) {
        try {
            log.info("invoke doDouyinReport using clickid={} and phone={} and fromUrl={} and params={}", clickid, phone, fromUrl, params);
            int eventType = 3; // 表单事件
            String trackingUrl = AdReportUtil.douyinTrackingUrl(eventType, clickid, fromUrl, params);
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json");
            HttpEntity<String> httpEntity = new HttpEntity<>(null, headers);
            return NetUtil.sendHttpRequest(trackingUrl, httpEntity, HttpMethod.GET);
        } catch (Exception e) {
            log.error("捕获到异常: ", e);
            e.printStackTrace();
        }
        return null;
    }

    // 此回传根据文档 https://event-manager.oceanengine.com/docs/8650/h5_api_docs
    public static String doDouyinReport2(String clickId) {
        try {
            log.info("invoke doDouyinReport2 using clickid={}", clickId); // 处log输出更改暂未更新

            String url = "https://analytics.oceanengine.com/api/v2/conversion";
            // 生成当前时间戳
            long currentTimeStamp = System.currentTimeMillis();
            String jsonBody = "{\"event_type\":\"form\",\"context\":{\"ad\":{\"callback\":\"" + clickId + "\"}},\"timestamp\":" + currentTimeStamp + "}";

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<String> httpEntity = new HttpEntity<>(jsonBody, headers);
            return NetUtil.sendHttpRequest(url, httpEntity, HttpMethod.POST);
        } catch (Exception e) {
            log.error("捕获到异常: ", e);
            e.printStackTrace();
        }
        return null;
    }

    private static String doLiuliangguoReport(String callback) {
        String apiUrl = "https://api.liuliangguo.com/openapi/v1/trace/convert";

        // 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 构建请求体参数
        String requestBody = "{\"click_id\": \"" + callback + "\", \"convert_type\": \"FORM\"}";
        HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);

        return NetUtil.sendHttpRequest(apiUrl, requestEntity, HttpMethod.POST);
    }

    private static String doLiuliangguoAPIReport(String callback) {
        try {
            String apiUrl = "https://notify.wi-fi.cn/callback/unify/AgentZSGL/sdkCallback";

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体参数
            String requestBody = "{\"clickid\": \"" + callback + "\", \"convert_type\": \"FORM\"}";
            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);

            return NetUtil.sendHttpRequest(apiUrl, requestEntity, HttpMethod.POST);
        } catch (Exception e) {
            log.error("捕获到异常: ", e);
            e.printStackTrace();
        }
        return null;
    }

    private static String doXwAPIReport(String callback) {
        try {
            log.info("in  doXwAPIReport, callback is: {}", callback);

            String apiUrl = "https://shop.xwtec.cn/tmall/busiApi/callback/377";

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体参数
            String requestBody = "{\"clickid\": \"" + callback + "\", \"convert_type\": \"FORM\"}";
            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);

            return NetUtil.sendHttpRequest(apiUrl, requestEntity, HttpMethod.POST);
        } catch (Exception e) {
            log.error("捕获到异常: ", e);
            e.printStackTrace();
        }
        return null;
    }

    private static String doXwAPIReportV2(String callback, String pro, boolean success) {
        try {
            log.info("in  doXwAPIReport, callback is: {}", callback);

            String apiUrl = "https://shop.xwtec.cn/tmall/busiApi/callback/377";

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            String dt = TimeUtil.currentDate();
//            dt = "2024-07-16";
//            DbUtil.setDbByPro(pro);
            Entity entity = Db.use().queryOne("select pro,platform,date,num,lmt from pro_num where pro = ?  and platform='xwapi' and date = ?", pro, dt);

            Integer num = 1;

            // 因公司整体到量而失败时的回传
            if (!success) {
                log.info("当天公司整体已到量 pro: {}", pro);
                String requestBody = StringUtil.getHuichuanJsonStrring(450, callback, "FORM", "当日已到量");
                log.info("request body: {}", requestBody);
                HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);
                return NetUtil.sendHttpRequest(apiUrl, requestEntity, HttpMethod.POST);
            } else if (entity != null) {
                num = entity.getInt("num");
                Integer lmt = entity.getInt("lmt");
                num += 1;

                entity.set("num", num);
                Db.use().update(entity, entity.clone().set("num", num - 1));

                if (num >= lmt) {
                    log.info("当天已到量 pro: {}, platform:xwapi, date:{}, num:{}, lmt:{}", pro, dt, num, lmt);
                    String requestBody = StringUtil.getHuichuanJsonStrring(450, callback, "FORM", "当日已到量");
                    log.info("request body: {}", requestBody);
                    HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);
                    return NetUtil.sendHttpRequest(apiUrl, requestEntity, HttpMethod.POST);
                }
            } else {
                entity = Entity.create("pro_num")
                        .set("pro", pro)
                        .set("platform", "xwapi")
                        .set("date", dt)
                        .set("num", num);
                Db.use().insert(entity);
            }

            String requestBody = StringUtil.getHuichuanJsonStrring(200, callback, "FORM", "成功");
            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);
            return NetUtil.sendHttpRequest(apiUrl, requestEntity, HttpMethod.POST);
        } catch (Exception e) {
            log.error("捕获到异常: ", e);
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 众视广联回调
     */
    public static String doZsglReport(String clickid, String key) {
        try {
            String apiUrl = "https://dsp.widelink.net.cn/api/dsp/consume/conversion";

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体参数
            String requestBody = "{\"clickId\":\"" + clickid + "\",\"key\":\"" + key + "\"}";

            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);

            String response = NetUtil.sendHttpRequest(apiUrl, requestEntity, HttpMethod.POST);
            log.info("zsgl api 新增回传调用返回结果：{}", response);

            return response;
        } catch (Exception e) {
            log.error("捕获到异常: ", e);
            e.printStackTrace();
        }
        return null;
    }


    private static String doGzqhAPIReport(String clickid, String eventType) {
        try {
            String apiUrl = "http://ywff.ysy138.com/api/callback/zhongshiguanglian/?t=0507";

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体参数
            String requestBody = "{\"clickid\":\"" + clickid + "\",\"event_type\":\"" + eventType + "\"}";

            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);

            String response = NetUtil.sendHttpRequest(apiUrl, requestEntity, HttpMethod.POST);
            log.info("gzqh api 新增回传调用返回结果：{}", response);

            return response;
        } catch (Exception e) {
            log.error("捕获到异常: ", e);
            e.printStackTrace();
        }
        return null;
    }

    private static String doKuaishouReport(String callback) {
        // 表单事件
        doKuaishouReport(9, callback, null);
        // 提交订单事件
        return doKuaishouReport(14, callback, "10");
    }

    private static String doKuaishouReport(int eventType, String callback, String purchase_amount) {
        String trackingUrl = AdReportUtil.kuaishouTrackingUrl(eventType, callback, purchase_amount);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        HttpEntity<String> httpEntity = new HttpEntity<>(null, headers);
        return NetUtil.sendHttpRequest(trackingUrl, httpEntity, HttpMethod.GET);
    }

    // 参考文档：https://developers.e.qq.com/docs/guide/conversion/new_version/Web_api
    public static String doGuangdiantongReport(String clickId, String link) {
        try {
            log.info("invoke doGuangdiantongReport using clickid={}", clickId); // 处log输出更改暂未更新
            String actionType = "RESERVATION"; // 表单预约
            long actionTime = System.currentTimeMillis() / 1000; // Converting current time to seconds
            String encodedLink = URLEncoder.encode(link, StandardCharsets.UTF_8.toString());
            String url = String.format("http://tracking.e.qq.com/conv/web?clickid=%s&action_time=%d&action_type=%s&link=%s",
                    clickId, actionTime, actionType, encodedLink);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<String> httpEntity = new HttpEntity<>(headers);
            return NetUtil.sendHttpRequest(url, httpEntity, HttpMethod.GET);
        } catch (Exception e) {
            log.error("捕获到异常: ", e);
            e.printStackTrace();
        }
        return null;
    }

    private static String douyinTrackingUrl(int eventType, String clickid, String fromUrl, String params) {
        String url = fromUrl + params;
        log.info("in douyinTrackingUrl origin url: {}", url);
        // 构建上报请求链接
        StringBuilder sb = new StringBuilder("https://ad.oceanengine.com/track/activate/?");
        sb.append("event_type=").append(eventType);
        sb.append("&callback=").append(clickid);
        sb.append("&link=").append(encodeValue(url));

        return sb.toString();
    }

    private static String kuaishouTrackingUrl(int eventType, String callback, String purchase_amount) {
        long eventTime = System.currentTimeMillis(); // 获取当前时间戳

        // 构建上报请求链接
        StringBuilder sb = new StringBuilder("http://ad.partner.gifshow.com/track/activate?");
        sb.append("event_type=").append(eventType);
        sb.append("&event_time=").append(eventTime);
        sb.append("&callback=").append(callback);
        if (purchase_amount != null) {
            sb.append("&purchase_amount=").append(purchase_amount);
        }

        return sb.toString();
    }

    private static String encodeValue(String value) {
        try {
            return URLEncoder.encode(value, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) {
        String callback = "ENmRxJnUhJgDGKbmw8yIhfgGIKbmw8yIhfgGMA446-LTpwNCKWViYWQ3YzA5LTE2ZDMtNDE0Ni04YmE1LTI4NzhmZjQ4MjRlNHU1MzE2SIDSk60DkAEA";
//        String response = doKuaishouReport(callback);
//        String response = doLiuliangguoReport(callback);
//        doReport(callback, "llg");
//        Map<String, String> params = new HashMap<>();
//        params.put("callback", callback);
//        params.put("unknown", "weizhi");
//        report("13800001234", "shfy", "ks04", params);
//        String response = doDouyinReport2(callback, null, null, null);
//        String clickid = "yq4GtbZz0TdMaZOXPFNaeMdugt4zu55r_o6oiLFtXK9T7wGwWhpFcR209CfvW8SOKOSQULU3UC5eMMlKLnwwGvnzaMnCTxuBFNklQ7nVREqK9MHPNI_M-T5ttJTK8gy7xu8tkLzrSr7kOjTZkGQLEnKRIttiwpSBffadjmDICNRqLlN1IwpVbywXAyozi7sTVAk8O9HQRy2Ra9UKiIa_UQ";
//        String response = doGuangdiantongReport(clickid, "card.kuaifuinfo.com");
//        System.out.println(response);
//        String clickid = "17a29ec6fa8f76c8d3b2e122ac0b90d1";
//        String response = doGzqhAPIReport(clickid, "subscribe");
//        System.out.println(response);
        doXwAPIReportV2(callback, "nmch", true);
        doXwAPIReportV2(callback, "nmch", false);
    }

}
