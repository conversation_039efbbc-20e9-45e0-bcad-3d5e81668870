package com.xy.promote.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 杭州铖晗联响应码映射枚举
 * 将杭州铖晗联的响应码映射到众联流量果的响应码和描述
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
@Getter
@AllArgsConstructor
public enum HzchlAndZlllgResCode {

    SUCCESS("1", "成功", "0000", "短信发送成功"),
    //VERIFICATION_CODE_ERROR("0", "验证码错误", "E001", "验证码错误"),
    //PHONE_FORMAT_ERROR("0", "手机号格式错误", "E002", "手机号格式不正确"),
    //BLACKLISTED_NUMBER("0", "黑名单号码", "E003", "黑名单号码无法处理"),
    //CHANNEL_OFFLINE("0", "渠道已关闭", "E004", "渠道不存在或已关闭"),
    //OUT_OF_SERVICE_PERIOD("0", "非服务时段", "E005", "非服务时段，请稍后再试"),
    //PROVINCE_LIMIT_REACHED("0", "省份到量", "E010", "该省份达到今日限量，明天可继续办理"),
    //PRODUCT_LIMIT_REACHED("0", "产品到量", "E011", "该产品达到今日限量，明天可继续办理"),
    //PRODUCT_OFFLINE("0", "产品下线", "E012", "产品已下架，请暂停推广"),
    OTHER_FAILURE("0", "其他失败", "E101", "其他失败（运营商或其他系统返回失败）");

    /**
     * 杭州铖晗联状态码（1成功，0失败）
     */
    private final String status;

    /**
     * 杭州铖晗联结果消息
     */
    private final String resultMsg;

    /**
     * 对应的众联流量果响应码
     */
    private final String respCode;

    /**
     * 对应的众联流量果响应描述
     */
    private final String respDesc;

    /**
     * 根据状态码获取枚举
     *
     * @param status 状态码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static HzchlAndZlllgResCode getByStatus(String status) {
        for (HzchlAndZlllgResCode resCode : values()) {
            if (resCode.getStatus().equals(status)) {
                return resCode;
            }
        }
        return null;
    }

    /**
     * 根据状态码获取枚举，如果不存在则返回默认的OTHER_FAILURE
     *
     * @param status 状态码
     * @return 对应的枚举值，如果不存在则返回OTHER_FAILURE
     */
    public static HzchlAndZlllgResCode getByStatusWithDefault(String status) {
        HzchlAndZlllgResCode result = getByStatus(status);
        return result != null ? result : OTHER_FAILURE;
    }

    /**
     * 根据状态码获取响应码，如果不存在则返回默认的"E101"
     *
     * @param status 状态码
     * @return 对应的响应码，如果不存在则返回"E101"
     */
    public static String getRespCodeByStatus(String status) {
        HzchlAndZlllgResCode resCode = getByStatus(status);
        return resCode != null ? resCode.getRespCode() : OTHER_FAILURE.getRespCode();
    }

    /**
     * 根据响应码获取响应描述
     *
     * @param respCode 响应码
     * @return 对应的响应描述，如果不存在则返回默认值
     */
    public static String getRespDescByRespCode(String respCode) {
        for (HzchlAndZlllgResCode resCode : values()) {
            if (resCode.getRespCode().equals(respCode)) {
                return resCode.getRespDesc();
            }
        }
        return OTHER_FAILURE.getRespDesc();
    }

    /**
     * 根据响应码获取枚举
     *
     * @param respCode 响应码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static HzchlAndZlllgResCode getByRespCode(String respCode) {
        for (HzchlAndZlllgResCode resCode : values()) {
            if (resCode.getRespCode().equals(respCode)) {
                return resCode;
            }
        }
        return null;
    }

    /**
     * 获取响应信息对象
     *
     * @return 包含respCode和respDesc的响应信息对象
     */
    public ResponseInfo getResponseInfo() {
        return new ResponseInfo(this.respCode, this.respDesc);
    }

    @Override
    public String toString() {
        return "HzchlAndZlllgResCode{" +
                "status='" + status + '\'' +
                ", resultMsg='" + resultMsg + '\'' +
                ", respCode='" + respCode + '\'' +
                ", respDesc='" + respDesc + '\'' +
                '}';
    }

    /**
     * 响应信息类
     * 用于返回respCode和respDesc的组合
     */
    @Getter
    @AllArgsConstructor
    public static class ResponseInfo {
        /**
         * 响应码
         */
        private final String respCode;

        /**
         * 响应描述
         */
        private final String respDesc;

        @Override
        public String toString() {
            return "{\"respCode\": \"" + respCode + "\", \"respDesc\": \"" + respDesc + "\"}";
        }
    }
}
