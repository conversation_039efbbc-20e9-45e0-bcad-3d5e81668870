package com.xy.promote.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xy.lib.migu.util.PhoneInfoUtil;
import com.xy.lib.migu.util.PhoneUtil;
import com.xy.lib.migu.vo.PhoneLocationType;
import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import com.xy.promote.entity.ApiOrder;
import com.xy.promote.entity.DliangInfo;
import com.xy.promote.entity.ProInfoProduct;
import com.xy.promote.entity.ProOrder;
import com.xy.promote.enums.ZlllgOrderStatusCode;
import com.xy.promote.enums.ZlllgResponseCode;
import com.xy.promote.mapper.DliangInfoMapper;
import com.xy.promote.mapper.ProInfoProductMapper;
import com.xy.promote.service.ApiOrderService;
import com.xy.promote.service.ApiProviderService;
import com.xy.promote.service.ProOrderService;
import com.xy.promote.util.ApiExceptionUtil;
import com.xy.promote.util.ZlllgResponseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * ApiProviderService的抽象基类，包含公共逻辑
 * 减少各个实现类中的重复代码
 */
@Slf4j
@RequiredArgsConstructor
public abstract class BaseApiProviderServiceImpl implements ApiProviderService {

  protected final ApiOrderService apiOrderService;
  protected final ProInfoProductMapper proInfoProductMapper;
  protected final ProOrderService proOrderService;
  protected final DliangInfoMapper dliangInfoMapper;

  /**
   * 获取该服务提供商对应的pro标识
   * 用于到量检测
   */
  protected abstract String getProIdentifier();

  /**
   * 构建请求参数Map
   * 参数内容是为保存数据到pro_order表做准备
   */
  protected Map<String, String> buildMapRequestParams(ZlllgMessageDTO reqDTO, ProInfoProduct proInfoProduct) {
    Map<String, String> request = new HashMap<>();
    request.put("channel", reqDTO.getBiz().getChannelNo());
    request.put("phone", reqDTO.getBiz().getMobileNo());
    request.put("source", "api91");
    request.put("pro", proInfoProduct.getPro());
    request.put("clickid", reqDTO.getHead().getTransId());
    request.put("url", reqDTO.getBiz().getSource().getPageUrl());
    request.put("ua", reqDTO.getBiz().getSource().getUserAgent());
    return request;
  }
  
  /**
   * 查询产品信息
   */
  protected ProInfoProduct getProductInfo(String productNo) {
	  return proInfoProductMapper.selectOne(new LambdaQueryWrapper<ProInfoProduct>().eq(ProInfoProduct::getProductNo, productNo));
  }

  /**
   * 验证产品信息
   * pro_info_product中productNo等产品信息
   * 如果不存在就抛出产品下架的业务异常
   */
  protected void validateProductInfo(ProInfoProduct proInfoProduct, ZlllgMessageDTO reqDTO) {
    ApiExceptionUtil.throwIfNull(proInfoProduct, reqDTO, ZlllgResponseCode.PRODUCT_OFFLINE);  // 产品不存在，断言产品下架异常
    ApiExceptionUtil.throwIfTrue(proOrderService.isInBlacklist(reqDTO.getBiz().getMobileNo()), reqDTO, ZlllgResponseCode.APP_ERROR,"办理失败，请联系管理员");  // 号码黑名单校验
    ApiExceptionUtil.throwIfTrue(proOrderService.isAppExist(reqDTO.getBiz().getSource().getAppPackage()), reqDTO, ZlllgResponseCode.APP_ERROR, "该APP无法发展");  //  包名黑名单验证
  }

  /**
   * 保存短信发送信息到数据库
   */
  protected void saveSmsInfo(ZlllgMessageDTO reqDTO, Object response) {
    apiOrderService.sendSmsSaveInfo(reqDTO, JSONUtil.toJsonPrettyStr(JSONUtil.parseObj(response)));
  }

  /**
   * 更新验证码校验后的信息
   */
  protected void updateVerifyCodeInfo(ZlllgMessageDTO reqDTO, Object response) {
    apiOrderService.checkVerifyCodeLaterUpdate(reqDTO, JSONUtil.toJsonStr(response));
  }

  /**
   * 到量检测
   */
  protected void checkDaoliang(ZlllgMessageDTO reqDTO) {
    String startTime = PhoneUtil.getDaoliangTime();
    if (StrUtil.isBlank(startTime)) {
      return; // 获取不到时间则结束
    }

    // 查询号码归属地和运营商
    PhoneLocationType phoneLocationType = Objects.requireNonNull(PhoneInfoUtil.phoneInfo(reqDTO.getBiz().getMobileNo()));
    String province = phoneLocationType.getLocation().getProvince();

    // 把不是移动的号码过滤拦截
    //String carrier = phoneLocationType.getCarrier();
    //ApiExceptionUtil.throwIfFalse(
    //    carrier.equals("移动"),
    //    reqDTO,
    //    ZlllgResponseCode.OTHER_SYSTEM_FAILURE,"该号码非移动号码");

    // 进来先去检查是否存在到量
    LambdaQueryWrapper<DliangInfo> dliangWrapper = new LambdaQueryWrapper<DliangInfo>()
        .eq(DliangInfo::getPro, getProIdentifier())
        .and(notice -> notice.eq(DliangInfo::getNotice, province).or().eq(DliangInfo::getNotice, "all"))
        .eq(DliangInfo::getEnable, 1)
        .gt(DliangInfo::getCreateTime, startTime);

    // 如果不等于空，有返回all就提示全部到量，如果没有就提示省份到量
    dliangInfoMapper.selectList(dliangWrapper).forEach(dliangInfo -> {
      ApiExceptionUtil.throwIfTrue(
          dliangInfo.getNotice().equals("all"),
          reqDTO,
          ZlllgResponseCode.PRODUCT_DAILY_LIMIT);
      ApiExceptionUtil.throwIfTrue(
          dliangInfo.getNotice().equals(province),
          reqDTO,
          ZlllgResponseCode.PROVINCE_DAILY_LIMIT);
    });
  }

  @Override
  public ZlllgMessageDTO orderQuery(ZlllgMessageDTO reqDTO) {
    // 查询API订单
    LambdaQueryWrapper<ApiOrder> queryWrapper = new LambdaQueryWrapper<ApiOrder>()
        .eq(ApiOrder::getOrderId, reqDTO.getBiz().getOrderId())
        .eq(ApiOrder::getOutOrderId, reqDTO.getBiz().getOutOrderId());
    ApiOrder apiOrder = apiOrderService.getOne(queryWrapper);
    ApiExceptionUtil.throwIfNull(apiOrder, reqDTO, ZlllgResponseCode.ORDER_NOT_EXIST);

    // 查询产品订单
    LambdaQueryWrapper<ProOrder> proQueryWrapper = new LambdaQueryWrapper<ProOrder>()
        .eq(ProOrder::getOrderId, reqDTO.getBiz().getOrderId());
    ProOrder proOrder = proOrderService.getOne(proQueryWrapper);

    // 如果没有数据返回订单不存在响应
    if (ObjectUtil.isEmpty(proOrder)) {
      return ZlllgResponseUtil.buildZlllgResponse(reqDTO,
          ZlllgResponseCode.ORDER_NOT_EXIST.getCode(),
          ZlllgResponseCode.ORDER_NOT_EXIST.getDescription(),
          null);
    }

    // 根据回传状态确定订单状态和消息
    Integer huichuanStatus = proOrder.getHuichuanStatus();
    String orderStatus = huichuanStatus == 1 ? ZlllgOrderStatusCode.SUCCESS.getCode(): ZlllgOrderStatusCode.FAILED.getCode();
    String message = huichuanStatus == 1 ? "订购成功" : "订购失败";

    return ZlllgResponseUtil.buildZlllgResponse(
        reqDTO,
        ZlllgResponseCode.SUCCESS.getCode(),
        ZlllgResponseCode.SUCCESS.getDescription(),
        bizBuilder -> bizBuilder
            .orderId(apiOrder.getOrderId())
            .outOrderId(apiOrder.getOutOrderId())
            .mobileNo(apiOrder.getMobileNo())
            .orderStatus(orderStatus)
            .message(message));
  }

  @Override
  public ZlllgMessageDTO recommend(ZlllgMessageDTO reqDTO) {
    // 默认不支持前置校验接口，返回默认成功响应
    return ZlllgResponseUtil.buildZlllgResponse(
        reqDTO,
        ZlllgResponseCode.SUCCESS.getCode(),
        ZlllgResponseCode.SUCCESS.getDescription(),
        bizBuilder -> bizBuilder.mobileNo(reqDTO.getBiz().getMobileNo()));
  }
}