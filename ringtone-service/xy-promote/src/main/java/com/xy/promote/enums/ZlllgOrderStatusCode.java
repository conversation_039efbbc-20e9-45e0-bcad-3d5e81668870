package com.xy.promote.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单状态码枚举
 * 业务订单状态(biz->orderStatus)
 */
@Getter
@AllArgsConstructor
public enum ZlllgOrderStatusCode {
    
    PROCESSING("A101", "订单处理中", "需要等待后续订购接口异步通知或进行订单状态查询"),
    SUCCESS("A102", "业务订购成功", "返回该状态码的订单为最终订购成功，双方数据以该状态订单数据进行核对"),
    FAILED("A103", "业务订购失败", "");
    
    /**
     * 状态码
     */
    private final String code;
    
    /**
     * 描述
     */
    private final String description;
    
    /**
     * 说明
     */
    private final String explanation;
    
    /**
     * 根据状态码获取枚举
     * 
     * @param code 状态码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ZlllgOrderStatusCode getByCode(String code) {
        for (ZlllgOrderStatusCode status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    @Override
    public String toString() {
        return "OrderStatusBiz{" +
                "code='" + code + '\'' +
                ", description='" + description + '\'' +
                ", explanation='" + explanation + '\'' +
                '}';
    }
}