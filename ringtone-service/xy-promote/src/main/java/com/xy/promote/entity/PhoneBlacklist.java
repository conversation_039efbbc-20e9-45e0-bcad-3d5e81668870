package com.xy.promote.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

@Data
@Accessors(chain = true)
@SuperBuilder
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "phone_blacklist")
public class PhoneBlacklist implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @NotNull(message = "主键不能为null")
    private Long id;

    /**
     * 手机号
     */
    @TableField(value = "phone")
    @Size(max = 52,message = "手机号最大长度要小于 52")
    @NotBlank(message = "手机号不能为空")
    private String phone;

    /**
     * 1黑名单禁止办理
     */
    @TableField(value = "`type`")
    @NotNull(message = "1黑名单禁止办理不能为null")
    private Integer type;

    /**
     * 描述
     */
    @TableField(value = "description")
    @Size(max = 512,message = "描述最大长度要小于 512")
    private String description;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @NotNull(message = "创建时间不能为null")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @NotNull(message = "更新时间不能为null")
    private Date updateTime;
}