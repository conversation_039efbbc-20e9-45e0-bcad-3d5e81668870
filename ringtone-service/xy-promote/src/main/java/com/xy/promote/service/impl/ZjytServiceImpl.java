package com.xy.promote.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xy.base.core.constant.ResultCodeConsts;
import com.xy.base.core.exception.AppException;
import com.xy.promote.dto.zjyt.CheckCodeRequest;
import com.xy.promote.dto.zjyt.VerifyCodeRequest;
import com.xy.promote.dto.zjyt.ZjytResponse;
import com.xy.promote.service.ZjytService;
import com.xy.promote.util.PromoteUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class ZjytServiceImpl implements ZjytService{
    private static final String ORDER_API = "https://mgyy.youtianz.com/api/order/addOrder";
    private static final String SUBMIT_API = "https://mgyy.youtianz.com/api/order/submitCode";
    private static final String RELEASE_NO = "r394097784";
    
    private final ObjectMapper mapper;

    /**
     * 调用浙江有田接口 - 获取验证码
     *
     * @param reqDTO  请求参数
     * @param request HTTP请求
     * @return API响应
     */
    @Override
    public ZjytResponse addOrder(VerifyCodeRequest reqDTO, Map<String, String> request) {
        reqDTO.setReleaseNo(RELEASE_NO);
        ZjytResponse zjytResponse = sendRequest(reqDTO.getBillNum(), ORDER_API, reqDTO, request);
        if (ObjectUtil.isNotEmpty(zjytResponse) && ObjectUtil.isNotEmpty(zjytResponse.getData()) && zjytResponse.getData().containsKey("linkId")) {
            request.put("orderId", zjytResponse.getData().get("linkId"));
        }
        return zjytResponse;
    }

    /**
     * 调用浙江有田接口 - 验证短信验证码
     *
     * @param reqDTO 请求参数
     * @return API响应
     */
    @Override
    public ZjytResponse submitCode(CheckCodeRequest reqDTO) {
        return sendRequest(reqDTO.getBillNum(), SUBMIT_API, reqDTO, null);
    }

    /**
     * 发送API请求并处理响应
     * 
     * @param phone 电话号码
     * @param url API地址
     * @param requestBody 请求体
     * @param params 请求参数
     * @return API响应
     */
    private ZjytResponse sendRequest(String phone, String url, Object requestBody, Map<String, String> params) {
        // 将请求参数转换为 JSON 格式
        String jsonRequestBody;
        try {
            jsonRequestBody = mapper.writeValueAsString(requestBody);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON序列化失败", e);
        }

        log.info("准备请求：" + url);
        long start = System.currentTimeMillis();
        try (HttpResponse response = HttpUtil.createPost(url)
                .contentType("application/json")
                .body(jsonRequestBody)
                .execute()) {
            String responseBodyStr = response.body();
            log.info("interfaceAction {},{}", url, responseBodyStr);
            PromoteUtils.insertLogApi(phone, url, jsonRequestBody, responseBodyStr, "POST", (int) (System.currentTimeMillis() - start));

            // 判断response的statusCode
            if (ResultCodeConsts.SUCCESS != response.getStatus()) {
                log.error("用户：{}, 接口：{},访问失败状态码：{}", phone, url, response.getStatus());
                throw new AppException("请求接口失败，状态码：" + response.getStatus());
            }

            ZjytResponse res = mapper.readValue(responseBodyStr, ZjytResponse.class);
            // 如果访问的是下发验证码接口，则将linkId保存到数据库中
            if (ORDER_API.equals(url) && null != res && null != res.getData() && res.getData().containsKey("linkId")) {
                params.put("orderId", res.getData().get("linkId"));
                params.put("ua", requestBody instanceof VerifyCodeRequest ? ((VerifyCodeRequest) requestBody).getUserAgent() : "");
                try {
                    PromoteUtils.insertProOrder(params);
                } catch (Exception e) {
                    log.error("插入浙江有田短信订单失败", e);
                }
            }
            return res;
        } catch (JsonProcessingException e) {
            throw new RuntimeException("解析响应失败", e);
        }
    }
} 