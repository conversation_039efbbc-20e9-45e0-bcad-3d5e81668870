package com.xy.promote.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xy.base.starter.redis.RedisUtils;
import com.xy.promote.entity.ProductPromotionCompany;
import com.xy.promote.mapper.ProductPromotionCompanyMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 推广商服务
 * <AUTHOR>
 * @since 2025/6/20 15:09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductPromotionCompanyService {

  private final ProductPromotionCompanyMapper productPromotionCompanyMapper;

  private static final String CACHE_PREFIX_COMPANY = "promotion:company:";
  private static final String CACHE_PREFIX_SECRET = "promotion:secret:";
  private static final long CACHE_EXPIRE_TIME = 30 * 60;

  /**
   * 根据appId查询推广商信息
   */
  public ProductPromotionCompany getByAppId(String appId) {
    if (StrUtil.isBlank(appId))
      return null;

    String cacheKey = CACHE_PREFIX_COMPANY + appId;
    ProductPromotionCompany company = RedisUtils.getForEntity(cacheKey, ProductPromotionCompany.class);
    if (company != null)
      return company;

    company = productPromotionCompanyMapper.selectOne(new LambdaQueryWrapper<ProductPromotionCompany>().eq(ProductPromotionCompany::getAppId, appId));
    if (company != null) {
      RedisUtils.set(cacheKey, company, CACHE_EXPIRE_TIME);
    }
    return company;
  }

  /**
   * 根据appId获取secretKey
   */
  public String getSecretKeyByAppId(String appId) {
    if (StrUtil.isBlank(appId))
      return null;

    String cacheKey = CACHE_PREFIX_SECRET + appId;
    String secretKey = RedisUtils.get(cacheKey);
    if (StrUtil.isNotBlank(secretKey))
      return secretKey;

    ProductPromotionCompany company = getByAppId(appId);
    secretKey = company != null ? company.getSecretKey() : null;
    if (StrUtil.isNotBlank(secretKey)) {
      RedisUtils.set(cacheKey, secretKey, CACHE_EXPIRE_TIME);
    }
    return secretKey;
  }

  /**
   * 验证appId是否有效
   */
  public boolean isValidAppId(String appId) {
    return getByAppId(appId) != null;
  }

  /**
   * 清除缓存
   */
  public void evictCache(String appId) {
    if (StrUtil.isBlank(appId)) {
      return;
    }
    RedisUtils.del(CACHE_PREFIX_COMPANY + appId, CACHE_PREFIX_SECRET + appId);
    log.info("清除appId {} 的缓存", appId);
  }

  /**
   * 保存或更新
   */
  public boolean saveOrUpdate(ProductPromotionCompany company) {
    if (company == null || StrUtil.isBlank(company.getAppId())) {
      return false;
    }
    boolean result = company.getId() == null ? productPromotionCompanyMapper.insert(company) > 0 : productPromotionCompanyMapper.updateById(company) > 0;
    if (result) {
      evictCache(company.getAppId());
    }
    return result;
  }
}