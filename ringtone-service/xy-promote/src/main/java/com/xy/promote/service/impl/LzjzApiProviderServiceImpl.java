package com.xy.promote.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.xy.promote.dto.lzjz.LzjzCheckCodeRequest;
import com.xy.promote.dto.lzjz.LzjzResponse;
import com.xy.promote.dto.lzjz.LzjzVerifyCodeRequest;
import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import com.xy.promote.entity.ProInfoProduct;
import com.xy.promote.enums.LzjzAndZlllgResCode;
import com.xy.promote.enums.ZlllgOrderStatusCode;
import com.xy.promote.enums.ZlllgResponseCode;
import com.xy.promote.mapper.DliangInfoMapper;
import com.xy.promote.mapper.ProInfoProductMapper;
import com.xy.promote.service.ApiOrderService;
import com.xy.promote.service.LzjzService;
import com.xy.promote.service.ProOrderService;
import com.xy.promote.util.ApiExceptionUtil;
import com.xy.promote.util.ZlllgResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 乐只君子API服务实现
 */
@Slf4j
@Service
public class LzjzApiProviderServiceImpl extends BaseApiProviderServiceImpl {

	private final LzjzService lzjzService;

	public LzjzApiProviderServiceImpl(ApiOrderService apiOrderService,
			LzjzService lzjzService,
			ProInfoProductMapper proInfoProductMapper,
			ProOrderService proOrderService,
			DliangInfoMapper dliangInfoMapper) {
		super(apiOrderService, proInfoProductMapper, proOrderService, dliangInfoMapper);
		this.lzjzService = lzjzService;
	}

	@Override
	public String getSupportedProductNo() {
		// 乐只君子支持的产品列表
		return "FA";
	}

	@Override
	protected String getProIdentifier() {
		return "lzjz";
	}

	@Override
	public ZlllgMessageDTO sendSms(ZlllgMessageDTO reqDTO) {
		// 进行省份到量检测和运营商检测
		checkDaoliang(reqDTO);

		// 查询并验证产品信息
		ProInfoProduct proInfoProduct = getProductInfo(reqDTO.getBiz().getProductNo());
		validateProductInfo(proInfoProduct, reqDTO);

		// 构建请求参数
		Map<String, String> request = buildMapRequestParams(reqDTO, proInfoProduct);
		// 使用hutool如果reqDTO.getBiz().getSource().getAppPackage()为空，使用默认值
		String appPackage = StrUtil.blankToDefault(reqDTO.getBiz().getSource().getAppPackage(), "com.xy.promote");
		// 构建乐只君子请求参数
		LzjzVerifyCodeRequest lzjzRequest = LzjzVerifyCodeRequest.builder()
				.biz(proInfoProduct.getProduct()) // 使用乐只君子的产品编号
				.ditch("49")
				.mobile(reqDTO.getBiz().getMobileNo())
				.cpparam("juliang")
				.ipAddress(reqDTO.getBiz().getSource().getClientIp())
				.appPackage(appPackage)
				.appName(reqDTO.getBiz().getSource().getPlatform())
				.sourceUrl(reqDTO.getBiz().getSource().getPageUrl())
				.userAgent(reqDTO.getBiz().getSource().getUserAgent())
				.build();

		// 发送请求到乐只君子
		LzjzResponse lzjzResponse = lzjzService.getVerifyCode(lzjzRequest, request);

		String orderId = Optional.ofNullable(lzjzResponse.getOrderid()).orElse(IdWorker.getTimeId());
		String respCode = LzjzAndZlllgResCode.getRespCodeByCode(lzjzResponse.getCode());
		String respDesc = Optional.ofNullable(lzjzResponse.getErrmsg())
				.orElse(LzjzAndZlllgResCode.getRespDescByResCode(respCode));

		// 众联流量果接口响应
		ZlllgMessageDTO res = ZlllgResponseUtil.buildZlllgResponse(reqDTO, respCode, respDesc,
				bizBuilder -> bizBuilder.extraInfo(MapUtil.builder().put("linkId", orderId).build()));
		// 更新DTO对象存到数据库
		reqDTO.getHead().setRespCode(lzjzResponse.getCode().toString());
		reqDTO.getBiz().setOrderId(orderId);
		reqDTO.getBiz().setOrderStatus(respCode);
		// 保存信息到数据库
		saveSmsInfo(reqDTO, lzjzResponse);
		return res;
	}

	@Override
	public ZlllgMessageDTO orderSubmit(ZlllgMessageDTO reqDTO) {
		String orderId = Optional.ofNullable(reqDTO.getBiz().getExtraInfo().get("linkId")).map(Object::toString)
				.orElse(null);

		ApiExceptionUtil.throwIfEmpty(orderId, reqDTO, ZlllgResponseCode.PARAM_VALIDATION_FAILED);

		// 构建乐只君子验证码请求
		LzjzCheckCodeRequest codeRequest = LzjzCheckCodeRequest.builder()
				.orderid(orderId)
				.vcode(reqDTO.getBiz().getSmsCode())
				.build();

		// 发送请求到乐只君子
		LzjzResponse lzjzResponse = lzjzService.checkVerifyCode(codeRequest, new HashMap<>());

		String respCode = LzjzAndZlllgResCode.getRespCodeByCode(lzjzResponse.getCode());
		String respDesc = Optional.ofNullable(lzjzResponse.getErrmsg())
				.orElse(LzjzAndZlllgResCode.getRespDescByResCode(respCode));

		// 响应众联流量果接口
		ZlllgMessageDTO res = ZlllgResponseUtil.buildZlllgResponse(reqDTO, respCode, respDesc,
				bizBuilder -> bizBuilder
						.orderId(orderId)
						.outOrderId(reqDTO.getBiz().getOutOrderId())
						.orderStatus(ZlllgOrderStatusCode.PROCESSING.getCode()));
		// 更新DTO对象存到数据库
		reqDTO.getHead().setRespCode(lzjzResponse.getCode().toString());
		reqDTO.getBiz().setOrderId(orderId);
		reqDTO.getBiz().setOrderStatus(respCode);
		// 更新信息到数据库
		updateVerifyCodeInfo(reqDTO, lzjzResponse);
		return res;
	}

}