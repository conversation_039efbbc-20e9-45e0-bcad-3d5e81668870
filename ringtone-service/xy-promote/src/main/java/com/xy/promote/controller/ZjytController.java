package com.xy.promote.controller;

import cn.hutool.extra.servlet.ServletUtil;
import com.xy.promote.dto.zjyt.CheckCodeRequest;
import com.xy.promote.dto.zjyt.VerifyCodeRequest;
import com.xy.promote.dto.zjyt.ZjytResponse;
import com.xy.promote.service.ZjytService;
import com.xy.promote.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 浙江有田API控制器
 */
@Slf4j
@RestController
@RequestMapping("/zjyt")
@RequiredArgsConstructor
public class ZjytController {
    
    private final ZjytService zjytService;

    /**
     * 下发验证码接口
     */
    @PostMapping("/getVerifyCode")
    public ZjytResponse getVerifyCode(@RequestParam Map<String, String> params, HttpServletRequest request) {
        VerifyCodeRequest req = VerifyCodeRequest.builder()
                .billNum(params.get("phone"))
                .releaseNo("r394097784")
                .userIp(ServletUtil.getClientIP(request))
                .userAgent(request.getHeader("user-agent"))
                .mediaName("巨量")
                .packageName(StringUtil.extractPackageName(request.getHeader("user-agent")))
                .build();
        return zjytService.addOrder(req , params);
    }

    /**
     * 短信验证接口
     */
    @PostMapping("/checkVerifyCode")
    public ZjytResponse checkVerifyCode(@RequestParam Map<String, String> request) {
        CheckCodeRequest requestBody = CheckCodeRequest.builder()
                .linkId(request.get("linkId"))
                .code(request.get("code"))
                .billNum(request.get("billNum"))
                .build();
        return zjytService.submitCode(requestBody);
    }
}
