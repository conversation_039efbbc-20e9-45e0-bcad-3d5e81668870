package com.xy.promote.util;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import com.xy.promote.enums.ZlllgResponseCode;
import com.xy.promote.exception.ApiException;

import java.util.Collection;
import java.util.Map;

/**
 * API异常工具类
 */
public class ApiExceptionUtil {

  /**
   * 抛出API异常
   *
   * @param reqDTO   请求DTO
   * @param respCode 响应码
   * @param respDesc 响应描述
   */
  public static void throwApiException(ZlllgMessageDTO reqDTO, String respCode, String respDesc) {
    throw new ApiException(reqDTO, respCode, respDesc);
  }

  /**
   * 抛出API异常
   *
   * @param reqDTO   请求DTO
   * @param respCode 响应码枚举
   */
  public static void throwApiException(ZlllgMessageDTO reqDTO, ZlllgResponseCode respCode) {
    throw new ApiException(reqDTO, respCode.getCode(), respCode.getDescription());
  }
  
  /**
   * 抛出API异常
   *
   * @param reqDTO   请求DTO
   * @param respCode 响应码枚举
   */
  public static void throwApiException(ZlllgMessageDTO reqDTO, ZlllgResponseCode respCode, String respDesc) {
    throw new ApiException(reqDTO, respCode.getCode(), respDesc);
  }

  /**
   * 抛出API异常
   *
   * @param reqDTO   请求DTO
   * @param respCode 响应码枚举
   * @param cause    异常原因
   */
  public static void throwApiException(ZlllgMessageDTO reqDTO, ZlllgResponseCode respCode, Throwable cause) {
    throw new ApiException(reqDTO, respCode.getCode(), respCode.getDescription(), cause);
  }

  /**
   * 当条件为true时抛出API异常
   *
   * @param condition 条件
   * @param reqDTO    请求DTO
   * @param respCode  响应码枚举
   */
  public static void throwIfTrue(boolean condition, ZlllgMessageDTO reqDTO, ZlllgResponseCode respCode) {
    if (condition) {
      throwApiException(reqDTO, respCode);
    }
  }
  
  /**
   * 当条件为true时抛出API异常，自定义描述
   *
   * @param condition 条件
   * @param reqDTO    请求DTO
   * @param respCode  响应码枚举
   */
  public static void throwIfTrue(boolean condition, ZlllgMessageDTO reqDTO, ZlllgResponseCode respCode , String respDesc) {
    if (condition) {
      throwApiException(reqDTO, respCode, respDesc);
    }
  }

  /**
   * 当条件为false时抛出API异常
   *
   * @param condition 条件
   * @param reqDTO    请求DTO
   * @param respCode  响应码枚举
   */
  public static void throwIfFalse(boolean condition, ZlllgMessageDTO reqDTO, ZlllgResponseCode respCode) {
    if (!condition) {
      throwApiException(reqDTO, respCode);
    }
  }

  /**
   * 当条件为false时抛出API异常，自定义描述
   *
   * @param condition 条件
   * @param reqDTO    请求DTO
   * @param respCode  响应码枚举
   */
  public static void throwIfFalse(boolean condition, ZlllgMessageDTO reqDTO, ZlllgResponseCode respCode , String respDesc) {
    if (!condition) {
      throwApiException(reqDTO, respCode, respDesc);
    }
  }

  /**
   * 检查对象是否为null，如果为null则抛出异常
   *
   * @param obj      需要检查的对象
   * @param reqDTO   请求DTO
   * @param respCode 响应码枚举
   */
  public static void throwIfNull(Object obj, ZlllgMessageDTO reqDTO, ZlllgResponseCode respCode) {
    if (obj == null) {
      throwApiException(reqDTO, respCode);
    }
  }

  /**
   * 检查字符串是否为空，如果为空则抛出异常
   * 
   * @param str      需要检查的字符串
   * @param reqDTO   请求DTO
   * @param respCode 响应码枚举
   */
  public static void throwIfEmpty(String str, ZlllgMessageDTO reqDTO, ZlllgResponseCode respCode) {
    if (str == null || str.trim().isEmpty()) {
      throwApiException(reqDTO, respCode);
    }
  }

  /**
   * 断言对象不为空，如果为空则抛出异常
   *
   * @param obj      需要断言的对象
   * @param reqDTO   请求DTO
   * @param respCode 响应码枚举
   */
  public static void notNull(Object obj, ZlllgMessageDTO reqDTO, ZlllgResponseCode respCode) {
    try {
      Assert.notNull(obj);
    } catch (Exception e) {
      throwApiException(reqDTO, respCode);
    }
  }

  /**
   * 断言字符串不为空，如果为空则抛出异常
   *
   * @param str      需要断言的字符串
   * @param reqDTO   请求DTO
   * @param respCode 响应码枚举
   */
  public static void notEmpty(String str, ZlllgMessageDTO reqDTO, ZlllgResponseCode respCode) {
    try {
      Assert.notEmpty(str);
    } catch (Exception e) {
      throwApiException(reqDTO, respCode);
    }
  }

  /**
   * 断言集合不为空，如果为空则抛出异常
   *
   * @param collection 需要断言的集合
   * @param reqDTO     请求DTO
   * @param respCode   响应码枚举
   */
  public static void notEmpty(Collection<?> collection, ZlllgMessageDTO reqDTO, ZlllgResponseCode respCode) {
    try {
      Assert.notEmpty(collection);
    } catch (Exception e) {
      throwApiException(reqDTO, respCode);
    }
  }

  /**
   * 断言Map不为空，如果为空则抛出异常
   *
   * @param map      需要断言的Map
   * @param reqDTO   请求DTO
   * @param respCode 响应码枚举
   */
  public static void notEmpty(Map<?, ?> map, ZlllgMessageDTO reqDTO, ZlllgResponseCode respCode) {
    try {
      Assert.notEmpty(map);
    } catch (Exception e) {
      throwApiException(reqDTO, respCode);
    }
  }

  /**
   * 断言表达式为真，如果为假则抛出异常
   *
   * @param expression 需要断言的表达式
   * @param reqDTO     请求DTO
   * @param respCode   响应码枚举
   */
  public static void isTrue(boolean expression, ZlllgMessageDTO reqDTO, ZlllgResponseCode respCode) {
    try {
      Assert.isTrue(expression);
    } catch (Exception e) {
      throwApiException(reqDTO, respCode);
    }
  }

  /**
   * 断言对象相等，如果不相等则抛出异常
   *
   * @param obj1     对象1
   * @param obj2     对象2
   * @param reqDTO   请求DTO
   * @param respCode 响应码枚举
   */
  public static void equals(Object obj1, Object obj2, ZlllgMessageDTO reqDTO, ZlllgResponseCode respCode) {
    if (!ObjectUtil.equals(obj1, obj2)) {
      throwApiException(reqDTO, respCode);
    }
  }

  /**
   * 断言值是否在指定范围内，如果不在则抛出异常
   *
   * @param value    需要检查的值
   * @param min      最小值（包含）
   * @param max      最大值（包含）
   * @param reqDTO   请求DTO
   * @param respCode 响应码枚举
   */
  public static void isBetween(Number value, Number min, Number max, ZlllgMessageDTO reqDTO,
      ZlllgResponseCode respCode) {
    double val = value.doubleValue();
    double minimum = min.doubleValue();
    double maximum = max.doubleValue();

    if (val < minimum || val > maximum) {
      throwApiException(reqDTO, respCode);
    }
  }
}