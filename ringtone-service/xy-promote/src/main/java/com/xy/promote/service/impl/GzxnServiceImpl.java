package com.xy.promote.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xy.promote.dto.gzxn.CheckCodeRequest;
import com.xy.promote.dto.gzxn.GzxnResponse;
import com.xy.promote.dto.gzxn.VerifyCodeRequest;
import com.xy.promote.service.GzxnService;
import com.xy.promote.util.PromoteUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 广州馨宁业务实现类
 *
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GzxnServiceImpl implements GzxnService {
	
	private static final String GET_VERIFY_CODE_API = "http://8.130.117.5/pay/v1.servlet";
	private static final String CHECK_VERIFY_CODE_API = "http://8.130.117.5/pay/v2.servlet";
	
	private final ObjectMapper mapper;
	
	/**
	 * 调用广州馨宁接口 - 获取验证码
	 *
	 * @param reqDTO  请求参数
	 * @param params  前端参数
	 * @return API响应
	 */
	@Override
	public GzxnResponse getVerifyCode(VerifyCodeRequest reqDTO, Map<String, String> params) {
		Map<String, String> body = Convert.toMap(String.class, String.class, BeanUtil.beanToMap(reqDTO));
		return getResponse(GET_VERIFY_CODE_API, body, params ,true);
	}
	
	/**
	 * 调用广州馨宁接口 - 验证短信验证码
	 *
	 * @param reqDTO 请求参数
	 * @return API响应
	 */
	@Override
	public GzxnResponse checkVerifyCode(CheckCodeRequest reqDTO) {
		Map<String, Object> objectMap = BeanUtil.beanToMap(reqDTO);
		Map<String, String> body = Convert.toMap(String.class, String.class, objectMap);
		return getResponse(CHECK_VERIFY_CODE_API, body, null, false);
	}
	
	/**
	 * 发送API请求并处理响应
	 *
	 * @param url   API地址
	 * @param body  馨宁请求参数
	 * @param params 前端参数
	 * @param record 是否记录订单
	 * @return API响应
	 */
	private GzxnResponse getResponse(String url, Map<String, String> body, Map<String, String> params, boolean record) {
		
		url = url + "?" + HttpUtil.toParams(body);
		
		log.info("准备请求：" + url);
		long start = System.currentTimeMillis();
		try (HttpResponse response = HttpUtil.createGet(url)
				.contentType("application/json")
				.execute()) {
			
			String responseStr = response.body();
			log.info("interfaceAction {},{}", url, responseStr);
			GzxnResponse res = mapper.readValue(responseStr, GzxnResponse.class);
			
			PromoteUtils.insertLogApi(url, "", responseStr, "GET", (int) (System.currentTimeMillis() - start));
			if ("1".equals(res.getStatus())) {
				if (record) {
					params.put("orderId", res.getLinkid());
					params.put("pro", params.get("pro"));
					params.put("ua", body.get("userAgent"));
					PromoteUtils.insertProOrder(params);
				}
			}
			return res;
		} catch (JsonProcessingException e) {
			throw new RuntimeException(e);
		}
	}
}