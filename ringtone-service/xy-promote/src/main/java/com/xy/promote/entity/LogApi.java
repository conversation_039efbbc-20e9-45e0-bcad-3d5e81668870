package com.xy.promote.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName log_api
 */
@TableName(value ="log_api")
@Data
public class LogApi implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 日志类型，0主调，1被调
     */
    private Integer type;

    /**
     * 
     */
    private String url;

    /**
     * 请求ip
     */
    private String ip;

    /**
     * 方法类型
     */
    private String method;

    /**
     * 代码位置
     */
    private String position;

    /**
     * 请求内容
     */
    private String request;

    /**
     * 回复内容
     */
    private String response;

    /**
     * 花费时间，单位毫秒
     */
    private Integer timing;

    /**
     * 是否发生异常
     */
    private Boolean exception;

    /**
     * 
     */
    private String exceptionName;

    /**
     * 
     */
    private String exceptionTrace;

    /**
     * 
     */
    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        LogApi other = (LogApi) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getUrl() == null ? other.getUrl() == null : this.getUrl().equals(other.getUrl()))
            && (this.getIp() == null ? other.getIp() == null : this.getIp().equals(other.getIp()))
            && (this.getMethod() == null ? other.getMethod() == null : this.getMethod().equals(other.getMethod()))
            && (this.getPosition() == null ? other.getPosition() == null : this.getPosition().equals(other.getPosition()))
            && (this.getRequest() == null ? other.getRequest() == null : this.getRequest().equals(other.getRequest()))
            && (this.getResponse() == null ? other.getResponse() == null : this.getResponse().equals(other.getResponse()))
            && (this.getTiming() == null ? other.getTiming() == null : this.getTiming().equals(other.getTiming()))
            && (this.getException() == null ? other.getException() == null : this.getException().equals(other.getException()))
            && (this.getExceptionName() == null ? other.getExceptionName() == null : this.getExceptionName().equals(other.getExceptionName()))
            && (this.getExceptionTrace() == null ? other.getExceptionTrace() == null : this.getExceptionTrace().equals(other.getExceptionTrace()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getUrl() == null) ? 0 : getUrl().hashCode());
        result = prime * result + ((getIp() == null) ? 0 : getIp().hashCode());
        result = prime * result + ((getMethod() == null) ? 0 : getMethod().hashCode());
        result = prime * result + ((getPosition() == null) ? 0 : getPosition().hashCode());
        result = prime * result + ((getRequest() == null) ? 0 : getRequest().hashCode());
        result = prime * result + ((getResponse() == null) ? 0 : getResponse().hashCode());
        result = prime * result + ((getTiming() == null) ? 0 : getTiming().hashCode());
        result = prime * result + ((getException() == null) ? 0 : getException().hashCode());
        result = prime * result + ((getExceptionName() == null) ? 0 : getExceptionName().hashCode());
        result = prime * result + ((getExceptionTrace() == null) ? 0 : getExceptionTrace().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", type=").append(type);
        sb.append(", url=").append(url);
        sb.append(", ip=").append(ip);
        sb.append(", method=").append(method);
        sb.append(", position=").append(position);
        sb.append(", request=").append(request);
        sb.append(", response=").append(response);
        sb.append(", timing=").append(timing);
        sb.append(", exception=").append(exception);
        sb.append(", exceptionName=").append(exceptionName);
        sb.append(", exceptionTrace=").append(exceptionTrace);
        sb.append(", createTime=").append(createTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}