package com.xy.promote.dto.zsgl;

import com.xy.promote.dto.zlllg.ZlllgMessageDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @since 2025/7/8 10:45
 */
@Data
@Accessors(chain = true)
@SuperBuilder
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ZsglOrderSubmitReq {
	  /**
	   * 手机号码
	   */
	  private String mobileNo;
	  /**
	   * 短信验证码
	   */
	  private String smsCode;
	  /**
	   * linkId
	   */
	  private String linkId;
}
