package com.xy.admin.util;

import org.jaudiotagger.audio.AudioFile;
import org.jaudiotagger.audio.AudioFileIO;
import org.junit.jupiter.api.Test;

import java.io.File;

/**
 * <AUTHOR>
 * @since 2025/1/12
 */
class CVUtilTest {

    @Test
    void compose() throws Exception {

        String voicePath = "D:/diy/2/error.mp3";

        String[] pics = new String[]{"D:/diy/sys/0b40a495de4a7144391eb660af0daad4.jpg", "D:/diy/2/45735f32abdc27068513d84c6ede7dc4.png"};

//        CVUtil.compose(voicePath, pics, "D:\\diy\\2");


        AudioFile audioFile = AudioFileIO.read(new File(voicePath));
        int duration = audioFile.getAudioHeader().getTrackLength();
        System.out.println(duration);

    }

    @Test
    void composeAudio() throws Exception {


        CVUtil.mergeAudioAndVideo("D:\\diy\\2\\V2477954890421.mp4", "D:\\diy\\2\\VOICE2477954843077.mp3", "D:\\diy\\2\\a.mp4");

    }
}