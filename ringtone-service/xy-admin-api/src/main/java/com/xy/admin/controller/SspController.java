package com.xy.admin.controller;


import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.xy.admin.entity.*;
import com.xy.admin.service.SspRequestService;
import com.xy.admin.service.UrlBeianService;
import com.xy.base.core.response.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * SSP 成员状态变动回执
 */
@Slf4j
@RestController
@RequestMapping("/admin-api/ssp")
@Validated
@AllArgsConstructor
public class SspController {

    private final SspRequestService requestService;
    private final UrlBeianService beianService;

    /**
     * 成员状态变动回执
     *
     * @return
     */
    @PostMapping("/request")
    public Result<String> sspRequest(HttpServletRequest httpRequest, @RequestBody SspRequest request) throws JsonProcessingException {
        log.info("sspRequest method is invoked, request: " + request);

        try {
            request.setId(IdUtil.getSnowflake().nextId());
            // String ssp = getSspbyUrl(request.getUrl());
            String ssp = "llb";
            request.setSsp(ssp);
            requestService.save(request);
            return Result.success(ssp);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("捕获到异常: ", e);
        }
        return Result.success("失败");
    }

    /**
     * 根据 url 获取 SSP
     *
     * @return
     */
    private String getSspbyUrl(String url) {
        // 默认流量宝
        String sspDefault = "llb";
        if (null == url) {
            return sspDefault;
        }
        LambdaQueryWrapper<UrlBeian> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UrlBeian::getUrl, url)
                .last("LIMIT 1");
        UrlBeian one = beianService.getOne(wrapper);
        if (null == one || null == one.getEntity()) {
            return sspDefault;
        }
        if (one.getEntity().contains("易尊")) {
            return "llb";
        }
        if (one.getEntity().contains("众联") || one.getEntity().contains("流量果")) {
            return "llg";
        }
        return sspDefault;
    }

}
