package com.xy.admin.controller;


import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xy.admin.entity.AuthJl;
import com.xy.admin.entity.CallbackPro;
import com.xy.admin.service.AuthJlService;
import com.xy.admin.service.CallbackProService;
import com.xy.base.core.response.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 外链办理和巨量API回调
 *
 */
@Slf4j
@RestController
@RequestMapping("/admin-api/pro")
@Validated
@AllArgsConstructor
public class CallbackProController {

    private final CallbackProService proService;
    private final AuthJlService authJlService;
    private final ObjectMapper mapper;

    /**
     * 外链办理成功通知
     * @return
     */
    @PostMapping("/callback")
    public Result<String> callback(HttpServletRequest httpRequest, @RequestBody CallbackPro request) throws JsonProcessingException {
        // 获取请求者的 IP 地址
        String ipAddress = ServletUtil.getClientIP(httpRequest);
        log.info("pro callback method is invoked, request: " + request);
        log.info("Requester IP Address: " + ipAddress);

        try {
            request.setId(IdUtil.getSnowflake().nextId());
            request.setIp(ipAddress);
            proService.save(request);
            return Result.success("成功");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("捕获到异常: ", e);
        }
        return Result.success("失败");
    }

    /**
     * 巨量API回调
     * @return
     */
    @GetMapping("/juliang")
    public Result<String> juliang(HttpServletRequest httpRequest,
                                  @RequestParam String state,
                                  @RequestParam String auth_code) throws JsonProcessingException {
        // 获取请求者的 IP 地址
        String ipAddress = ServletUtil.getClientIP(httpRequest);
        log.info("juliang method is invoked, state={}, auth_code={} ", state, auth_code);
        log.info("Requester IP Address: " + ipAddress);

        try {
            AuthJl request = new AuthJl();
            request.setState(state);
            request.setAuthCode(auth_code);
            request.setId(IdUtil.getSnowflake().nextId());
            request.setIp(ipAddress);
            authJlService.save(request);
            return Result.success("成功");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("捕获到异常: ", e);
        }
        return Result.success("失败");
    }

}
