package com.xy.admin.controller;


import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xy.admin.entity.*;
import com.xy.admin.mapper.PhoneCheckMapper;
import com.xy.admin.service.*;
import com.xy.lib.migu.online.MemberOperation;
import com.xy.lib.migu.online.RingOperation;
import com.xy.lib.migu.util.AdTrackingUtil;
import com.xy.lib.migu.vo.MemberCallbackRequest;
import com.xy.lib.migu.vo.XamwCallbackRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Date;

/**
 * 成员管理
 */
@Slf4j
@RestController
@RequestMapping("/admin-api/lm/member-order-confirm")
@Validated
@AllArgsConstructor
public class MemberManegeController {

    private final LogApiService logApiService;
    private final ChannelReportService reportService;
    private final ConfigProService configProService;
    private final RingtoneTemplateService templateService;
    private final ProInfoService proInfoService;
    private final PhoneCheckService phoneCheckService;
    private final PhoneCheckMapper phoneCheckMapper;
    private final ObjectMapper mapper;

    /**
     * 成员状态变动回执
     */
    @PostMapping("/callback")
    public void callback(HttpServletRequest httpRequest, @RequestBody MemberCallbackRequest request) throws JsonProcessingException {
        // 获取请求者的 IP 地址
        String ipAddress = ServletUtil.getClientIP(httpRequest);
        log.info("callback method callback is invoked, request: " + request);
        log.info("Requester IP Address: " + ipAddress);

        try {
            if (request.getType() == 1) {
                LambdaQueryWrapper<ProInfo> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(ProInfo::getProductId, request.getOrderId())
                        .last("LIMIT 1"); // 限制只查询一条记录
                ProInfo one = proInfoService.getOne(queryWrapper);
                log.info("get proInfo record {}", one);
                if ("000000".equals(request.getState())) {
                    processNewSuccess(request, one);
                } else {
                    String desc = request.getDesc();
                    if (null != desc && desc.contains(one.getAdvertiser())) {
                        // 此时，该公司达量，报错示例：辰华无线用户发展数量已达当日上限
                        // request.setDesc("当日用户发展数量已达当日上限");
                        processNewFailReport(request, one);
                    } else if (null != desc && desc.contains("已达当日上限")) {
                        // 此时，该公司达量，报错示例：山东用户发展数量已达当日上限
                        processNewFailReport(request, one);
                    } else {
                        processNewFail(request);
                    }
                }
            } else if (request.getType() == 2 && "000000".equals(request.getState())) {
                // 不再做 phone_check和删除部门，以及退订回传时，没必要再去调用此方法
                // processDelSuccess(request);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("捕获到异常: ", e);
        }

        // 返回响应
        LogApi log = new LogApi();
        log.setMethod("POST");
        log.setTiming(0);
        log.setId(IdUtil.getSnowflake().nextId());
        log.setCreateTime(LocalDateTime.now());
        log.setIp((ServletUtil.getClientIP(httpRequest)));
        log.setRequest(mapper.writeValueAsString(request));
        log.setType(1);
        log.setUrl("/admin-api/lm/member-order-confirm/callback");

        logApiService.save(log);
    }

    /**
     * 成员状态变动回执-西安摩威
     */
    @PostMapping("/xamw")
    public void callback_xamw(HttpServletRequest httpRequest, @RequestBody XamwCallbackRequest xamwRequest) throws JsonProcessingException {
        // 获取请求者的 IP 地址
        String ipAddress = ServletUtil.getClientIP(httpRequest);
        log.info("callback method callback is invoked, request: " + xamwRequest);
        log.info("Requester IP Address: " + ipAddress);

        try {
            MemberCallbackRequest request = new MemberCallbackRequest();
            int type = 2;
            if("06".equals(xamwRequest.getOprType()) || "02".equals(xamwRequest.getOprType())){
                type = 1;
            }
            request.setType(type);
            String state = xamwRequest.getState();
            if("02".equals(xamwRequest.getOprType())){
                state = "000001";
            }
            request.setState(state);
            request.setOrderId("62500120730");
            request.setBillNum(xamwRequest.getMsisdn());
            request.setDesc(xamwRequest.getResultDesc());
            request.setFinishedTime(xamwRequest.getTimestamp());

            callback(httpRequest, request);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("捕获到异常: ", e);
        }
    }

    // 根据orderId查询pro名称
    private String getPro(String orderId) {
        LambdaQueryWrapper<ConfigPro> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConfigPro::getOrderid, orderId)
                .last("LIMIT 1"); // 限制只查询一条记录
        ConfigPro configPro = configProService.getOne(queryWrapper);
        if (null == configPro) {
            log.warn("orderId {} 找不到对应的合作商！", orderId);
            return "";
        } else {
            log.warn("orderId {} 对应的合作商={}", orderId, configPro.getPro());
            return configPro.getPro();
        }
    }

    /**
     * 处理新增且成功的用户
     */
    private void processNewSuccess(MemberCallbackRequest request, ProInfo proInfo) {
        // 处理回传
        String ringId = doHuichuan(request, proInfo, true);
        savePhoneCheck(request);
        // 为新用户设置默认铃声，或与素材对应的铃音，仅支持众视广联，江西组网
        if ("zsgl".equals(proInfo.getPro()) || "jxzw".equals(proInfo.getPro()) || "zs02".equals(proInfo.getPro())) {
            log.info("starting set ring for pro {}", proInfo.getPro());
            String departmentId = MemberOperation.queryDeptId(request.getBillNum(), proInfo.getUniqueAccId(), proInfo.getAccPassword(), proInfo.getProductId());
            log.info("department id for user {} is {}", request.getBillNum(), departmentId);
            if (null != departmentId) {
                String ringName = null;
                if (null == ringId) {
                    LambdaQueryWrapper<RingtoneTemplate> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(RingtoneTemplate::getIsDefault, 1)
                            .last("LIMIT 1");
                    RingtoneTemplate ringtoneTemplate = templateService.getOne(wrapper);
                    if (null != ringtoneTemplate) {
                        ringId = ringtoneTemplate.getId();
                        ringName = ringtoneTemplate.getName();
                    }
                }
                if (null != ringId) {
                    RingOperation.setRing(departmentId, "00:00:00", "23:59:59", Collections.singletonList(ringId), proInfo.getUniqueAccId(), proInfo.getAccPassword(), proInfo.getProductId());
                    log.info("set ring {}[{}] as default ring for user {}", ringName, ringId, request.getBillNum());
                }
            }
        }
    }

    // 处理回传
    private String doHuichuan(MemberCallbackRequest request, ProInfo proInfo, boolean success) {
        String pro = proInfo.getPro();
        LambdaQueryWrapper<ChannelReport> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChannelReport::getPhone, request.getBillNum())
                .eq(ChannelReport::getPro, pro)
                .eq(ChannelReport::getReportStatus, 0)
                .last("ORDER BY create_time desc LIMIT 1"); // 限制只查询一条记录
        ChannelReport channelReport = reportService.getOne(queryWrapper);
        log.info("get report record {}", channelReport);
        String ringId = null;
        if (null != channelReport) {
            if (channelReport.getReportStatus() == 0) {
                // 新增成功时，回传并更新状态
                if (success) {
                    boolean yihuichuan = AdTrackingUtil.doSuccessReport(channelReport.getCallback(), channelReport.getChannel(), channelReport.getSource(), channelReport.getPro(), channelReport.getPlatform(),
                            channelReport.getPhone(), channelReport.getParams(), channelReport.getUrl());
                    channelReport.setReportStatus(1);
                    if (yihuichuan) {
                        channelReport.setHuichuanStatus(1);
                    }
                    channelReport.setUpdateTime(new Date());
                    reportService.updateById(channelReport);
                }
                // 新增失败时，仅回传
                else {
                    AdTrackingUtil.doFailReport(channelReport.getCallback(), channelReport.getChannel(), channelReport.getSource(), channelReport.getPro(), channelReport.getPlatform(),
                            channelReport.getPhone(), channelReport.getParams(), channelReport.getUrl(), request.getDesc());
                }
                log.info("report successful: {}", channelReport);
            } else {
                log.info("report is nodelay, no more report is need");
            }
            ringId = channelReport.getRing();
        } else {
            log.warn("未获取到回传记录：{}", request);
        }
        return ringId;
    }

    /**
     * 处理新增且失败，但需调用回传，提示推广渠道已达量
     */
    private void processNewFailReport(MemberCallbackRequest request, ProInfo proInfo) {
        // 仅做回传，不修改 channel_report
        doHuichuan(request, proInfo, false);
    }

    /**
     * 处理新增且失败的用户
     */
    private void processNewFail(MemberCallbackRequest request) {
        String description = request.getDesc();
        if (null == description) {
            return;
        }
        if (description.startsWith("成员已存在")) {
            savePhoneCheck(request);
        }
    }

    /**
     * 将用户写入phone_check以便进行拦截
     *
     * @param request
     */
    private void savePhoneCheck(MemberCallbackRequest request) {
        LambdaQueryWrapper<PhoneCheck> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PhoneCheck::getBillnum, request.getBillNum())
                .last("ORDER BY create_time desc LIMIT 1"); // 限制只查询一条记录
        PhoneCheck phoneCheck = phoneCheckService.getOne(queryWrapper);
        if (null == phoneCheck) {
            phoneCheck = new PhoneCheck();
            phoneCheck.setBillnum(request.getBillNum());
            phoneCheck.setType(request.getType());
            phoneCheck.setDescription(request.getDesc());
            phoneCheckService.save(phoneCheck);
        } else {
            phoneCheck.setBillnum(request.getBillNum());
            phoneCheck.setType(request.getType());
            phoneCheck.setDescription(request.getDesc());
            phoneCheckService.updateById(phoneCheck);
        }
    }

    /**
     * 处理退订且成功的用户
     */
    private void processDelSuccess(MemberCallbackRequest request) {
        // 检查用户对应的部门，若无成员，则删除部门
        // 目前做不到：1. 确认是否为空部门需要根据部门id查询部门成员；2.用户已被删除是，已经查不出其原先对应的部门id
        // 但可借助自行组装部门名称查询部门id
        // 很多重复订阅的用户留下了太多部门删不掉，此处不是解决部门重复的根本办法，不再做退订时的部门删除
        /*
        DepartmentOperation.tryRemoveIfEmpty(request.getBillNum());
        // 移除 phone_check 中的记录
        LambdaQueryWrapper<PhoneCheck> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PhoneCheck::getBillnum, request.getBillNum());
        phoneCheckService.remove(queryWrapper);
         */
        // 此处暂仅做退订回传，与成功回传类似，但仅需调用回传接口，无需写数据库
        LambdaQueryWrapper<ChannelReport> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChannelReport::getPhone, request.getBillNum())
                .eq(ChannelReport::getReportStatus, 1)
                .last("ORDER BY create_time desc LIMIT 1"); // 限制只查询一条记录
        ChannelReport channelReport = reportService.getOne(queryWrapper);
        log.info("get report record {}", channelReport);
        if (null != channelReport) {
            // 回传并更新状态
            boolean success = AdTrackingUtil.doWithdrawReport(channelReport.getCallback(), channelReport.getPlatform(), channelReport.getPro());
            if (success) {
                log.info("report withdraw successful: {}", channelReport);
            }
        }
    }
}
