package com.xy.admin.controller;


import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xy.admin.entity.LogApi;
import com.xy.admin.entity.RingUpload;
import com.xy.admin.entity.RingtoneOrder;
import com.xy.admin.service.LogApiService;
import com.xy.admin.service.RingUploadService;
import com.xy.admin.service.RingtoneOrderService;
import com.xy.lib.migu.online.MemberOperation;
import com.xy.lib.migu.online.RingOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.Collections;

/**
 * 铃音管理
 */
@Slf4j
@RestController
@RequestMapping("/admin-api/lm")
@Validated
@AllArgsConstructor
public class RingManegeController {

    private final RingUploadService uploadService;
    private final RingtoneOrderService ringtoneOrderService;
    private final LogApiService logApiService;
    private final ObjectMapper mapper;

    /**
     * 铃音上传处理回执
     */
    @PostMapping("/videoring-process/callback")
    public void uploadCallback(HttpServletRequest httpRequest, @RequestBody RingUpload request) throws JsonProcessingException {
        // 获取请求者的 IP 地址
        String ipAddress = httpRequest.getRemoteAddr();

        log.info("callback method uploadCallback is invoked, request: " + request);
        log.info("Requester IP Address: " + ipAddress);

        LogApi logApi = new LogApi();
        logApi.setMethod("POST");
        logApi.setTiming(0);
        logApi.setId(IdUtil.getSnowflake().nextId());
        logApi.setCreateTime(LocalDateTime.now());
        logApi.setIp((ServletUtil.getClientIP(httpRequest)));
        logApi.setRequest(mapper.writeValueAsString(request));
        logApi.setType(1);
        logApi.setUrl("/admin-api/lm/videoring-process/callback");

        logApiService.save(logApi);

        uploadService.updateById(request);
    }

    /**
     * 铃音分发回执
     */
    @PostMapping("/videoring-dist/callback")
    public void distCallback(HttpServletRequest httpRequest, @RequestBody RingUpload request) throws JsonProcessingException, SQLException {
        // 获取请求者的 IP 地址
        String ipAddress = httpRequest.getRemoteAddr();

        // 处理业务逻辑
        log.info("callback method distCallback is invoked, request: " + request);
        log.info("Requester IP Address: " + ipAddress);

        // 返回响应
        LogApi logApi = new LogApi();
        logApi.setId(IdUtil.getSnowflake().nextId());
        logApi.setCreateTime(LocalDateTime.now());
        logApi.setIp((ServletUtil.getClientIP(httpRequest)));
        logApi.setMethod("POST");
        logApi.setRequest(mapper.writeValueAsString(request));
        logApi.setType(1);
        logApi.setTiming(0);
        logApi.setUrl("/admin-api/lm/videoring-dist/callback");

        logApiService.save(logApi);

        uploadService.updateById(request);

        // 因对应请求的实体类中有desc字段，使用Mybatis-plus查询时始终会报错
//        LambdaQueryWrapper<RingUpload> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(RingUpload::getStreamNumber, request.getStreamNumber());
//        final Map<String, Object> map = uploadService.getMap(queryWrapper);
//        log.info("streamNumber:{}, map:{}", request.getStreamNumber(), map);
//        final String orderNumber = map.get("order_number").toString();
//        log.info("streamNumber:{}, orderNumber:{}", request.getStreamNumber(), orderNumber);

        // 因该回调会被重复调用，部分省份成功，部分失败，故不做失败的更新，只要有省份分发成功，就认为分发成功了
        if (0 == request.getDeployRslt()) {
            // 因对应请求的实体类中有desc字段，使用Mybatis-plus查询时始终会报错

            LambdaQueryWrapper<RingUpload> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(RingUpload::getStreamNumber, request.getStreamNumber())
                    .last("LIMIT 1");
            RingUpload upload = uploadService.getOne(wrapper);
            if (null != upload) {
                String departmentId = upload.getDeptId();
                String ringId = upload.getRingId();
                String phone = upload.getPhone();
                // 如果 departmentId 不为空，该上传动作可能是外部供给，此时暂不更新ring_order
                // 如果 departmentId 为空，则查询订单号和手机号，再根据手机号查询部门
                if (null == departmentId) {
                    String orderNumber = upload.getOrderNumber();
                    final RingtoneOrder ringtoneOrder = new RingtoneOrder();
                    ringtoneOrder.setOrderNumber(orderNumber);
                    ringtoneOrder.setStatus(20);
                    ringtoneOrderService.updateById(ringtoneOrder);
                    departmentId = MemberOperation.queryDeptId(phone);
                }
                if (null != departmentId) {
                    // 设置铃音
                    RingOperation.setRing(departmentId, "00:00:00", "23:59:59", Collections.singletonList(ringId));
                    log.info("set deployed ring {} for user {} in department {}", ringId, phone, departmentId);
                }
            }
        }
    }
}
