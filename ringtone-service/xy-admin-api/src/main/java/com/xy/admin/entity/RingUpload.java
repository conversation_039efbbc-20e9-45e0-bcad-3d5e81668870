package com.xy.admin.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2024-02-04
 */
@TableName(value = "ring_upload")
@Data
public class RingUpload implements Serializable {

    /**
     * 铃音标识
     */
    @TableId
    private String streamNumber;
    /**
     * 视频模板订单id
     */
    private String orderNumber;
    /**
     * 手机
     */
    private String phone;
    /**
     * 部门id
     */
    private String deptId;
    /**
     * 铃音id
     */
    private String ringId;
    /**
     * 铃音名称
     */
    private String ringName;
    /**
     * 铃音路径
     */
    private String ringFilePath;
    /**
     * 铃音创建时间
     */
    private String createTime;
    /**
     * 1-视频  2-音频
     */
    private Integer ringType;
    /**
     * 铃音状态：
00 已上传
01 转码成功审核中
02 审核未通过
03 审核通过
06 分发成功
07 已过期
08 转码失败
     */
    private String ringStatus;
    /**
     * 状态描述
     */
    private String description;
    /**
     * 分发省份名称
     */
    private String provinceName;
    /**
     * 0-成功
1-失败
     */
    private Integer deployRslt;
    /**
     * 
     */
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
