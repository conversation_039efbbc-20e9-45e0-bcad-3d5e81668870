package com.xy.admin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "spring.datasource")
@Data // Lombok生成getter/setter
public class DataSourceConfig {
    private String url;
    private String username;
    private String password;
    private String driverClassName;
}
