<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.admin.mapper.AuthJlMapper">

    <resultMap id="BaseResultMap" type="com.xy.admin.entity.AuthJl">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="state" column="state" jdbcType="VARCHAR"/>
            <result property="authCode" column="auth_code" jdbcType="VARCHAR"/>
            <result property="ip" column="ip" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,state,auth_code,
        ip,create_time,update_time
    </sql>
</mapper>
