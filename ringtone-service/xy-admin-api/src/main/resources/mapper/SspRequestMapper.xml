<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.admin.mapper.SspRequestMapper">

    <resultMap id="BaseResultMap" type="com.xy.admin.entity.SspRequest">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="channel" column="channel" jdbcType="VARCHAR"/>
            <result property="source" column="source" jdbcType="VARCHAR"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="action" column="action" jdbcType="VARCHAR"/>
            <result property="ssp" column="ssp" jdbcType="VARCHAR"/>
            <result property="url" column="url" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,channel,source,
        phone,action,ssp,
        url,create_time,update_time
    </sql>
</mapper>
