<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.admin.mapper.CallbackProMapper">

    <resultMap id="BaseResultMap" type="com.xy.admin.entity.CallbackPro">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="product" column="product" jdbcType="VARCHAR"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="SMALLINT"/>
            <result property="channel" column="channel" jdbcType="VARCHAR"/>
            <result property="source" column="source" jdbcType="VARCHAR"/>
            <result property="reportStatus" column="report_status" jdbcType="SMALLINT"/>
            <result property="clickid" column="clickid" jdbcType="VARCHAR"/>
            <result property="llgclickid" column="llgclickid" jdbcType="VARCHAR"/>
            <result property="ip" column="ip" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,product,phone,
        type,channel,source,
        report_status,clickid,llgclickid,
        ip,create_time,update_time
    </sql>
</mapper>
