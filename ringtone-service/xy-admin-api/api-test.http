# xy-admin-api 接口测试文件
# 服务地址: http://localhost:8917/admin

### 1.1 外链办理成功通知
POST http://localhost:8917/admin/admin-api/pro/callback
Content-Type: application/json

{
  "orderId": "12345678901234567890",
  "billNum": "13800138000",
  "state": "000000",
  "desc": "办理成功",
  "finishedTime": "2025-06-27 18:30:00"
}

### 1.2 巨量API回调
GET http://localhost:8917/admin/admin-api/pro/juliang?state=test_state_001&auth_code=auth_code_123456

### 2.1 成员状态变动回执 - 新增成功
POST http://localhost:8917/admin/admin-api/lm/member-order-confirm/callback
Content-Type: application/json

{
  "type": 1,
  "state": "000000",
  "orderId": "62500120730",
  "billNum": "13800138001",
  "desc": "新增成功",
  "finishedTime": "2025-06-27 18:30:00"
}

### 2.2 成员状态变动回执 - 新增失败
POST http://localhost:8917/admin/admin-api/lm/member-order-confirm/callback
Content-Type: application/json

{
  "type": 1,
  "state": "000001",
  "orderId": "62500120730",
  "billNum": "13800138002",
  "desc": "新增失败",
  "finishedTime": "2025-06-27 18:30:00"
}

### 2.3 成员状态变动回执 - 退订成功
POST http://localhost:8917/admin/admin-api/lm/member-order-confirm/callback
Content-Type: application/json

{
  "type": 2,
  "state": "000000",
  "orderId": "62500120730",
  "billNum": "13800138003",
  "desc": "退订成功",
  "finishedTime": "2025-06-27 18:30:00"
}

### 2.4 西安摩威回调 - 办理
POST http://localhost:8917/admin/admin-api/lm/member-order-confirm/xamw
Content-Type: application/json

{
  "oprType": "06",
  "state": "000000",
  "msisdn": "13800138004",
  "resultDesc": "办理成功",
  "timestamp": "2025-06-27 18:30:00"
}

### 2.5 西安摩威回调 - 退订
POST http://localhost:8917/admin/admin-api/lm/member-order-confirm/xamw
Content-Type: application/json

{
  "oprType": "02",
  "state": "000000",
  "msisdn": "13800138005",
  "resultDesc": "退订成功",
  "timestamp": "2025-06-27 18:30:00"
}

### 3.1 铃音上传处理回执
POST http://localhost:8917/admin/admin-api/lm/videoring-process/callback
Content-Type: application/json

{
  "id": 1001,
  "streamNumber": "STREAM_20250627_001",
  "processRslt": 0,
  "desc": "上传处理成功",
  "ringId": "RING_001",
  "phone": "13800138006",
  "orderNumber": "ORDER_20250627_001"
}

### 3.2 铃音分发回执 - 成功
POST http://localhost:8917/admin/admin-api/lm/videoring-dist/callback
Content-Type: application/json

{
  "id": 1002,
  "streamNumber": "STREAM_20250627_002",
  "deployRslt": 0,
  "desc": "分发成功",
  "ringId": "RING_002",
  "phone": "13800138007",
  "orderNumber": "ORDER_20250627_002",
  "deptId": "DEPT_001"
}

### 3.3 铃音分发回执 - 失败
POST http://localhost:8917/admin/admin-api/lm/videoring-dist/callback
Content-Type: application/json

{
  "id": 1003,
  "streamNumber": "STREAM_20250627_003",
  "deployRslt": 1,
  "desc": "分发失败",
  "ringId": "RING_003",
  "phone": "13800138008",
  "orderNumber": "ORDER_20250627_003"
}

### 4.1 SSP请求 - 流量宝
POST http://localhost:8917/admin/admin-api/ssp/request
Content-Type: application/json

{
  "url": "http://example.com/test1",
  "userId": "user_001",
  "channel": "channel_001",
  "source": "source_001"
}

### 4.2 SSP请求 - 流量果
POST http://localhost:8917/admin/admin-api/ssp/request
Content-Type: application/json

{
  "url": "http://众联.com/test2",
  "userId": "user_002",
  "channel": "channel_002",
  "source": "source_002"
}

#########################
### 测试说明
#########################

# 1. 服务器配置
# - 端口: 8917
# - 上下文路径: /admin
# - 完整基础URL: http://localhost:8917/admin

# 2. 接口分组
# - CallbackProController: 处理外链办理和巨量API回调
# - MemberManegeController: 处理成员状态变动
# - RingManegeController: 处理铃音上传和分发
# - SspController: 处理SSP请求

# 3. 测试数据说明
# - 所有手机号使用138开头的测试号码
# - 订单号、流水号等使用固定格式
# - 时间统一使用当前时间

# 4. 状态码说明
# - 000000: 成功
# - 000001: 失败
# - type: 1=新增, 2=退订
# - deployRslt/processRslt: 0=成功, 1=失败

# 5. 注意事项
# - 确保服务已启动并监听8917端口
# - 某些接口可能需要数据库中存在对应的配置数据
# - 回调接口通常不需要认证，但可能有IP白名单限制 