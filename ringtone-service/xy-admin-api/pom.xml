<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>xy-services</artifactId>
        <groupId>com.xy</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.xy.admin.api</groupId>
    <artifactId>xy-admin-api</artifactId>
    <description>回调操作</description>
    <packaging>jar</packaging>
    <version>1.0.0</version>

    <properties>
        <maven.test.skip>true</maven.test.skip>
    </properties>


    <dependencies>
        <!-- 依赖 xy-admin 核心模块，通过传递依赖获得所有必需的依赖项 -->
        <dependency>
            <groupId>com.xy.admin</groupId>
            <artifactId>xy-admin</artifactId>
            <version>1.0.0</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>