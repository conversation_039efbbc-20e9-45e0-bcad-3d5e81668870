ARG PROFILE
FROM frolvlad/alpine-java:jdk8-slim
MAINTAINER Lee
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
VOLUME /tmp
ARG JAR_FILE
ADD target/xy-resource-1.0.0.jar /app.jar

ARG PROFILE
ENV env=$PROFILE

ENTRYPOINT exec java -server -XX:MetaspaceSize=32m -XX:MaxMetaspaceSize=256m -Xms256m -Xmx256m -Xmn128m -Xss256k -XX:+UseParNewGC -XX:+UseConcMarkSweepGC -jar /app.jar --spring.profiles.active=${env}

