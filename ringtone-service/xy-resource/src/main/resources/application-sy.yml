spring:
  cloud:
    nacos:
      discovery:
        enabled: true
        server-addr: 192.168.1.86:8848

  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************
    username: sysaas
    password: Ku#mf0dZ
    hikari:
      connection-test-query: select 1 from dual
      connection-timeout: 30000
      idle-timeout: 60000
      max-lifetime: 1800000
      maximum-pool-size: 20
      minimum-idle: 1

  redis:
    database: 6
    host: ************
    password: YAGi2acwr4oIBTbB8geq
    port: 6378

logging:
  config: classpath:logback-spring-sy.xml

upload:
  basePath: /opt/oss/upload/
  fullImg: /opt/oss/upload/drug/full
  miniImg: /opt/oss/upload/drug/mini
  originalImg: /opt/oss/upload/drug/original
  waterMark180Img: /opt/oss/upload/drug/waterMark180.png
  waterMark800Img: /opt/oss/upload/drug/waterMark800.png
  wholePath: https://sa.tuochuangyiyao.com:4443/upload/

ali:
  accessKeyId: LTAI5tMNRHrmK1kBiipEjnkK
  secret: ******************************
  endPoint: https://oss-cn-chengdu.aliyuncs.com
  bucketName: sysaas-dev
  point: ossdev.jingkongcloud.com
  uploadCallBackUrl: https://sy.tuochuangyiyao.com:4433/res/upload/uploadCallBack