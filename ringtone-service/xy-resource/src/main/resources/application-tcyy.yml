spring:
  cloud:
    nacos:
      discovery:
        enabled: true
        server-addr: http://*************:6846

  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************
    username: tcyy
    password: tcdbtc
    hikari:
      connection-test-query: select 1 from dual
      connection-timeout: 30000
      idle-timeout: 60000
      max-lifetime: 1800000
      maximum-pool-size: 20
      minimum-idle: 1

  redis:
    database: 0
    host: *************
    password: On34QubzIEXx3Gs28vuf03GiUg1JMlZH
    port: 6379


logging:
  config: classpath:logback-spring-tcyy.xml

upload:
  basePath: /opt/oss/upload/
  fullImg: /opt/oss/upload/drug/full
  miniImg: /opt/oss/upload/drug/mini
  originalImg: /opt/oss/upload/drug/original
  waterMark180Img: /opt/oss/upload/drug/waterMark180.png
  waterMark800Img: /opt/oss/upload/drug/waterMark800.png
  wholePath: https://sa.tuochuangyiyao.com:4443/upload/

ali:
  accessKeyId: LTAI5tMNRHrmK1kBiipEjnkK
  secret: ******************************
  endPoint: https://oss-cn-chengdu.aliyuncs.com
  bucketName: sysaas
  point: oss.jingkongcloud.com
  uploadCallBackUrl: https://control.tuochuangyiyao.com/res/upload/uploadCallBack
