package com.xy.res;

import com.xy.res.property.AliProperty;
import com.xy.res.property.UploadProperty;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * <AUTHOR>
 * @since 2022/12/5
 */
@SpringBootApplication
@EnableDiscoveryClient
@MapperScan("com.xy.*.mapper")
@EnableConfigurationProperties(value = {AliProperty.class, UploadProperty.class})
public class ResourceApplication {
    public static void main(String[] args) {
        SpringApplication.run(ResourceApplication.class, args);
    }
}
