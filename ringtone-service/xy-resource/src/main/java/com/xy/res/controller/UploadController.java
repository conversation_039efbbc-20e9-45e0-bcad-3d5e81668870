package com.xy.res.controller;


import com.xy.base.core.annotation.DirectOutput;
import com.xy.base.core.annotation.DisableLoggerRequest;
import com.xy.base.core.enums.ExceptionResultEnum;
import com.xy.base.core.exception.AppException;
import com.xy.base.core.response.Result;
import com.xy.res.service.AliOssService;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022年09月19日 11:42
 */
@RequiredArgsConstructor
@RestController
@RequestMapping(value = "/upload")
public class UploadController {

    private final AliOssService aliOssService;

    /**
     * 上传文件到本地
     *
     * @param multipartFile multipartFile
     * @param type          1商品图片，2商品明细，3banner，4品牌，5excel，6公告，8资质，10采购表，20业务员打卡，30电子发票，101套餐,35对码中心
     * @return com.sysaas.starter.response.Result<java.lang.String>
     * <AUTHOR>
     * @since 11:50 2022.09.19
     **/
    @RequestMapping("/local")
    public Result<String> upload(@RequestParam("file") MultipartFile multipartFile,
                                 @RequestParam(value = "type", required = false) Integer type) {
        boolean flag = multipartFile.isEmpty() || !StringUtils.hasText(multipartFile.getOriginalFilename());
        AppException.tnt(flag, ExceptionResultEnum.PARAMS_ERROR);
        return Result.success(aliOssService.uploadLocal(multipartFile, type));
    }

    /**
     * 上传文件到ali-oss
     *
     * @param multipartFile multipartFile
     * @param type type
     * @return com.sysaas.starter.response.Result<java.lang.String>
     * <AUTHOR>
     * @since 11:41 2022.09.20
     **/
    @RequestMapping(value = "/uploadAliOss")
    public Result<String> uploadAliOss(@RequestParam("file") MultipartFile multipartFile,
                                       @RequestParam(value = "type", required = false) Integer type) {
        boolean flag = multipartFile.isEmpty() || !StringUtils.hasText(multipartFile.getOriginalFilename());
        AppException.tnt(flag, ExceptionResultEnum.PARAMS_ERROR);
        return Result.success(aliOssService.uploadAliOss(multipartFile, type));
    }

    /**
     * 阿里云上传文件回调
     *
     * @param request request
     * <AUTHOR>
     * @since 14:02 2022.09.21
     **/
    @DisableLoggerRequest
    @DirectOutput
    @RequestMapping("/uploadCallBack")
    public void uploadCallBack(HttpServletRequest request) throws IOException {
        String content;
        try {
            BufferedReader reader = request.getReader();
            content = new BufferedReader(reader)
                    .lines().collect(Collectors.joining(System.lineSeparator()));
        } catch (Exception e) {
            ServletInputStream inputStream = request.getInputStream();
            content = new BufferedReader(new InputStreamReader(inputStream))
                    .lines().collect(Collectors.joining(System.lineSeparator()));
        }
        aliOssService.uploadCallBack(content);
    }


}
