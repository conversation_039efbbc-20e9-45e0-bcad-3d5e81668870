package com.xy.res.controller;

import com.xy.base.core.dto.export.ExportDTO;
import com.xy.base.core.exception.AppException;
import com.xy.res.entity.ExportRecord;
import com.xy.res.service.ExportRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 数据导出相关
 *
 * <AUTHOR>
 * @since 2022/8/11.
 */
@RestController
@RequestMapping("/export")
@RequiredArgsConstructor
public class ExportController {

    private final ExportRecordService exportRecordService;

    /**
     * 获取导出作业相关状态
     *
     * @param exportId exportId
     */
    @GetMapping("/status/{exportId}")
    public ExportRecord status(@PathVariable("exportId") Long exportId) {

        return exportRecordService.getById(exportId);
    }

    /**
     * 开始进行导出作业，得到作业id后，通过status方法查询作业状态
     */
    @PostMapping("/start")
    public ExportRecord start(@RequestBody @Valid ExportDTO dto) {
        AppException.tnt(dto.getDatas().isEmpty(), "没有可以导出的数据");
        return exportRecordService.export(dto);
    }

}
