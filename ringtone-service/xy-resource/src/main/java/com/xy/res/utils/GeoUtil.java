package com.xy.res.utils;

import lombok.SneakyThrows;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.util.DigestUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @since 2022/10/10
 */
public class GeoUtil {

    private static final String AK = "yeifCy8jB7Z7MU7H3ftclwTXAr4GxqNO";
    private static final String SK = "eNdgetTcO0UQsH3N7CU3wvKvG13VG6nY";

    private static final String GEO_SERVER = "https://api.map.baidu.com";
    private static final String GEO_API_CODING = "/geocoding/v3/?";
    private static final String GEO_API_REVERSE = "/reverse_geocoding/v3/?";


    /**
     * 正地理编码，将地址解析为经纬度
     *
     * @param address address
     */
    @SneakyThrows
    public static String geocoding(String address) {

        LinkedHashMap<String, String> map = buildBaseMap();
        map.put("address", address);
        map.put("ret_coordtype", "gcj02ll");

        return getRequest(map, GEO_API_CODING);

    }


    /**
     * 逆地理编码，将经纬度解析为行政区划
     * lat lng
     *
     * @param location 经纬度，格式30.6801262408,104.017167148
     */
    @SneakyThrows
    public static String reverseGeocoding(String location) {

        LinkedHashMap<String, String> map = buildBaseMap();
        map.put("coordtype", "gcj02ll");
        map.put("extensions_town", "true");
        map.put("location", URLEncoder.encode(location, "UTF-8"));

        return getRequest(map, GEO_API_REVERSE);

    }

    public static String getRequest(LinkedHashMap<String, String> paramsMap, String api) throws UnsupportedEncodingException {

        String params = map2kv(paramsMap);
        String req = api + params + SK;
        String sn = DigestUtils.md5DigestAsHex(URLEncoder.encode(req, "UTF-8").getBytes(StandardCharsets.UTF_8));
        String url = GEO_SERVER + api + params + "&sn=" + sn;

        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromHttpUrl(url);
        URI uri = uriComponentsBuilder.build(true).toUri();

        return restTemplate.exchange(uri, HttpMethod.GET, entity, String.class).getBody();
    }

    public static String map2kv(LinkedHashMap<String, String> map) {

        StringJoiner sj = new StringJoiner("&");
        map.forEach((k, v) -> {
            try {
                String val = !k.equals("location") ? URLEncoder.encode(v, "UTF-8") : v;
                sj.add(k + "=" + val);
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        });

        return sj.toString();
    }

    public static LinkedHashMap<String, String> buildBaseMap() {
        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        map.put("ak", AK);
        map.put("output", "json");

        return map;
    }

}
