package com.xy.res.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xy.res.entity.Map;
import com.xy.res.vo.MapTree;
import com.xy.res.vo.Region;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MapService extends IService<Map> {

    /**
     * 从百度地图开发资源导入行政区划数据
     *
     * @param filePath xlsx路径
     */
    void importFromBaidu(String filePath);

    /**
     * 修复直辖县问题
     *
     * @param filePath xlsx路径
     */
    void importFixData(String filePath);


    /**
     * 获取完整地图
     */
    List<MapTree> buildTree();

    Region findByAdr(String address);
}
