package com.xy.res.controller;


import com.xy.base.core.exception.AppException;
import com.xy.base.core.util.MapUtils;
import com.xy.res.entity.Map;
import com.xy.res.service.MapService;
import com.xy.res.vo.FullMapVO;
import com.xy.res.vo.MapTree;
import com.xy.res.vo.MapVO;
import com.xy.res.vo.Region;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 地图相关接口
 *
 * <AUTHOR>
 * @since 2022/9/7.
 */
@RestController
@RequestMapping("/map")
@RequiredArgsConstructor
public class MapController {

    @Value("${map.version}")
    private Integer version;

    private final MapService mapService;

    /**
     * 根据Pid获取地图列表
     *
     * @param pCode 上级code
     * @return List<Map>
     * @apiNote pid为0表示获取省份
     */
    @GetMapping("/findChildMap/{pCode}")
    public List<MapVO> findChildMapController(@PathVariable("pCode") String pCode) {

        List<Map> maps = mapService.lambdaQuery().eq(Map::getPCode, pCode).list();

        List<MapVO> vos = new ArrayList<>();

        maps.forEach(m -> {
            MapVO vo = new MapVO();
            BeanUtils.copyProperties(m, vo);
            vos.add(vo);
        });

        return vos;
    }

    /**
     * 获取地图版本号
     *
     * @return int
     */
    @GetMapping("/version")
    public int version() {

        return version;
    }


    /**
     * 获取完整地图
     *
     * @return List<Map>
     * @apiNote pid为0表示获取省份
     */
    @GetMapping("/fullMap")
    public FullMapVO fullMap() {

        FullMapVO vo = new FullMapVO();

        List<MapTree> tree = mapService.buildTree();

        vo.setVersion(version);
        vo.setMaps(tree);
        return vo;
    }

    /**
     * 传入address返回完整的Region对象
     *
     * @param address address
     * @return Region
     */
    @GetMapping("/findByAddress/{address}")
    public Region findByAddress(@PathVariable("address") String address) {
        return mapService.findByAdr(address);
    }


    /**
     * 将地区代码转换为对应的行政区域
     *
     * @return Region
     */
    @PostMapping("/code2value")
    public List<MapVO> code2value(@RequestBody List<String> codes) {

        AppException.tnt(codes == null || codes.size() < 1, "参数错误");

        List<String> extracts = MapUtils.extractMapSelect(codes);
        List<Map> maps = mapService.lambdaQuery().in(Map::getCode, extracts).list();
        List<MapVO> vos = new ArrayList<>();

        maps.forEach(m -> {
            MapVO vo = new MapVO();
            BeanUtils.copyProperties(m, vo);
            vos.add(vo);
        });

        return vos;
    }


}
