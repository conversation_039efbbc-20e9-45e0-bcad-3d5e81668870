package com.xy.res.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xy.base.core.enums.StatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @TableName export_record
 */
@TableName(value = "sys_export_record")
@Data
public class ExportRecord implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId
    private Long id;
    /**
     * 导出的主要表名
     */
    private String tableName;
    /**
     * 导出状态（-1：导出失败；0；初始化，1：导出中，100：导出成功）
     */
    private StatusEnum status;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 操作者姓名
     */
    private String operator;
    /**
     * 数据总量
     */
    private Integer total;
    /**
     * 当前生成条数
     */
    private Integer current;
    /**
     * 查询条件（非必须）
     */
    private String query;
    /**
     * 文件访问路径
     */
    private String accessPath;
    /**
     *
     */
    private LocalDateTime createTime;
    /**
     * 完成时间
     */
    private LocalDateTime completeTime;
}