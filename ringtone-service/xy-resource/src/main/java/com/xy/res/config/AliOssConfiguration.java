package com.xy.res.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.xy.res.property.AliProperty;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022年09月19日 11:04
 */
@RequiredArgsConstructor
@Component
public class AliOssConfiguration {

    private final AliProperty aliProperty;

    public OSS creatOssClient() {
        try {
            return new OSSClientBuilder().build(aliProperty.getEndPoint(), aliProperty.getAccessKeyId(),
                    aliProperty.getSecret());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
