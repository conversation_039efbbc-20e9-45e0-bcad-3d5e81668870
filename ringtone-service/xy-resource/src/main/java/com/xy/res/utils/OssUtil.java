package com.xy.res.utils;

import cn.hutool.core.text.StrBuilder;
import com.xy.base.core.util.CommonUtils;
import com.xy.res.property.AliProperty;
import com.xy.res.property.UploadProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2022年09月20日 11:11
 */
@Slf4j
public class OssUtil {

    /**
     * 获取文件上传时的路劲
     *
     * @param type type
     * @return java.lang.String
     * <AUTHOR>
     * @since 11:12 2022.09.20
     **/
    public static String getFolder(Integer type) {
        String folder;
        int tp = type == null ? 0 : type;
        String date = CommonUtils.getDateStr("yyyyMMdd") + "/";
        switch (tp) {
            case 1:
                folder = "product/";
                break;
            case 2:
                folder = "detail/" + date;
                break;
            case 3:
                folder = "banner/" + date;
                break;
            case 4:
                folder = "brand/" + date;
                break;
            case 5:
                folder = "excel/" + date;
                break;
            case 6:
                folder = "notice/" + date;
                break;
            case 8:
                folder = "certification/" + date;
                break;
            case 10:
                folder = "purchase/" + date;
                break;
            case 20:
                folder = "checkin/" + date;
                break;
            case 30:
                folder = "invoice/" + date;
                break;
            case 35:
                folder = "medicine/" + date;
                break;
            case 101:
                folder = "package/" + date;
                break;
            default:
                folder = "other/" + date;
                break;
        }
        return folder;
    }

    /**
     * 添加图片水印
     *
     * @param path path
     * @param fileName fileName
     * @param uploadProperty uploadProperty
     * @return java.lang.Boolean
     * <AUTHOR>
     * @since 11:50 2022.09.20
     **/
    public static Boolean imgAddWatermark(String path, String fileName, UploadProperty uploadProperty) {
        if (!StringUtils.hasText(path)) {
            log.info("无图片内容");
            return false;
        }

        return false;
    }

    /**
     * 生成文件地址
     *
     * @param aliProperty aliProperty
     * @param objectName objectName
     * @return java.lang.String
     * <AUTHOR>
     * @since 15:46 2022.09.20
     **/
    public static String getAliOssFilePath(AliProperty aliProperty, String objectName) {
        StrBuilder sb = new StrBuilder();
        sb.append("https://").append(aliProperty.getPoint())
                .append("/").append(objectName);
        return sb.toString();
    }

    /**
     * 生成上传文件到本地后的完整地址
     *
     * @param uploadProperty uploadProperty
     * @param lastPath lastPath
     * @return java.lang.String
     * <AUTHOR>
     * @since 14:32 2022.09.23
     **/
    public static String getLocalPath(UploadProperty uploadProperty, String lastPath) {
        StrBuilder sb = new StrBuilder();
        sb.append(uploadProperty.getWholePath())
                .append(lastPath);
        return sb.toString();
    }
}
