package com.xy.res.dto.baidu;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/12/5
 */
@Data
public class GeoReverseInfo {

    private Location location;
    private String formatted_address;
    private String business;
    private AddressComponent addressComponent;
    private List<String> pois;
    private List<String> roads;
    private List<String> poiRegions;
    private String sematic_description;
    private int cityCode;

    @Data
    public static class AddressComponent {

        private String country;
        private int country_code;
        private String country_code_iso;
        private String country_code_iso2;
        private String province;
        private String city;
        private int city_level;
        private String district;
        private String town;
        private String town_code;
        private String distance;
        private String direction;
        private String adcode;
        private String street;
        private String street_number;
    }
}
