package com.xy.res.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.base.core.constant.CommonConsts;
import com.xy.base.core.dto.export.ExportDTO;
import com.xy.base.core.enums.StatusEnum;
import com.xy.base.core.util.ThreadUtil;
import com.xy.base.starter.security.util.SecurityUtils;
import com.xy.res.entity.ExportRecord;
import com.xy.res.mapper.ExportRecordMapper;
import com.xy.res.service.ExportRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ExportRecordServiceImpl extends ServiceImpl<ExportRecordMapper, ExportRecord>
        implements ExportRecordService {

    @Value("${upload.basePath}")
    private String baseUploadPath;

    @Value("${upload.wholePath}")
    private String wholePath;

    @Override
    public ExportRecord export(ExportDTO dto) {


        long id = IdUtil.getSnowflakeNextId();
        ExportRecord record = new ExportRecord();
        BeanUtils.copyProperties(dto, record);
        record.setId(id);
        record.setOperator(SecurityUtils.current().getRealName());
        record.setCreateTime(LocalDateTime.now());
        record.setStatus(StatusEnum.INIT);
        record.setFileName(dto.getFileName() + CommonConsts.LOCAL_DATETIME_COMPACT_FORMATTER.format(LocalDateTime.now()));
        record.setAccessPath(wholePath + "exports/" + record.getTableName() + record.getFileName());
        record.setTotal(dto.getDatas().size());
        record.setCurrent(0);
        save(record);

        String filePath = baseUploadPath + "exports/" + record.getTableName();
        FileUtil.mkdir(filePath);
        final String absFile = StrUtil.format("{}/{}.xlsx", filePath, record.getFileName());

        ThreadUtil.execute("导出数据", (t) -> {
            try (BigExcelWriter writer = ExcelUtil.getBigWriter(absFile)) {
                writer.write(dto.getDatas(), true);
                record.setStatus(StatusEnum.SUCCESS);
                record.setCompleteTime(LocalDateTime.now());
            } catch (Exception e) {
                log.error("export", e);
                record.setStatus(StatusEnum.FAIL);
                record.setCompleteTime(LocalDateTime.now());
            } finally {
                updateById(record);
            }
        });
        return record;
    }
}




