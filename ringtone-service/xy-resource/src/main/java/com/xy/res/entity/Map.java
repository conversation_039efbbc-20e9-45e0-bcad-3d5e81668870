package com.xy.res.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 行政区划（基于百度地图22年7月数据）
 *
 * <AUTHOR>
 * @since 2022-10-10
 */
@TableName(value = "sys_map")
@Data
public class Map implements Serializable {

    /**
     * 简化代码
     */
    @TableId
    private String code;

    /**
     * 行政区划名称
     */
    private String name;
    /**
     * 级别，1省，2市，3区，4县
     */
    private Integer level;
    /**
     * 上一级代码，0表示省（无上级）
     */
    private String pCode;
    /**
     * 完整路径
     */
    private String fullPath;
    /**
     * 标准代码，直辖县简化代码和标准代码有差异
     */
    private String fullCode;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
