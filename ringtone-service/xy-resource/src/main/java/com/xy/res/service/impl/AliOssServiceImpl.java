package com.xy.res.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.crypto.digest.MD5;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.CreateBucketRequest;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.xy.base.core.exception.AppException;
import com.xy.res.config.AliOssConfiguration;
import com.xy.res.property.AliProperty;
import com.xy.res.property.UploadProperty;
import com.xy.res.service.AliOssService;
import com.xy.res.utils.OssUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;

/**
 * <AUTHOR>
 * @date 2022年09月19日 14:16
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AliOssServiceImpl implements AliOssService {

    private final AliOssConfiguration aliOssConfiguration;
    private final AliProperty aliProperty;
    private final UploadProperty uploadProperty;


    @Override
    public String uploadAliOss(MultipartFile multipartFile, Integer type) {
        OSS ossClient = aliOssConfiguration.creatOssClient();
        AppException.tnt(null == ossClient, "ali-oss连接错误");
        try {
            String folder = OssUtil.getFolder(type);
            String fileName = multipartFile.getOriginalFilename();
            String finalFileName = MD5.create().digestHex(multipartFile.getBytes()) + "." + FileUtil.extName(fileName);
            String objectName = folder + finalFileName;
            // 判断存储空间是否存在
            boolean bucketExist = ossClient.doesBucketExist(aliProperty.getBucketName());
            if (!bucketExist) {
                CreateBucketRequest createBucketRequest = new CreateBucketRequest(aliProperty.getBucketName());
                createBucketRequest.setCannedACL(CannedAccessControlList.PublicRead);
                ossClient.createBucket(createBucketRequest);
            }
            // 判断文件是否存在
            boolean objectExist = ossClient.doesObjectExist(aliProperty.getBucketName(), objectName);
            if (objectExist) {
                return OssUtil.getAliOssFilePath(aliProperty, objectName);
            }
            // 上传部分
            byte[] fileBytes = multipartFile.getBytes();

            ByteArrayInputStream inputStream = new ByteArrayInputStream(fileBytes);
            PutObjectRequest putObjectRequest = new PutObjectRequest(aliProperty.getBucketName(), objectName, inputStream);
            // 设置文件元信息，包含权限
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setObjectAcl(CannedAccessControlList.PublicRead);
            metadata.setContentType(multipartFile.getContentType());
            metadata.setHeader("x-oss-forbid-overwrite", "true");
            putObjectRequest.setMetadata(metadata);
            ossClient.putObject(putObjectRequest);
            inputStream.close();
            return OssUtil.getAliOssFilePath(aliProperty, objectName);
        } catch (Exception e) {
            log.error("上传文件到ali-oss错误信息>>>>>:{}", e.getMessage());
            e.printStackTrace();
        } finally {
            ossClient.shutdown();
        }
        throw new AppException("上传失败，请稍后重试");
    }

    @Override
    public String uploadLocal(MultipartFile multipartFile, Integer type) {
        try {
            String folder = OssUtil.getFolder(type);
            String fileName = multipartFile.getOriginalFilename();
            String finalFileName = MD5.create().digestHex(multipartFile.getBytes()) + "." + FileUtil.extName(fileName);
            String finalPath = uploadProperty.getBasePath() + folder;
            String returnPath = folder + finalFileName;
            if (!FileUtil.exist(finalPath)) {
                FileUtil.mkdir(finalPath);
            }
            if (FileUtil.exist(finalPath + finalFileName)) {
                return OssUtil.getLocalPath(uploadProperty, returnPath);
            }
            FileUtil.writeBytes(multipartFile.getBytes(), finalPath + finalFileName);
            boolean typeFlag = Integer.valueOf(1).equals(type);
            if (typeFlag) {
                Boolean addWatermark = OssUtil.imgAddWatermark(finalPath + finalFileName, finalFileName, uploadProperty);
                log.info("图片水印添加结果:>>>>>,{}", addWatermark ? "成功" : "失败");
            }
            return OssUtil.getLocalPath(uploadProperty, returnPath);
        } catch (Exception e) {
            log.error("上传文件到本地错误信息>>>>>:{}", e.getMessage());
            e.printStackTrace();
        }
        throw new AppException("上传失败，请稍后重试");
    }

    @Override
    public void uploadCallBack(String content) {
        log.info("ali-oss上传文件回调内容>>>>>:{}", content);
    }

}
