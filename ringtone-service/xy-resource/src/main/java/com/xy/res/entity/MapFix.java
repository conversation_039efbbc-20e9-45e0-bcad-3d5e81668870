package com.xy.res.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 修正部分县级直辖市编码导致短码不能用的问题
 *
 * <AUTHOR>
 * @since 2022-10-10
 */
@TableName(value = "sys_map_fix")
@Data
public class MapFix implements Serializable {


    /**
     * 
     */
    @TableId
    private String code;
    /**
     * 
     */
    private String name;
    /**
     * 
     */
    private Integer level;
    /**
     * 
     */
    private String fixCode;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
