package com.xy.res.service.impl;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xy.res.dto.baidu.BdMapResult;
import com.xy.res.dto.baidu.GeoCodingInfo;
import com.xy.res.dto.baidu.GeoReverseInfo;
import com.xy.res.dto.baidu.Location;
import com.xy.res.entity.Map;
import com.xy.res.entity.MapFix;
import com.xy.res.mapper.MapMapper;
import com.xy.res.service.MapFixService;
import com.xy.res.service.MapService;
import com.xy.res.utils.GeoUtil;
import com.xy.res.vo.MapTree;
import com.xy.res.vo.Region;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class MapServiceImpl extends ServiceImpl<MapMapper, Map>
        implements MapService {

    private final MapFixService mapFixService;
    private final ObjectMapper objectMapper;

    @SneakyThrows
    @Override
    public void importFromBaidu(String filePath) {

        List<MapFix> list = mapFixService.list();

        HashMap<String, MapFix> fixDict = new HashMap<>();

        list.forEach(l -> fixDict.put(l.getCode(), l));


        ExcelReader reader = ExcelUtil.getReader(filePath);
        List<java.util.Map<String, Object>> readAll = reader.readAll();
        System.out.println("读取了" + readAll.size() + "行数据");

        Set<String> codes = new HashSet<>();
        ExecutorService executorService = Executors.newFixedThreadPool(8);

        readAll.forEach(line -> {
            Map province = new Map();
            province.setName(line.get("省份名称").toString());
            province.setFullCode(line.get("省份代码").toString());
            province.setCode(province.getFullCode().substring(0, 2));
            province.setLevel(1);
            province.setPCode("0");
            province.setFullPath(province.getName());
            province.setCreateTime(LocalDateTime.now());

            Map city = new Map();
            city.setName(line.get("城市名称").toString());
            city.setFullCode(line.get("城市代码").toString());

            MapFix fixCity = fixDict.get(city.getFullCode());
            if (fixCity != null) {
                city.setCode(fixCity.getFixCode());
            } else {
                city.setCode(city.getFullCode().substring(0, 4));
            }
            city.setLevel(2);
            city.setPCode(province.getCode());
            city.setFullPath(province.getName() + "/" + city.getName());
            city.setCreateTime(LocalDateTime.now());

            Map district = new Map();
            district.setName(line.get("区县名称").toString());
            district.setFullCode(line.get("区县代码").toString());
            district.setCode(district.getFullCode().substring(0, 6));
            district.setLevel(3);
            district.setPCode(city.getCode());
            district.setFullPath(province.getName() + "/" + city.getName() + "/" + district.getName());
            district.setCreateTime(LocalDateTime.now());

            Map town = new Map();
            town.setName(line.get("乡镇名称").toString());
            town.setFullCode(line.get("乡镇代码").toString());
            town.setCode(town.getFullCode());
            town.setLevel(4);
            town.setPCode(district.getCode());
            town.setFullPath(province.getName() + "/" + city.getName() + "/" + district.getName() + "/" + town.getName());
            town.setCreateTime(LocalDateTime.now());

            if (codes.add(province.getCode())) {
                executorService.execute(new InsertTask(province));
            }

            if (codes.add(city.getCode())) {
                executorService.execute(new InsertTask(city));
            }

            if (codes.add(district.getCode())) {
                executorService.execute(new InsertTask(district));
            }

            if (codes.add(town.getCode())) {
                executorService.execute(new InsertTask(town));
            }
        });

        while (!executorService.isTerminated()) {
            Thread.sleep(1000);
        }

    }

    @Override
    public void importFixData(String filePath) {
        List<java.util.Map<String, Object>> readAll = ExcelUtil.getReader(filePath).readAll();
        System.out.println("读取了" + readAll.size() + "行数据");

        readAll.forEach(line -> {
            String cityCode = line.get("城市代码").toString().trim();
            String districtCode = line.get("区县代码").toString().trim();

            if (cityCode.equals(districtCode)) {
                MapFix mapFix = new MapFix();
                mapFix.setCode(cityCode);
                mapFix.setLevel(2);
                mapFix.setName(line.get("城市名称").toString());
                mapFixService.saveOrUpdate(mapFix);
            }
        });
    }

    public class InsertTask implements Runnable {

        private final Map map;

        public InsertTask(Map map) {
            this.map = map;
        }

        @Override
        public void run() {
            save(map);
        }
    }


    @Override
    public List<MapTree> buildTree() {
        List<Map> maps = list();

        return buildTree(maps, "0");
    }

    @SneakyThrows
    @Override
    public Region findByAdr(String address) {

        String geocoding = GeoUtil.geocoding(address);
        BdMapResult<GeoCodingInfo> geoCodingInfo = objectMapper.readValue(geocoding, new TypeReference<BdMapResult<GeoCodingInfo>>() {
        });

        if (geoCodingInfo.getResult().getComprehension() < 90) {
            return null;
        }

        Location location = geoCodingInfo.getResult().getLocation();
        String reverseGeocoding = GeoUtil.reverseGeocoding(location.getLat() + "," + location.getLng());
        BdMapResult<GeoReverseInfo> geoReverseInfo = objectMapper.readValue(reverseGeocoding, new TypeReference<BdMapResult<GeoReverseInfo>>() {
        });

        location = geoReverseInfo.getResult().getLocation();

        GeoReverseInfo.AddressComponent addressComponent = geoReverseInfo.getResult().getAddressComponent();

        Region region = new Region();
        region.setLng(location.getLng());
        region.setLat(location.getLat());
        region.setProvince(addressComponent.getProvince());
        region.setCity(addressComponent.getCity());
        region.setCounty(addressComponent.getDistrict());
        region.setStreet(addressComponent.getTown());
        region.setTownCode(addressComponent.getTown_code());
        return region;

    }

    private List<MapTree> buildTree(List<Map> maps, String pCode) {
        List<MapTree> trees = null;
        for (Map map : maps) {
            if (map.getPCode().equals(pCode)) {
                MapTree mapTree = new MapTree();
                BeanUtils.copyProperties(map, mapTree);
                mapTree.setCode(map.getCode());
                mapTree.setChildren(buildTree(maps, map.getCode()));
                if (null == trees) {
                    trees = new ArrayList<>();
                }
                trees.add(mapTree);
            }
        }
        return trees;
    }

}




