package com.xy.res.service;

import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2022年09月19日 14:16
 */
public interface AliOssService {

    /**
     * 上传文件到ali-oss
     *
     * @param multipartFile
     * @param type
     * @return java.lang.String
     * <AUTHOR>
     * @since 17:11 2022.09.20
     **/
    String uploadAliOss(MultipartFile multipartFile, Integer type);

    /**
     * 上传文件到本地
     *
     * @param multipartFile
     * @param type
     * @return java.lang.String
     * <AUTHOR>
     * @since 17:12 2022.09.20
     **/
    String uploadLocal(MultipartFile multipartFile, Integer type);

    /**
     * 阿里云上传文件回调
     *
     * @param content
     * <AUTHOR>
     * @since 14:05 2022.09.21
     **/
    void uploadCallBack(String content);
}
