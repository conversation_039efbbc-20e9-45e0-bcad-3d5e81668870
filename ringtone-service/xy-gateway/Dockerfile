ARG PROFILE
FROM openjdk:8-jdk-alpine
MAINTAINER Lee
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
VOLUME /tmp
ARG JAR_FILE
ADD target/xy-gateway-1.0.0.jar /app.jar

ARG PROFILE=test
ARG MARK=SY
ENV env=$PROFILE
ENV m=$MARK

RUN echo "java -Dmark.name=${m} -server -XX:MetaspaceSize=32m -XX:MaxMetaspaceSize=256m -Xms256m -Xmx256m -Xmn128m -Xss256k -XX:+UseParNewGC -XX:+UseConcMarkSweepGC -jar /app.jar --spring.profiles.active=${env}" > /run.sh && chmod 777 /run.sh
ENTRYPOINT ["/bin/sh","/run.sh"]
