spring:
  cloud:
    nacos:
      discovery:
        server-addr: 192.168.0.32:8848

    gateway:
      default-filters:
        - DedupeResponseHeader=Vary Access-Control-Allow-Origin Access-Control-Allow-Credentials, RETAIN_UNIQUE
      globalcors:
        corsConfigurations:
          '[/**]':
            allowCredentials: true
            allowedOriginPatterns: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
      routes:
        - id: uaa
          uri: lb://sysaas-uaa
          predicates:
            - Path=/uaa/**

        - id: pay
          uri: lb://sysaas-pay
          predicates:
            - Path=/pay/**

        - id: trade
          uri: lb://sysaas-trade
          predicates:
            - Path=/trade/**

        - id: admin
          uri: lb://sysaas-admin
          predicates:
            - Path=/admin/**

        - id: adminIndustry
          uri: lb://sysaas-admin-industry
          predicates:
            - Path=/admin-industry/**

        - id: adminTenant
          uri: lb://sysaas-admin-tenant
          predicates:
            - Path=/admin-tenant/**

        - id: resource
          uri: lb://sysaas-resource
          predicates:
            - Path=/res/**

        - id: tasker
          uri: lb://sysaas-tasker
          predicates:
            - Path=/tasker/**

        - id: finance
          uri: lb://sysaas-finance
          predicates:
            - Path=/finance/**

        - id: sync
          uri: lb://sysaas-sync
          predicates:
            - Path=/sync/**

        - id: syncApi
          uri: lb://sysaas-sync-api
          predicates:
            - Path=/sync-api/**

        - id: index
          uri: lb://sysaas-index
          predicates:
            - Path=/index/**

        - id: maker
          uri: lb://sysaas-maker
          predicates:
            - Path=/maker/**

        - id: vas
          uri: lb://sysaas-vas
          predicates:
            - Path=/vas/**

logging:
  config: classpath:logback-spring-sy.xml