spring:
  cloud:
    nacos:
      discovery:
        server-addr: 192.168.0.32:8848
        enabled: false

    gateway:
      default-filters:
        - DedupeResponseHeader=Vary Access-Control-Allow-Origin Access-Control-Allow-Credentials, RETAIN_UNIQUE
      globalcors:
        corsConfigurations:
          '[/**]':
            allowCredentials: true
            allowedOriginPatterns: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
      routes:
        - id: uaa
          uri: http://127.0.0.1:8912/
          predicates:
            - Path=/uaa/**

        - id: adminApiRewrite
          uri: http://127.0.0.1:8917/
          predicates:
            - Path=/admin-api/**
          filters:
            - RewritePath=/admin-api/?(?<segment>.*),/admin/admin-api/$\{segment}

        - id: adminApi
          uri: http://127.0.0.1:8917/
          predicates:
            - Path=/admin/admin-api/**

        - id: admin
          uri: http://127.0.0.1:8915/
          predicates:
            - Path=/admin/**

        - id: resource
          uri: http://127.0.0.1:8915/
          predicates:
            - Path=/res/**

logging:
  config: classpath:logback-spring-test.xml