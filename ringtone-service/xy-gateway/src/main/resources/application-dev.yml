spring:
  cloud:
    nacos:
      discovery:
        server-addr: 192.168.0.32:8848
        enabled: false

    gateway:
      default-filters:
        - DedupeResponseHeader=Vary Access-Control-Allow-Origin Access-Control-Allow-Credentials, RETAIN_UNIQUE
      globalcors:
        corsConfigurations:
          '[/**]':
            allowCredentials: true
            allowedOriginPatterns: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
      routes:
        - id: uaa
          uri: http://127.0.0.1:8912/
          predicates:
            - Path=/uaa/**

        - id: pay
          uri: http://127.0.0.1:8913/
          predicates:
            - Path=/pay/**

        - id: trade
          uri: http://127.0.0.1:8916/
          predicates:
            - Path=/trade/**

        - id: adminApiRewrite
          uri: http://127.0.0.1:8917/
          predicates:
            - Path=/admin-api/**
          filters:
            - RewritePath=/admin-api/?(?<segment>.*),/admin/admin-api/$\{segment}

        - id: adminApi
          uri: http://127.0.0.1:8917/
          predicates:
            - Path=/admin/admin-api/**

        - id: admin
          uri: http://127.0.0.1:8915/
          predicates:
            - Path=/admin/**

        - id: adminIndustry
          uri: http://127.0.0.1:8952/
          predicates:
            - Path=/admin-industry/**

        - id: adminTenant
          uri: http://127.0.0.1:8953/
          predicates:
            - Path=/admin-tenant/**

        - id: resource
          uri: http://127.0.0.1:8911/
          predicates:
            - Path=/res/**

        - id: tasker
          uri: http://127.0.0.1:8920/
          predicates:
            - Path=/tasker/**

        - id: finance
          uri: lb://sysaas-finance
          predicates:
            - Path=/finance/**

        - id: sync
          uri: http://127.0.0.1:8941/
          predicates:
            - Path=/sync/**

        - id: syncApi
          uri: http://127.0.0.1:8942/
          predicates:
            - Path=/sync-api/**

        - id: index
          uri: http://127.0.0.1:8930/
          predicates:
            - Path=/index/**

        - id: maker
          uri: http://127.0.0.1:8992/
          predicates:
            - Path=/maker/**

        - id: vas
          uri: http://127.0.0.1:8916/
          predicates:
            - Path=/vas/**

logging:
  config: classpath:logback-spring-dev.xml