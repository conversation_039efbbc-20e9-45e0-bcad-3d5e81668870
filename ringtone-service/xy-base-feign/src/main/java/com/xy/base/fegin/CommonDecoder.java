package com.xy.base.fegin;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.xy.base.core.constant.ResultCodeConsts;
import com.xy.base.core.response.Result;
import feign.FeignException;
import feign.Response;
import feign.Util;
import feign.codec.DecodeException;
import feign.codec.Decoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @since 2022/10/28
 */
@Component
@Slf4j
public class CommonDecoder implements Decoder {

    @Resource
    private ObjectMapper mapper;

    public CommonDecoder() {
        log.info("feign启用自定义解析器");
    }

    @Override
    public Object decode(Response response, Type type) throws IOException, FeignException {

        if (response.body() == null) {
            throw new DecodeException(response.status(), "没有返回有效的数据", response.request());
        }

        JavaType feignType = TypeFactory.defaultInstance().constructType(type);

        if (!(type.getTypeName().contains(Result.class.getName()))) {
            feignType = TypeFactory.defaultInstance().constructParametricType(Result.class, feignType);
        }

        String body = Util.toString(response.body().asReader(Util.UTF_8));
        Result<?> result = mapper.readValue(body, feignType);

        if (!result.getCode().equals(ResultCodeConsts.SUCCESS)) {
            throw new DecodeException(response.status(), body, response.request());
        }
        return result.getData();
    }
}
