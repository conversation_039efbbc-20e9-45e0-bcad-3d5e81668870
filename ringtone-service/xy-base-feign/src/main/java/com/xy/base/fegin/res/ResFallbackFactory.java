package com.xy.base.fegin.res;

import com.xy.base.core.dto.export.ExportDTO;
import com.xy.base.core.dto.export.ExportRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 测试feign熔断，实际并未使用
 *
 * <AUTHOR>
 * @since 2022/10/26
 */
@Slf4j
@Component
public class ResFallbackFactory implements FallbackFactory<ResClient> {


    @Override
    public ResClient create(Throwable cause) {

        cause.printStackTrace();

        return new ResClient() {
            @Override
            public ExportRecordDTO start(ExportDTO dto, String token) {
                return null;
            }
        };
    }
}
