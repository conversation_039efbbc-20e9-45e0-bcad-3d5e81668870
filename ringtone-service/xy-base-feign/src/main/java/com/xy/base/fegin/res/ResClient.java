package com.xy.base.fegin.res;

import com.xy.base.core.constant.CommonConsts;
import com.xy.base.core.dto.export.ExportDTO;
import com.xy.base.core.dto.export.ExportRecordDTO;
import com.xy.base.fegin.CommonDecoder;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR>
 * @since 2022/8/11.
 */
@FeignClient(name = "sysaas-resource", path = "/res", url = "${feign.resUrl:}", configuration = CommonDecoder.class)
public interface ResClient {


    /**
     * 准备进行导出作业
     *
     * @param dto   dto
     * @param token 请勿使用内置token
     * @return ExportRecordDTO
     */
    @PostMapping("/export/start")
    ExportRecordDTO start(@RequestBody ExportDTO dto,
                                  @RequestHeader(name = CommonConsts.HTTP_AUTH_HEADER_NAME) String token);


}
