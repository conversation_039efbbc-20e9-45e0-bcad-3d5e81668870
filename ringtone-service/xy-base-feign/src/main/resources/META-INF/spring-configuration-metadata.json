{"properties": [{"name": "feign.adminUrl", "type": "java.lang.String", "description": "admin模块feign访问路径，测试环境可填写本地地址，线上环境请勿配置或者配置为空."}, {"name": "feign.indexUrl", "type": "java.lang.String", "description": "index模块feign访问路径，测试环境可填写本地地址，线上环境请勿配置或者配置为空."}, {"name": "feign.payUrl", "type": "java.lang.String", "description": "pay模块feign访问路径，测试环境可填写本地地址，线上环境请勿配置或者配置为空."}, {"name": "feign.resUrl", "type": "java.lang.String", "description": "res模块feign访问路径，测试环境可填写本地地址，线上环境请勿配置或者配置为空."}, {"name": "feign.syncUrl", "type": "java.lang.String", "description": "sync模块feign访问路径，测试环境可填写本地地址，线上环境请勿配置或者配置为空."}, {"name": "feign.syncApiUrl", "type": "java.lang.String", "description": "syncApi模块feign访问路径，测试环境可填写本地地址，线上环境请勿配置或者配置为空."}, {"name": "feign.tenantUrl", "type": "java.lang.String", "description": "tenantApi模块feign访问路径，测试环境可填写本地地址，线上环境请勿配置或者配置为空."}]}