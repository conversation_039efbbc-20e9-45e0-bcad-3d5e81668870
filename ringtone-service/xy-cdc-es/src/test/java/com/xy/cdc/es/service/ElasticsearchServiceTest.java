package com.xy.cdc.es.service;

import com.xy.base.core.response.PageData;
import com.xy.cdc.es.dto.ProductSearchQuery;
import com.xy.cdc.es.mapping.ProductTestMapping;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 2023/6/27 11:32
 */
@SpringBootTest
class ElasticsearchServiceTest {

    @Autowired
    private ElasticsearchService elasticsearchService;

    @Test
    public void searchProduct() {
        ProductSearchQuery query = new ProductSearchQuery();
        query.setKeyword("消痛片");
        PageData<ProductTestMapping> data = elasticsearchService.searchProduct(query);
        System.out.println(data);
    }
}