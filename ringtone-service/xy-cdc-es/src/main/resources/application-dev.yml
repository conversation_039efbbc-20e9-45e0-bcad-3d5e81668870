spring:
  cloud:
    nacos:
      discovery:
        enabled: false
        server-addr: ************:8848

  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************
    username: sysaas
    password: Ku#mf0dZ
    hikari:
      connection-test-query: select 1 from dual
      connection-timeout: 30000
      idle-timeout: 60000
      max-lifetime: 1800000
      maximum-pool-size: 20
      minimum-idle: 1

  redis:
    database: 6
    host: ************
    password: YAGi2acwr4oIBTbB8geq
    port: 6378

  elasticsearch:
    uris: http://************:9200
    connection-timeout: 5000
    password:
    username:
    socket-timeout: 500000

logging:
  config: classpath:logback-spring-dev.xml

cdc:
  hostname: ************
  pwd: Ku#mf0dZ
  userName: sysaas
  checkpoint: d:\stss.txt
  # 需要构建索引的表
  indexTable: sysaas.product_sale
  # 需要监控数据变化的表
  watchTable: null