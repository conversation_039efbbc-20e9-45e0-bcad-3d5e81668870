package com.xy.cdc.es.cdc;

import com.ververica.cdc.debezium.StringDebeziumDeserializationSchema;
import com.xy.base.core.enhancer.JacksonMaker;
import io.debezium.data.Envelope;
import org.apache.flink.util.Collector;
import org.apache.kafka.connect.data.Struct;
import org.apache.kafka.connect.source.SourceRecord;

import java.util.Optional;


/**
 * <AUTHOR>
 * @since 2023年03月06日 15:33
 */
public class CustomDeserialization extends StringDebeziumDeserializationSchema {

    @Override
    public void deserialize(SourceRecord sourceRecord, Collector<String> collector) {


        FlinkTableData data = new FlinkTableData();

        Struct value = (Struct) sourceRecord.value();
        Struct source = value.getStruct("source");

        data.setTableName(source.getString("table"));
        data.setDatabase(source.getString("db"));

        Envelope.Operation operation = Envelope.operationFor(sourceRecord);
        data.setOperation(operation);

        value.getStruct("before");

        Optional.ofNullable(value.getStruct("before"))
                .ifPresent(v -> v.schema().fields().forEach(f -> data.getBefore().putPOJO(f.name(), v.get(f))));

        Optional.ofNullable(value.getStruct("after"))
                .ifPresent(v -> v.schema().fields().forEach(f -> data.getAfter().putPOJO(f.name(), v.get(f))));

        collector.collect(JacksonMaker.writeValueAsString(data));
    }
}
