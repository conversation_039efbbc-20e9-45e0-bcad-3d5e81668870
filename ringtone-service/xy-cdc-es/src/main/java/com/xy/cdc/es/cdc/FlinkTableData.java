package com.xy.cdc.es.cdc;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.xy.base.core.enhancer.JacksonMaker;
import io.debezium.data.Envelope;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023年03月06日 15:41
 */
@Data
public class FlinkTableData {

    private String database;
    private String tableName;
    private Envelope.Operation operation;

    private ObjectNode before;
    private ObjectNode after;

    public FlinkTableData() {
        before = JacksonMaker.uniMapper().createObjectNode();
        after = JacksonMaker.uniMapper().createObjectNode();

    }
}
