package com.xy.cdc.es.mapping;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/6/26 10:22
 */
@Data
@Document(indexName = "product_test")
public class ProductTestMapping {

    @Id
    private Integer id;

    /**
     * 通用名
     */
    @Field(type = FieldType.Keyword, name = "commonName")
    private String commonName;

    /**
     * 分类
     */
    @Field(type = FieldType.Keyword, name = "category")
    private String category;

    /**
     * 生产厂家
     */
    @Field(type = FieldType.Keyword, name = "manufacturer")
    private String manufacturer;

    /**
     * 规格
     */
    @Field(type = FieldType.Keyword, name = "specifications")
    private String specifications;

    /**
     * 商家名称
     */
    @Field(type = FieldType.Keyword, name = "merchantName")
    private String merchantName;

    /**
     * 商品名称
     */
    @Field(type = FieldType.Keyword, name = "name")
    private String name;

    /**
     * 平台商品编码
     */
    @Field(type = FieldType.Keyword, name = "sn")
    private String sn;

    /**
     * 商业内码
     */
    @Field(type = FieldType.Keyword, name = "tenantSn")
    private String tenantSn;

    /**
     * 销售活动类型（详见SaleTypeEnum）
     */
    @Field(type = FieldType.Integer, name = "saleType")
    private Integer saleType;

    /**
     * 0下架；1上架
     */
    @Field(type = FieldType.Integer, name = "status")
    private Integer status;

    /**
     * 标签
     */
    @Field(type = FieldType.Text, name = "label")
    private String label;

    /**
     * 商品在商家中的排序
     */
    @Field(type = FieldType.Integer, name = "sort")
    private Integer sort;

    @Field(type = FieldType.Date, name = "expiry", format = DateFormat.date)
    private LocalDate expiry;

    /**
     * 销售开始时间
     */
    @Field(type = FieldType.Date, name = "startTime", format = {}, pattern = "uuuu-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 销售结束时间
     */
    @Field(type = FieldType.Date, name = "endTime", format = {}, pattern = "uuuu-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 普药还是控销 0|false控销，1|true普药
     */
    @Field(type = FieldType.Boolean, name = "gmed")
    private Boolean gmed;

    /**
     * 默认排序权重得分
     */
    @Field(type = FieldType.Keyword, name = "weightCount")
    private BigDecimal weightCount;

    /**
     * 拼音
     */
    @Field(type = FieldType.Keyword, name = "pym")
    private String pym;

    /**
     * 销售范围数组
     */
    @Field(type = FieldType.Integer, name = "saleScope")
    private List<Integer> saleScope;


    /**
     * 销售范围数组
     */
    @Field(type = FieldType.Keyword, name = "map")
    private List<String> map;
}
