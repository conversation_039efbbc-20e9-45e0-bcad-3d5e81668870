package com.xy.cdc.es.cdc;

import cn.hutool.core.io.FileUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.xy.base.core.enhancer.JacksonMaker;
import com.xy.base.core.util.SpringUtils;
import com.xy.cdc.es.constant.DataConstants;
import com.xy.cdc.es.service.ElasticsearchService;
import io.debezium.data.Envelope;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;

import java.nio.charset.StandardCharsets;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023年03月06日 15:26
 */
@Slf4j
public class FlinkCustomSink extends RichSinkFunction<String> {

    private final String pointUrl;
    private final Set<String> watchTables;
    private final Set<String> indexTables;


    public FlinkCustomSink(String checkpointUrl, Set<String> watchTables, Set<String> indexTables) {
        this.pointUrl = checkpointUrl;
        this.watchTables = watchTables;
        this.indexTables = indexTables;

    }

    @Override
    public void invoke(String value, Context context) throws JsonProcessingException {

        log.info("检测到数据变动:{}", value);
        FlinkTableData flinkTableData = JacksonMaker.uniMapper().readValue(value, FlinkTableData.class);

        if (!Envelope.Operation.READ.equals(flinkTableData.getOperation())) {
            log.info("最后时间:{}", context.currentProcessingTime());
            FileUtil.writeBytes(String.valueOf(context.currentProcessingTime()).getBytes(StandardCharsets.UTF_8), pointUrl);
        }

        if (indexTables.contains(DataConstants.DATA_BASE + "." + flinkTableData.getTableName())) {
            invokeIndex(flinkTableData);
        }
//
//        if (watchTables.contains(DataConstants.DATA_BASE + "." + flinkTableData.getTableName())) {
//            invokeWatch(flinkTableData);
//        }


    }

    /**
     * 执行索引构建任务
     */
    private void invokeIndex(FlinkTableData flinkTableData) {

        SpringUtils.getBean(ElasticsearchService.class).modifyIndex(flinkTableData);

    }

    /**
     * 执行数据监控任务
     * 目前需要监控的表不多，暂时用if来判断。如果后期表变多，采用工厂模式执行处理
     */
    private void invokeWatch(FlinkTableData flinkTableData) {

    }


}
