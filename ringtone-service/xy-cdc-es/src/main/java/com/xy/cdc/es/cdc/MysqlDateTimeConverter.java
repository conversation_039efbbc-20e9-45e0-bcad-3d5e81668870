package com.xy.cdc.es.cdc;

import io.debezium.spi.converter.CustomConverter;
import io.debezium.spi.converter.RelationalColumn;
import org.apache.kafka.connect.data.SchemaBuilder;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.Properties;

import static com.xy.base.core.constant.CommonConsts.LOCAL_DATETIME_FORMATTER;
import static com.xy.base.core.constant.CommonConsts.LOCAL_DATE_FORMATTER;

/**
 * 处理Debezium时间转换的问题
 * Debezium默认将MySQL中datetime类型转成UTC的时间戳({@link io.debezium.time.Timestamp})，时区是写死的无法更改，
 * 导致数据库中设置的UTC+8，到kafka中变成了多八个小时的long型时间戳
 * Debezium默认将MySQL中的timestamp类型转成UTC的字符串。
 * | mysql                               | mysql-binlog-connector                   | debezium                          |
 * | ----------------------------------- | ---------------------------------------- | --------------------------------- |
 * | date<br>(2021-01-28)                | LocalDate<br/>(2021-01-28)               | Integer<br/>(18655)               |
 * | time<br/>(17:29:04)                 | Duration<br/>(PT17H29M4S)                | Long<br/>(62944000000)            |
 * | timestamp<br/>(2021-01-28 17:29:04) | ZonedDateTime<br/>(2021-01-28T09:29:04Z) | String<br/>(2021-01-28T09:29:04Z) |
 * | Datetime<br/>(2021-01-28 17:29:04)  | LocalDateTime<br/>(2021-01-28T17:29:04)  | Long<br/>(1611854944000)          |
 *
 * <AUTHOR>
 * @since 2023/6/26 16:55
 */
public class MysqlDateTimeConverter implements CustomConverter<SchemaBuilder, RelationalColumn> {

    @Override
    public void configure(Properties props) {
        // 可从配置中读取相关属性，目前无相关内容
    }

    @Override
    public void converterFor(RelationalColumn column, ConverterRegistration<SchemaBuilder> registration) {

        String sqlType = column.typeName().toUpperCase();
        SchemaBuilder schemaBuilder = null;
        if ("DATE".equals(sqlType) || "DATETIME".equals(sqlType)) {
            schemaBuilder = SchemaBuilder.string();
        }
        Optional.ofNullable(schemaBuilder).ifPresent(s -> registration.register(s, this::convertDateTime));
    }

    private String convertDateTime(Object input) {

        if (input instanceof LocalDate) {
            return LOCAL_DATE_FORMATTER.format((LocalDate) input);
        } else if (input instanceof LocalDateTime) {
            return LOCAL_DATETIME_FORMATTER.format((LocalDateTime) input);
        }
        return input.toString();
    }

}
