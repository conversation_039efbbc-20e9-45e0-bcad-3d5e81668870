package com.xy.cdc.es.service.impl;

import com.xy.base.core.constant.CommonConsts;
import com.xy.base.core.enhancer.JacksonMaker;
import com.xy.base.core.response.PageData;
import com.xy.cdc.es.cdc.FlinkTableData;
import com.xy.cdc.es.dto.ProductSearchQuery;
import com.xy.cdc.es.entity.ProductSale;
import com.xy.cdc.es.mapping.ProductTestMapping;
import com.xy.cdc.es.repository.ProductTestRepository;
import com.xy.cdc.es.service.ElasticsearchService;
import com.xy.cdc.es.util.EsUtils;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/6/26 10:12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ElasticsearchServiceImpl implements ElasticsearchService {

    private final ProductTestRepository productTestRepository;
    private final ElasticsearchRestTemplate esTemplate;

    private static final String TB_PRODUCT_SALE = "product_sale";

    @Override
    public PageData<ProductTestMapping> searchProduct(ProductSearchQuery query) {
        BoolQueryBuilder filterBuilder = EsUtils.builderProductQueryParam(query);

        // 排序
        FieldSortBuilder sort = SortBuilders.fieldSort("id").order(SortOrder.ASC);

        Query queryBuilder = new NativeSearchQueryBuilder()
                .withFilter(filterBuilder)
                .withPageable(PageRequest.of(query.getPage() - 1, query.getSize()))
                .withSorts(sort)
                .build();

        try {
            log.info("dsl:{}", EsUtils.dsl(esTemplate.getRequestFactory(),queryBuilder,ProductTestMapping.class, esTemplate.getIndexCoordinatesFor(ProductTestMapping.class)));

            SearchHits<ProductTestMapping> searchHits = esTemplate.search(queryBuilder, ProductTestMapping.class, IndexCoordinates.of("product_test"));
            if (searchHits.getTotalHits() > 0) {
                List<ProductTestMapping> productMappings = new ArrayList<>();
                searchHits.forEach(hit -> productMappings.add(hit.getContent()));
                return new PageData<>(query.getPage(), searchHits.getTotalHits(), query.getSize(), productMappings);
            }
        } catch (Exception e) {
            log.error("商品查询错误信息>>>>>:{}", e.getMessage());
            e.printStackTrace();
        }
        return new PageData<>(query.getPage(), 0, query.getSize(), null);
    }

    @Override
    public void modifyIndex(FlinkTableData flinkTableData) {

        switch (flinkTableData.getOperation()) {
            case READ:
            case CREATE:
            case UPDATE:
                updateIndex(flinkTableData);
                break;
            case DELETE:
                deleteIndex(flinkTableData);
                break;
            default:
                break;
        }
    }


    @SneakyThrows
    private void updateIndex(FlinkTableData flinkTableData) {

        switch (flinkTableData.getTableName()) {
            case TB_PRODUCT_SALE:
                ProductSale productSale = JacksonMaker.uniMapper().treeToValue(flinkTableData.getAfter(), ProductSale.class);
                updateProductSaleIndex(productSale);
                break;
            default:
                break;
        }

    }

    private void updateProductSaleIndex(ProductSale productSale) {
        if (productSale.getStatus().equals(CommonConsts.ZERO)) {
            productTestRepository.deleteById(productSale.getId());
        } else {
            ProductTestMapping mapping = new ProductTestMapping();
            BeanUtils.copyProperties(productSale, mapping);

            mapping.setSaleScope(new ArrayList<>());
            mapping.setMap(new ArrayList<>());
            for (String s : productSale.getSaleScope().split(",")) {
                mapping.getSaleScope().add(Integer.parseInt(s));
            }
            mapping.getMap().add("51");
            mapping.getMap().add("510");
            mapping.getMap().add("2567");
            productTestRepository.save(mapping);
        }

    }

    private void deleteIndex(FlinkTableData flinkTableData) {


    }

}
