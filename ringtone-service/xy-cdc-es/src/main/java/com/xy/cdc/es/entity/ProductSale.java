package com.xy.cdc.es.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 商品销售信息（货架）
 *
 * <AUTHOR>
 * @since 2023/6/26 10:07
 */
@TableName(value = "product_sale")
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ProductSale implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 仓库商品库id
     */
    private Integer productId;
    /**
     * 商品标识内码
     */
    private String codeId;
    /**
     *
     */
    private String tenantId;
    /**
     * 平台内码
     */
    private String sn;
    /**
     * 商业内码
     */
    private String tenantSn;
    /**
     * 销售活动类型（详见SaleTypeEnum）
     */
    private Integer saleType;
    /**
     * 如果是套餐活动，同一个套餐具有相同的packId(通常是一个uuid)
     */
    private String packId;
    /**
     * 是否能使用优惠券
     */
    private Boolean useCoupon;
    /**
     * 商品名称
     */
    private String name;
    /**
     * 生产厂家
     */
    private String manufacturer;
    /**
     * 规格
     */
    private String specification;
    /**
     * 0下架；1上架
     */
    private Integer status;
    /**
     * 普药还是控销 0|false控销，1|true普药
     */
    private Boolean gmed;
    /**
     * 标签
     */
    private String label;
    /**
     * 药品经营类型
     */
    private String genre;
    /**
     * 效期(到期时间)
     */
    private LocalDate expiry;
    /**
     * 效期持续时间
     */
    private String period;
    /**
     * 商品在商家中的排序
     */
    private Integer sort;
    /**
     * 销售区域（中文描述）
     */
    private String saleDistrictCode;
    /**
     * 销售区域（地图选择器的值）
     */
    private String saleDistrict;
    /**
     * 销售范围（客户类型）
     */
    private String saleScope;
    /**
     * 销售开始时间
     */
    private LocalDateTime startTime;
    /**
     * 销售结束时间
     */
    private LocalDateTime endTime;
    /**
     * 起发数量
     */
    private Integer startAmount;
    /**
     * 中包装数量（即步进），可以和基础资料不一样
     */
    private Integer mediumAmount;
    /**
     * 销售限制（单笔最大购买数量），0表示不限购，由库存决定
     */
    private Integer limitAmount;
    /**
     * 限制类型，0不限制，1限单，2限周，3限月
     */
    private Integer limitType;
    /**
     * 是否同步价格
     */
    private Integer syncPrice;
    /**
     * 是否同步库存
     */
    private Integer syncStore;
    /**
     * 商品库存
     */
    private Integer store;
    /**
     * 商品图片（如果不传，默认用基础商品资料填充）
     */
    private String imgs;
    /**
     * 件装
     */
    private Integer pack;
    /**
     * 累计销售数量
     */
    private Integer sales;
    /**
     * 拼音码
     */
    private String pym;
    /**
     * 是否可赠 (0不可赠 ,1可赠)
     */
    private Boolean gift;
    /**
     * 可赠库存
     */
    private Integer giftStore;
    /**
     * 是否显示商品图片外边框
     */
    private Boolean tag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
