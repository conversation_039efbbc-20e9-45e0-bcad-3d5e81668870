package com.xy.cdc.es.service;

import com.xy.base.core.response.PageData;
import com.xy.cdc.es.cdc.FlinkTableData;
import com.xy.cdc.es.dto.ProductSearchQuery;
import com.xy.cdc.es.mapping.ProductTestMapping;

/**
 * <AUTHOR>
 * @since 2023/6/26 10:11
 */
public interface ElasticsearchService {

    /**
     * 修改ES索引属性
     *
     * @param flinkTableData 数据库数据
     */
    void modifyIndex(FlinkTableData flinkTableData);


    /**
     * 全部商品搜索，包含普药和控销
     *
     * @param query query
     **/
    PageData<ProductTestMapping> searchProduct(ProductSearchQuery query);
}
