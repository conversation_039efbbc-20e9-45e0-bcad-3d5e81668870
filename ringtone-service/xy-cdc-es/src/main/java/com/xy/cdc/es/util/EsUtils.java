package com.xy.cdc.es.util;

import com.xy.cdc.es.dto.ProductSearchQuery;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Objects;

import static com.xy.base.core.constant.CommonConsts.LOCAL_DATETIME_FORMATTER;

/**
 * <AUTHOR>
 * @since 2023/6/27 10:35
 */
public class EsUtils {

    /**
     * 构建商品查询query
     *
     * @param searchQuery searchQuery
     */
    public static BoolQueryBuilder builderProductQueryParam(ProductSearchQuery searchQuery) {
        BoolQueryBuilder builder = QueryBuilders.boolQuery();

        if (StringUtils.hasText(searchQuery.getKeyword())) {
            String keyWord = "*" + searchQuery.getKeyword() + "*";

            builder.must(QueryBuilders.boolQuery().should(QueryBuilders.wildcardQuery("name", keyWord))
                    .should(QueryBuilders.wildcardQuery("manufacturer", keyWord))
                    .should(QueryBuilders.wildcardQuery("pym", keyWord.toUpperCase())));
        }

        // 时间区间
        String now = LOCAL_DATETIME_FORMATTER.format(LocalDateTime.now());
        builder.must(QueryBuilders.termQuery("saleScope", 5))
                .must(QueryBuilders.termQuery("map", "51"))
                .must(QueryBuilders.rangeQuery("startTime").lte(now))
                .must(QueryBuilders.rangeQuery("endTime").gte(now));

        return builder;
    }

    /**
     * 返回dsl语句
     */
    public static String dsl(Object requestFactory, Query queryBuilder, Class<?> productTestMappingClass, IndexCoordinates indexCoordinatesFor) {
        try {
            Method searchRequest = ReflectionUtils.findMethod(Class.forName("org.springframework.data.elasticsearch.core.RequestFactory"), "searchRequest", Query.class, Class.class, IndexCoordinates.class);
            assert searchRequest != null;
            searchRequest.setAccessible(true);
            Object o = ReflectionUtils.invokeMethod(searchRequest, requestFactory, queryBuilder, productTestMappingClass, indexCoordinatesFor);

            Field source = ReflectionUtils.findField(Class.forName("org.elasticsearch.action.search.SearchRequest"), "source");
            assert source != null;
            source.setAccessible(true);
            return Objects.requireNonNull(ReflectionUtils.getField(source, o)).toString();
        } catch (Exception e) {
            return null;
        }

    }

}
