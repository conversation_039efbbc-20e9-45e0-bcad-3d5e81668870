package com.xy.cdc.es.cdc;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.FileUtil;
import com.ververica.cdc.connectors.mysql.source.MySqlSource;
import com.ververica.cdc.connectors.mysql.table.StartupOptions;
import com.xy.cdc.es.constant.DataConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Async;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Properties;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/10/9
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class FlinkCDCRunner implements ApplicationRunner {

    @Value("${cdc.watchTable}")
    private String watchTable;

    @Value("${cdc.indexTable}")
    private String indexTable;

    @Value("${cdc.hostname}")
    private String cdcHost;

    @Value("${cdc.port:3306}")
    private Integer cdcPort;

    @Value("${cdc.pwd:3canal3}")
    private String cdcPwd;

    @Value("${cdc.userName:canal}")
    private String cdcUserName;

    @Value("${cdc.checkpoint}")
    private String cdcCheckpoint;

    @Override
    @Async
    public void run(ApplicationArguments args) {

        // 自定义时间转换配置
        Properties properties = new Properties();
        properties.setProperty("converters", "dateConverter");
        properties.setProperty("dateConverter.type", "com.xy.cdc.es.cdc.MysqlDateTimeConverter");

        Set<String> watchTables = Arrays.stream(watchTable.split(",")).collect(Collectors.toSet());
        Set<String> indexTables = Arrays.stream(indexTable.split(",")).collect(Collectors.toSet());

        HashSet<String> tables = new HashSet<>();
        tables.addAll(watchTables);
        tables.addAll(indexTables);

        MySqlSource<String> mySqlSource = MySqlSource.<String>builder()
                .hostname(cdcHost)
                .port(cdcPort)
                .databaseList("sysaas")
                .username(cdcUserName)
                .password(cdcPwd)
                .serverTimeZone("Asia/Shanghai")
                .tableList(tables.toArray(new String[0]))
                .startupOptions(getStartupOption())
                .debeziumProperties(properties)
                .deserializer(new CustomDeserialization())
                .build();

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.enableCheckpointing(30000);
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        env.fromSource(mySqlSource, WatermarkStrategy.noWatermarks(), DataConstants.DATA_BASE)
                .setParallelism(2)
                .addSink(new FlinkCustomSink(cdcCheckpoint, watchTables, indexTables))
                .setParallelism(1);
        try {
            env.execute("Print MySQL Snapshot + Binlog");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 返回监听策略
     * 读取最后时间,检查文件是否存在。如果有处理时间，将时间在往回拨35秒，避免因为处理失败等原因导致的最后一点数据处理丢失。
     *
     * @return StartupOptions
     */
    private StartupOptions getStartupOption() {
        StartupOptions startupOptions = StartupOptions.initial();
        if (FileUtil.exist(cdcCheckpoint)) {
            long time = Convert.toLong(FileUtil.readString(cdcCheckpoint, StandardCharsets.UTF_8), 0L);
            if (time > 0) {
                log.info("CDC发现上次更新时间{}:{},继续处理", cdcCheckpoint, time);
                startupOptions = StartupOptions.timestamp(time - 35000000);
            }
        } else {
            log.info("CDC未发现上次处理时间，全量读取，并写入当前时间");
            FileUtil.writeString(String.valueOf(System.currentTimeMillis()), FileUtil.touch(cdcCheckpoint), StandardCharsets.UTF_8);
        }
        return startupOptions;
    }
}
