package com.xy.base.core.constant;

import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @since 2020/11/17
 */
public class CommonConsts {

    /**
     * 隐形的平台商户的名称
     */
    public static final String PLATFORM_TENANT_ID = "SY";


    /**
     * 存放鉴权信息的Header名称，默认是Authorization
     */
    public static final String HTTP_AUTH_HEADER_NAME = "Authorization";

    public static final String SUPER_ORG = "/";

    /**
     * 存放平台信息
     */
    public static final String PLATFORM = "Platform";

    /**
     * 存放平台信息
     */
    public static final String VERSION = "Version";

    /**
     * js传过来的undefined
     */
    public static final String JS_UNDEFINED = "undefined";

    /**
     * 默认过期时间4小时
     */
    public static final int EX_TIME = 4 * 60 * 60;

    /**
     * 0
     */
    public static final int ZERO = 0;

    /**
     * 1
     */
    public static final int ONE = 1;

    /**
     * 2
     */
    public static final int TWO = 2;

    /**
     * 3
     */
    public static final int THREE = 3;

    /**
     * 10
     */
    public static final int TEN = 10;

    /**
     * -1
     */
    public static final int NEGATIVE = -1;

    /**
     * 200
     */
    public static final int HTTP_OK = 200;

    public static String SEPARATOR = ";";


    public static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_TIME_PATTERN_COMPACT = "yyyyMMddHHmmss";
    public static final String DATE_PATTERN = "yyyy-MM-dd";

    public static DateTimeFormatter LOCAL_DATETIME_FORMATTER = DateTimeFormatter.ofPattern(DATE_TIME_PATTERN);
    public static DateTimeFormatter LOCAL_DATETIME_COMPACT_FORMATTER = DateTimeFormatter.ofPattern(DATE_TIME_PATTERN_COMPACT);
    public static DateTimeFormatter LOCAL_DATE_FORMATTER = DateTimeFormatter.ofPattern(DATE_PATTERN);


    /* -------------------redis keys---------------------------- */

    /**
     * 药品分类缓存
     */
    public static final String KEY_CATES_GEMD = "categories:gmed";

    /**
     * 百度accessToken缓存
     */
    public static final String KEY_BAIDU_AT = "access:token:baidu";

    /**
     * 配置数据缓存
     */
    public static final String KEY_CONFIG = "org:config";

    /**
     * 热搜缓存
     */
    public static final String KEY_HOT_SEARCH = "org:hot:search";


    /**
     * 用户导入计划
     */
    public static final String KEY_PURCHASE_PLAN = "purchase:plan:";

    /**
     * 阿里ak缓存
     */
    public static final String KEY_ORDERING = "ordering";

    public static final String KEY_CART_PRE_GROUP = "settlement:group:";

//    public static final String KEY_CART_PRE_GMED= "settlement:gmed:";



    /* -------------------一些常用锁名称---------------------------- */
    /**
     * 余额锁
     */
    public static final String LOCK_BALANCE = "balance:";


    public static final String INTERNAL_TOKEN = "X3xmDAAcECjUSt5pvGNoEdDoPwISgwP";

}
