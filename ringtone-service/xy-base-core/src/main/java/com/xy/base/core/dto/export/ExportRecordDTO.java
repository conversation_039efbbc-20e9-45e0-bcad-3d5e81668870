package com.xy.base.core.dto.export;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 导出作业数据
 *
 * <AUTHOR>
 * @since 2022/8/11.
 */
@Data
public class ExportRecordDTO {

    private String id;

    /**
     * 导出的主要表名
     */
    private String tableName;

    /**
     * 导出状态（-1：导出失败；0；初始化，1：导出中，100：导出成功）
     */
    private Integer status;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 操作者姓名
     */
    private String operator;

    /**
     * 数据总量
     */
    private Integer total;

    /**
     * 当前生成条数
     */
    private Integer current;

    /**
     *
     */
    private LocalDateTime createTime;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

}
