package com.xy.base.core.enums;

import com.xy.base.core.exception.AppException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2020/11/18
 */
@Getter
@AllArgsConstructor
public enum UaaTypeEnum implements BaseEnum<String> {

    /**
     * 平台管理员
     */
    ADMIN("admin"),

    /**
     * 租户（商业）
     */
    TENANT("tenant"),

    /**
     * 工业
     */
    INDUSTRY("industry"),

    /**
     * 省总
     */
    PROVINCIAL("provincial"),

    /**
     * 业务员
     */
    YWY("ywy"),

    /**
     * 终端用户
     */
    USER("user"),

    /**
     * 学术中心自主注册用户（只具有学术中心访问权限）
     */
    EDU("edu"),

    /**
     * 对码中心用户medicine center（只具有对码中心访问权限）
     */
    MC("mc"),


    /**
     * 供应商
     */
    SUPPLIER("supplier");

    private final String value;

    public static UaaTypeEnum of(String type) {
        switch (type.toLowerCase()) {
            case "admin":
                return ADMIN;
            case "tenant":
                return TENANT;
            case "industry":
                return INDUSTRY;
            case "provincial":
                return PROVINCIAL;
            case "ywy":
                return YWY;
            case "user":
                return USER;
            case "supplier":
                return SUPPLIER;
            case "edu":
                return EDU;
            default:
                throw new AppException("错误的用户类型");
        }
    }

}
