package com.xy.base.core.util;

import cn.hutool.db.Db;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import static com.xy.base.core.util.SpringUtils.getActiveProfile;

@Slf4j
public class DbUtil {

    private static String active = null;
    private static String useDb = null;

    public static void init() {
        active = getActiveProfile();
        log.info("active in DbUtil is: {}", active);
    }

    public static void setActive(String activeName) {
        active = activeName;
    }

    public static void setDbByPro(String pro) {
        useDb = pro;
    }

    public static Db useOld() {
        String db = useDb;
        if (!"zsgl".equals(db)) {
            db = "pros";
        }
        log.info("use db in DbUtil is: {}-{}", active, db);
        return Db.use(db);
    }

    public static Db use() {
        log.info("use db in DbUtil is: {}", active);
        return Db.use(active);
    }
}
