package com.xy.base.core.enhancer;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.TimeZone;

import static com.xy.base.core.constant.CommonConsts.*;


/**
 * <AUTHOR>
 * @since 2023/2/15
 */
public class JacksonMaker {

    private static final ObjectMapper OBJECT_MAPPER;

    static {
        OBJECT_MAPPER = uniBuilder(Jackson2ObjectMapperBuilder.json()).build();
    }


    /**
     * 获取系统内表现一致的ObjectMapper
     * 适用于无法通过框架注入ObjectMapper的情况，如静态方法或者未引入starter的模块等
     */
    public static ObjectMapper uniMapper() {
        return OBJECT_MAPPER;
    }

    /**
     * 忽略异常的objectMapper.writeValueAsString
     *
     * @param obj obj
     * @return jsonStr
     */
    public static String writeValueAsString(Object obj) {
        return writeValueAsString(OBJECT_MAPPER, obj);
    }

    /**
     * 忽略异常的objectMapper.writeValueAsString
     *
     * @param mapper 可以获取到其他mapper
     * @param obj    obj
     * @return jsonStr
     */
    public static String writeValueAsString(ObjectMapper mapper, Object obj) {
        try {
            return mapper.writeValueAsString(obj);
        } catch (JsonProcessingException ignore) {
            return "";
        }
    }


    /**
     * 获取系统内表现一致的Jackson2ObjectMapperBuilder
     */
    public static Jackson2ObjectMapperBuilder uniBuilder(Jackson2ObjectMapperBuilder builder) {

        TimeZone tz = TimeZone.getTimeZone("UTC");
        DateFormat df = new SimpleDateFormat(DATE_TIME_PATTERN);
        df.setTimeZone(tz);

        return builder.failOnEmptyBeans(false)
                .failOnUnknownProperties(false)
                .featuresToDisable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                .serializationInclusion(JsonInclude.Include.NON_NULL)
                .dateFormat(df)
                .serializerByType(LocalDateTime.class, new LocalDateTimeSerializer(LOCAL_DATETIME_FORMATTER))
                .serializerByType(LocalDate.class, new LocalDateSerializer(LOCAL_DATE_FORMATTER))
                .deserializerByType(LocalDate.class, new LocalDateDeserializer(LOCAL_DATE_FORMATTER))
                .deserializerByType(LocalDateTime.class, new LocalDateTimeDeserializer(LOCAL_DATETIME_FORMATTER));
    }

}
