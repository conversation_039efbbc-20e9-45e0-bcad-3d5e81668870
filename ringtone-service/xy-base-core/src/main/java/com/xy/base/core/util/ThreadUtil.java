package com.xy.base.core.util;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * 全局（模块中）公共线程池
 *
 * <AUTHOR>
 * @since 2022/5/19.
 */
@Slf4j
public class ThreadUtil {

    private static volatile ThreadPoolExecutor executorService = null;

    /**
     * 获取项目中全局的线程执行池，最多运行4个线程，其余的排队处理
     * 除非有特殊的需求，否则尽量不要创建新的线程池，避免系统不必要的开销
     * 每个模块最多运行4个线程，而不是整个系统最多运行4个线程
     * 使用完毕后记得调用ThreadUtils.getExecutorService().shutdown()关闭线程池，否则线程池会一直存在
     * 对于一些需要等待线程池运行结束的逻辑，参考以下用法
     * for (int i = 0; i < 8; i++) {
     * ThreadUtils.getExecutorService().execute(() -> {
     * // do something
     * });
     * }
     * // 线程运行完成后关闭线程池
     * ThreadUtils.getExecutorService().shutdown();
     * // 等到线程池执行结束(可选)
     * ThreadUtils.getExecutorService().awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);
     * // do things after ExecutorService complete
     */
    private static ThreadPoolExecutor getExecutorService() {
        if (executorService == null || executorService.isShutdown() || executorService.isTerminated()) {
            synchronized (ThreadUtil.class) {
                if (executorService == null || executorService.isShutdown() || executorService.isTerminated()) {
                    executorService = new ThreadPoolExecutor(
                            4,
                            4,
                            0L,
                            TimeUnit.MILLISECONDS,
                            new LinkedBlockingQueue<>(),
                            Executors.defaultThreadFactory(),
                            new ThreadPoolExecutor.AbortPolicy());
                }
            }
        }
        return executorService;
    }

    /**
     * 通过内置的线程池执行一个线程
     * 禁止在线程中再使用new Thread开新的线程
     *
     * @param taskName 任务名称
     * @param consumer 接收tenantId的消费方法
     */
    public static void execute(String taskName, Consumer<String> consumer) {

        ThreadPoolExecutor service = getExecutorService();

        String taskId = IdUtils.nextIdWithPrefix("#") + "#" + taskName;
        log.info(taskId + " 线程加入线程池准备执行,当前队列数：" + service.getQueue().size() + " ###########################");
        long prepare = System.currentTimeMillis();
        service.execute(() -> {
            long start = System.currentTimeMillis();
            log.info(taskId + " 线程开始执行，等待时间" + (start - prepare) + " ###########################");

            try {
                consumer.accept(taskName);
            } catch (Exception e) {
                log.error(taskId + "线程池执行异常:" + e.getMessage(), e);
            } finally {
                long end = System.currentTimeMillis();
                log.info(taskId + " 线程执行完毕，耗时" + (end - start) + " ###########################");
            }
        });
    }
}
