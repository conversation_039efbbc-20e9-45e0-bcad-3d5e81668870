package com.xy.base.core.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;

/**
 * 切面日志对象，用于高并发时日志散乱问题
 *
 * <AUTHOR>
 * @since 2021/5/8.
 */
@Getter
@Setter
public class LoggerInfo {

    private String ip;
    private String url;
    private String method;
    private String requestParams;
    private String result;
    private String header;
    private long startTime;
    private long endTime;
    private String id;

    private boolean logReq = true;
    private boolean logRes = true;

    /**
     * 发生的异常
     */
    private List<RuntimeException> exceptions;
    /**
     * 执行的sql
     */
    private List<String> sqlLists;
    /**
     * 附加日志
     */
    private List<String> additions;

    public LoggerInfo() {
        exceptions = new ArrayList<>();
        sqlLists = new ArrayList<>();
        additions = new ArrayList<>();
    }

    public void addSql(String sql) {
        sqlLists.add(sql);
    }

    public void addException(RuntimeException exception) {
        exceptions.add(exception);
    }

    public void addException(String msg, Throwable e) {
        exceptions.add(new RuntimeException(msg, e));
    }

    public void addAddition(String addition) {
        additions.add(addition);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();

        sb.append("---------------- ").append(id).append("------- Receive Request ----------------").append(System.lineSeparator())
                .append("URL      :").append(url).append(System.lineSeparator())
                .append("Header   :").append(header).append(System.lineSeparator())
                .append("IP       :").append(ip).append("@").append(method).append(System.lineSeparator())
                .append("Request  :").append(requestParams).append(System.lineSeparator())
                .append("Response :").append(result).append(System.lineSeparator())
                .append("Timing   :").append(endTime - startTime).append(System.lineSeparator());

        if (additions.size() > 0) {
            sb.append(System.lineSeparator()).append("【ADDITIONS】:").append(System.lineSeparator());
            additions.forEach(addition -> sb.append(addition).append(System.lineSeparator()));
        }

        if (sqlLists.size() > 0) {
            sb.append(System.lineSeparator()).append("【SQLS】:").append(System.lineSeparator());
            sqlLists.forEach(sql -> sb.append(sql).append(System.lineSeparator()));
        }

        if (exceptions.size() > 0) {
            sb.append(System.lineSeparator()).append("【EXCEPTIONS】:").append(System.lineSeparator());
            exceptions.forEach(ex -> {
                StringWriter sw = new StringWriter();
                ex.printStackTrace(new PrintWriter(sw, true));
                String st = sw.toString();
                sb.append(st.length() > 800 ? st.substring(0, 800) : st).append(System.lineSeparator());
            });
        }

        sb.append("----------------------------------------- End Request ---------!!!").append(System.lineSeparator());

        return sb.toString();
    }
}
