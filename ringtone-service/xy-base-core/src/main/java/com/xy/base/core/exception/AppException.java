package com.xy.base.core.exception;

import com.xy.base.core.enums.ExceptionResultEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2019-11-22
 */
@Getter
public class AppException extends RuntimeException {

    private final int code;
    private final String msg;
    private Object data;
    private String errorCode;

    public AppException() {
        this(1001, "请填写正确的信息");
    }

    public AppException(String msg) {
        this(1001, msg);
    }

    public AppException(String msg, Object data) {
        this(1001, msg, data);
    }

    public AppException(ExceptionResultEnum expected) {
        super(expected.getValue());
        this.code = expected.getCode();
        this.errorCode = expected.getErrorCode();
        this.msg = expected.getValue();
    }

    public AppException(ExceptionResultEnum expected, Object data) {
        super(expected.getValue());
        this.code = expected.getCode();
        this.errorCode = expected.getErrorCode();
        this.msg = expected.getValue();
        this.data = data;
    }

    public AppException(int code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }

    public AppException(int code, String msg, Object data) {
        this(code, msg);
        this.data = data;
    }

    public AppException(int code, String errorCode, String msg) {
        super(msg);
        this.code = code;
        this.errorCode = errorCode;
        this.msg = msg;
    }

    public AppException(int code, String errorCode, String msg, Object data) {
        this(code, errorCode, msg);
        this.data = data;
    }

    /**
     * throw new AppException when true 简化代码 抛出异常
     *
     * @param flag true抛出，false不做任何操作
     * @param msg  msg
     */
    public static void tnt(boolean flag, String msg) {
        if (flag) {
            throw new AppException(msg);
        }
    }

    /**
     * throw new AppException when true 简化代码 抛出异常
     *
     * @param flag     true抛出，false不做任何操作
     * @param expected expected
     */
    public static void tnt(boolean flag, ExceptionResultEnum expected) {
        if (flag) {
            throw new AppException(expected);
        }
    }
    
    /**
     * throw new AppException when true 简化代码 抛出异常
     *
     * @param expected expected
     */
    public static void tnt(ExceptionResultEnum expected) {
            throw new AppException(expected);
    }

    /**
     * throw new AppException when true 简化代码 抛出异常
     *
     * @param flag      true抛出，false不做任何操作
     * @param code      错误代码
     * @param errorCode 字符串格式的错误码
     * @param msg       错误信息
     */
    public static void tnt(boolean flag, int code, String errorCode, String msg) {
        if (flag) {
            throw new AppException(code, errorCode, msg);
        }
    }

}
