package com.xy.base.core.util;

import org.springframework.util.StringUtils;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @since 2020/11/18
 */
public class CommonUtils {

    private static final String STR_UNKNOWN = "unknown";


    public static String printStackTraceToString(Throwable t) {
        StringWriter sw = new StringWriter();
        t.printStackTrace(new PrintWriter(sw, true));
        return sw.getBuffer().toString();
    }

    public static String getDateStr(String pattern) {

        if (!StringUtils.hasText(pattern)) {
            pattern = "yyyy-MM-dd H:m:s";
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        ZonedDateTime now = ZonedDateTime.now();

        return now.format(formatter);
    }
}
