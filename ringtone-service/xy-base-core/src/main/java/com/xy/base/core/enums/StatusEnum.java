package com.xy.base.core.enums;

import com.xy.base.core.exception.AppException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通用成功状态码
 *
 * <AUTHOR>
 * @since 2020/11/18
 */
@Getter
@AllArgsConstructor
public enum StatusEnum implements BaseDoubleEnum<Integer, String> {

    /**
     * 0,待处理
     */
    INIT(0, "0,待处理"),

    /**
     * 1,正常
     */
    NORMAL(1, "1,正常"),

    /**
     * 2,审核通过
     */
    APPROVED(2, "2,审核通过"),

    /**
     * 10,成功
     */
    SUCCESS(10, "10,成功"),

    /**
     * -1,禁用
     */
    DENY(-1, "-1,禁用"),

    /**
     * -2,审核拒绝
     */
    REFUSED(-2, "-2,审核拒绝"),

    /**
     * -10,失败
     */
    FAIL(-10, "-10,失败");

    private Integer code;

    private String value;

    public static StatusEnum of(int type) {
        switch (type) {
            case 0:
                return INIT;
            case 10:
                return SUCCESS;
            case -10:
                return FAIL;
            case -2:
                return REFUSED;
            case 2:
                return APPROVED;
            default:
                throw new AppException("错误的状态");
        }
    }

}
