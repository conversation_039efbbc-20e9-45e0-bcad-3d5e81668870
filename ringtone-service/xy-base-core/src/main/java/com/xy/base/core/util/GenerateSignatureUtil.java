package com.xy.base.core.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Map;
import java.util.Set;

/**
 * MD5 sign签名校验及生成工具
 *
 * <AUTHOR>
 * @since 2022/6/23.
 */
@Slf4j
public class GenerateSignatureUtil {

    public static final String FIELD_SIGN = "sign";


    /**
     * 判断签名是否正确，必须包含sign字段，否则返回false。
     *
     * @param data Map类型数据
     * @param key  API密钥
     * @return 签名是否正确
     */
    public static boolean isSignatureValid(Map<String, String> data, String key) {
        if (!data.containsKey(FIELD_SIGN)) {
            return false;
        }
        String sign = data.get(FIELD_SIGN);
        return generateSignature(data, key).equalsIgnoreCase(sign);
    }

    /**
     * 生成签名
     *
     * @param data Map类型数据
     * @param key  API密钥
     * @return 签名
     */
    public static String generateSignature(final Map<String, String> data, String key) {
        try {
            Set<String> keySet = data.keySet();
            String[] keyArray = keySet.toArray(new String[0]);
            Arrays.sort(keyArray);
            StringBuilder sb = new StringBuilder();
            for (String k : keyArray) {
                if (k.equals(FIELD_SIGN)) {
                    continue;
                }
                // 参数值为空，则不参与签名
                if (data.get(k).trim().length() > 0) {
                    sb.append(k).append("=").append(data.get(k).trim()).append("&");
                }
            }
            sb.append("key=").append(key);

            log.info("----准备加密：" + sb);

            String hex = DigestUtils.md5DigestAsHex(sb.toString().getBytes(StandardCharsets.UTF_8));

            log.info("----加密后：" + hex);

            return hex;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }
}
