package com.xy.base.core.util;

import cn.hutool.core.util.StrUtil;
import com.xy.base.core.annotation.ConvertName;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static com.xy.base.core.constant.CommonConsts.LOCAL_DATETIME_FORMATTER;
import static com.xy.base.core.constant.CommonConsts.LOCAL_DATE_FORMATTER;

/**
 * 转换工具（可配合@ConvertName使用）
 *
 * <AUTHOR>
 * @since 2022/10/19
 */
public class ConvertUtils {


    /**
     * 将列明和值对象转换为实体对象
     *
     * @param kvs  列明和值的map
     * @param cc   class
     * @param trim 对值是否去空格
     * @return T
     */
    public static <T> T map2Bean(Map<String, Object> kvs, Class<T> cc, boolean trim) throws InstantiationException, IllegalAccessException {

        T instance = cc.newInstance();
        List<Field> fields = new ArrayList<>();

        Class<?> superclass = cc;
        while (superclass != null) {
            Field[] declaredFields = superclass.getDeclaredFields();
            fields.addAll(Arrays.asList(declaredFields));
            superclass = superclass.getSuperclass();
        }

        // Lambda表达式中无法抛出异常，采用原始方式迭代
        Set<String> keys = kvs.keySet();
        for (String key : keys) {
            Object value = kvs.get(key);
            for (Field f : fields) {
                ConvertName annotation = f.getAnnotation(ConvertName.class);
                if (annotation != null && key.equals(annotation.value())) {
                    setFieldValue(instance, value, f, trim);
                    break;
                } else {
                    String camelName = StrUtil.toCamelCase(key);
                    if (f.getName().equals(key) || f.getName().equals(camelName)) {
                        setFieldValue(instance, value, f, trim);
                        break;
                    }
                }
            }
        }
        return instance;

    }

    /**
     * 将jdbc结果集转换为实体对象
     *
     * @param rs ResultSet
     * @param cc cc
     * @return T
     */
    public static <T> T rs2Bean(ResultSet rs, Class<T> cc) throws SQLException, InstantiationException, IllegalAccessException {
        return rs2Bean(rs, cc, true);
    }

    /**
     * 将jdbc结果集转换为实体对象
     *
     * @param rs   ResultSet
     * @param cc   cc
     * @param trim 是否去空格
     * @return T
     */
    public static <T> T rs2Bean(ResultSet rs, Class<T> cc, boolean trim) throws SQLException, InstantiationException, IllegalAccessException {

        //结果集 中列的名称和类型的信息
        ResultSetMetaData rsm = rs.getMetaData();
        int colNumber = rsm.getColumnCount();

        Map<String, Object> kvs = new HashMap<>();

        for (int i = 1; i <= colNumber; i++) {
            Object value = rs.getObject(i);
            String columnName = rsm.getColumnName(i);
            kvs.put(columnName, value);
        }
        return map2Bean(kvs, cc, trim);
    }

    /**
     * 设置实体的数据，带自动类型转换
     *
     * @param instance 实体
     * @param value    value
     * @param f        Field
     * @param trim     是否去空格
     */
    private static <T> void setFieldValue(T instance, Object value, Field f, boolean trim) throws IllegalAccessException {
        if (value == null) {
            return;
        }
        boolean flag = f.isAccessible();
        f.setAccessible(true);
        if (f.getType().isInstance(value)) {
            f.set(instance, value);
        } else {
            // 实体类型与数据类型不匹配，强制转换(并未完全转换，遇到没有的类型在添加)
            String typeName = f.getType().getName();
            String valueString = trim ? value.toString().trim() : value.toString();
            if (typeName.equals(String.class.getName())) {
                f.set(instance, valueString);
            } else if (typeName.equals(Integer.class.getName())) {
                f.set(instance, Integer.parseInt(valueString));
            } else if (typeName.equals(BigDecimal.class.getName())) {
                f.set(instance, new BigDecimal(valueString));
            } else if (typeName.equals(LocalDate.class.getName())) {
                f.set(instance, LocalDate.parse(valueString, LOCAL_DATE_FORMATTER));
            } else if (typeName.endsWith(LocalDateTime.class.getName())) {
                f.set(instance, LocalDateTime.parse(valueString, LOCAL_DATETIME_FORMATTER));
            } else {
                throw new RuntimeException("rs2Bean未经处理的类型转换：" + typeName);
            }
        }
        f.setAccessible(flag);
    }

}
