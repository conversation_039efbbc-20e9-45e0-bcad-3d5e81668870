package com.xy.base.core.dto.login;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * 登录参数
 *
 * <AUTHOR>
 * @since 2020/11/17
 */
@Data
public class LoginDTO {

    /**
     * 用户名，通常为手机号
     */
    @NotNull
    @Length(max = 100, min = 4, message = "请输入正确的用户名")
    private String username;

    /**
     * 密码（明文）
     */
    @NotNull
    @Length(max = 100, min = 6, message = "密码不能少于6位")
    private String password;

    /**
     * 微信openid，通常用于微信登录（非必填）
     */
    private String openid;

    /**
     * 用户类型
     *
     * @see com.xy.base.core.enums.UaaTypeEnum
     */
    private String type;
}
