package com.xy.base.core.util;

import com.github.yitter.contract.IdGeneratorOptions;
import com.github.yitter.idgen.YitIdHelper;

/**
 * id生成器，在雪花算法上缩短长度
 *
 * <AUTHOR>
 * @since 2024/7/25 11:04
 */
public class IdUtils {

    static {
        /*
         调整说明
         1.设置机器码的长度为1，即最多支持2台机器同时生成（0，1）
         2.设置序列数SeqBitLength为3，极限性能为每秒6000个左右
         其他说明
         到达每秒生成极限的时，并不会生成重复id，系统会暂停等待下一秒的到来
         在项目中，由于各模块都是引用该方法，并且workId通常相同，所以可能产生重复id，但是对业务系统没有影响，因为订单id的重复和商品id重复并不影响业务
         系统默认长度是13位，随着时间的推移，长度会增加到14，15位等
         如果一个模块有多各副本运行，比如交易模块有2个服务在同时运行，那么尽量设置不同的workId避免订单号重复(以下代码可从启动参数中获取id)
         另外需要docker启动配合
         docker run -d -p --name 自定义名称  -e Dockerfile中定义的变量名="XXX" 镜像id

         String worker = System.getProperty("mark.worker", "0");
         short workId = 0;
         try {
            workId = Short.parseShort(worker);
         } catch (Exception ignore) {
         }
         */

        IdGeneratorOptions options = new IdGeneratorOptions((short) 0);
        options.WorkerIdBitLength = 1;
        options.SeqBitLength = 3;

        YitIdHelper.setIdGenerator(options);
    }

    /**
     * 获取一个较短的雪花Id
     */
    public static Long nextId() {
        return YitIdHelper.nextId();
    }

    /**
     * 获取一个含有前缀的Id
     *
     * @param prefix 前缀字母，如D,p等。会自动大写
     */
    public static String nextIdWithPrefix(String prefix) {
        return prefix.toUpperCase() + YitIdHelper.nextId();
    }

    /**
     * 获取一个32进制表示的id
     * 优点：长度很短；缺点：含有字母，不易阅读
     */
    public static String nextUnsignedId() {
        return Long.toUnsignedString(YitIdHelper.nextId(), 32);
    }
}
