package com.xy.base.core.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.xy.base.core.constant.ResultCodeConsts;
import com.xy.base.core.enums.ExceptionResultEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2019-11-22
 */
@Data
public class Result<T> implements Serializable {

    /**
     * 结果码
     */
    private Integer code;

    /**
     * 错误码，字符串格式，用于细分错误类型
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String errorCode;

    /**
     * 结果信息
     */
    private String msg;

    /**
     * 返回数据
     */
    private T data;

    public Result(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public Result(Integer code, String errorCode, String msg, T data) {
        this.code = code;
        this.errorCode = errorCode;
        this.msg = msg;
        this.data = data;
    }

    public Result(ExceptionResultEnum e) {
        this.code = e.getCode();
        this.errorCode = e.getErrorCode();
        this.msg = e.getValue();
        this.data = null;
    }

    public static <T> Result<T> success(T data) {
        return new Result<>(ResultCodeConsts.SUCCESS, null, data);
    }

    public static <T> Result<T> error(int code, String msg) {
        return new Result<>(code, msg, null);
    }

    public static <T> Result<T> error(int code, String msg, T data) {
        return new Result<>(code, msg, data);
    }

    public static <T> Result<T> error(int code, String errorCode, String msg) {
        return new Result<>(code, errorCode, msg, null);
    }

    public static <T> Result<T> error(int code, String errorCode, String msg, T data) {
        return new Result<>(code, errorCode, msg, data);
    }
}
