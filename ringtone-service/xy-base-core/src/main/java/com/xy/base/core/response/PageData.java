package com.xy.base.core.response;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2019-11-22
 */
@Data
public class PageData<T> {

    /**
     * 页码信息
     */
    private PageMeta meta;

    /**
     * 数据内容
     */
    private List<T> lists;

    public PageData(long page, long total, long size, List<T> lists) {
        this.meta = new PageMeta(page, total, size);
        this.lists = lists;
    }

    @Data
    public static class PageMeta {
        /**
         * 当前页，从1开始
         */
        private long page;
        /**
         * 数据总量
         */
        private long total;
        /**
         * 每页数量
         */
        private long size;

        public PageMeta(long page, long total, long size) {
            this.page = page;
            this.total = total;
            this.size = size;
        }
    }
}


