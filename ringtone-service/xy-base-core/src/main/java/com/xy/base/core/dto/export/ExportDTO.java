package com.xy.base.core.dto.export;

import com.xy.base.core.enhancer.ValidList;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 导出参数
 *
 * <AUTHOR>
 * @since 2022/8/11.
 */
@Data
@Accessors(chain = true)
public class ExportDTO {

    /**
     * 导出的主要表名
     */
    @NotNull(message = "tableName不能为空了")
    private String tableName;

    /**
     * 文件名称（无需日期，系统会自动添加）
     */
    @NotNull(message = "fileName不能为空了")
    private String fileName;

    /**
     * 数据文件
     */
    @NotNull(message = "datas不能为空了")
    private ValidList<Map<String, Object>> datas;

    /**
     * 查询条件（非必须）
     */
    private String query;

}
