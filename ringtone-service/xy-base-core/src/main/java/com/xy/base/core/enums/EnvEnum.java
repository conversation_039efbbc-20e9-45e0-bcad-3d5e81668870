package com.xy.base.core.enums;

import com.xy.base.core.exception.AppException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 当前支持的环境变量
 *
 * <AUTHOR>
 * @since 2020/11/18
 */
@Getter
@AllArgsConstructor
public enum EnvEnum implements BaseEnum<String> {

    /* 开发环境 */
    DEV("dev"),

    /* 生产环境 */
    PRO("PRO");

    private final String value;

    public static EnvEnum of(String type) {
        switch (type) {
            case "pro":
                return PRO;
            case "dev":
                return DEV;
            default:
                throw new AppException("错误的环境");
        }
    }

}
