package com.xy.base.core.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2020/11/18
 */
@Getter
public enum ExceptionResultEnum implements BaseDoubleEnum<Integer, String> {

    /* 没有用户名 */
    ACCESS_DENIED(403, "未授权的访问"),

    /* 未登录 */
    NOT_LOGIN(406, "未登录"),

    /* 没有用户名 */
    LOGIN_NONUSER(410, "没有该用户"),

    /* 参数错误 */
    PARAMS_ERROR(420, "参数错误"),

    /* 密码错误 */
    LOGIN_PASSWORD_ERROR(410, "密码错误"),

    /* 手机号验证不通过 */
    CELLPHONE_VALIDATE_FAILED(450, "尊敬的用户，已为您申请体验资格，感谢您的支持！"),

    /* 参数错误 */
    DATA_DUPLICATE(601, "记录已存在"),

    /* 参数错误 */
    DATA_ACCESS_ERROR(600, "访问数据错误"),

    /* 参数错误 */
    FEIGN_RESPONSE_ERROR(610, "feign返回结果异常"),

    /* 参数错误 */
    BANK_SERVER_ERROR(700, "银行服务暂不可用，请稍后再试"),

    /* 该省份达到今日限量，明天可继续办理 */
    PROVINCE_LIMIT(450, "E010", "该省份达到今日限量，明天9点后可继续办理"),

    /* 该产品达到今日限量，明天可继续办理 */
    PRODUCT_LIMIT(450, "E011", "该产品达到今日限量，明天9点可继续办理"),

    /* 产品已下架，请暂停推广 */
    //PRODUCT_OFF_SHELF(450, "E012", "产品已下架，请暂停推广"),

    /* 该省份已下架，暂停发展该省份 */
    PROVINCE_OFF_SHELF(450, "E013", "该省份已下架，暂停发展该省份"),
    
    /* E101	其他失败（已订阅用户）*/
    OTHER_FAILED_SUBSCRIBED(450, "E10101", "其他失败（已订阅用户）"),
    
    /* E101	其他失败（非移动用户）*/
    OTHER_FAILED_NOT_MOBILE(450, "E10102", "其他失败（非移动用户）"),
    
    /* E101	其他失败（该城市限制发展） */
    OTHER_FAILED_CITY_LIMIT(450, "E10103", "其他失败（该城市限制发展）"),
    
    /* E101	其他失败（点位限制）*/
    OTHER_FAILED_POINT_LIMIT(450, "E10104", "其他失败（点位限制）"),
    
    /* E101	其他失败（APP限制发展）*/
    OTHER_FAILED_APP_LIMIT(450, "E10105", "其他失败（APP限制发展）"),
    
    /* E101	其他失败（手机号已订购或不符合办理条件）*/
    OTHER_FAILED_PHONE_LIMIT(450, "E10106", "其他失败（手机号已订购或不符合办理条件）"),

    /* 该渠道已暂停发展 */
    CHANNEL_PAUSED(450, "E015", "当前产品在该渠道的推广已暂停"),

    /* E101	其他失败（运营商或其他系统返回失败）*/
    OTHER_FAILED(450, "E101", "其他失败（运营商或其他系统返回失败）");

    private Integer code;
    private String errorCode;
    private String value;

    ExceptionResultEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    ExceptionResultEnum(Integer code, String errorCode, String value) {
        this.code = code;
        this.errorCode = errorCode;
        this.value = value;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
