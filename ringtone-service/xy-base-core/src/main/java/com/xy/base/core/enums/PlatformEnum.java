package com.xy.base.core.enums;

import com.xy.base.core.exception.AppException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2021/3/2
 */
@Getter
@AllArgsConstructor
public enum PlatformEnum implements BaseDoubleEnum<Integer, String> {

    /**
     * PC 2的19次方
     */
    PC("mall", 524288),
    /**
     * 微信公众号 2的20次方
     */
    WX_PUB("wx_pub", 1048576),

    /**
     * 微信小程序 2的21次方
     */
    WX_MINI("wx_mini", 2097152),

    /**
     * Android 2的22次方
     */
    ANDROID("android", 4194304),

    /**
     * IOS 2的23次方
     */
    IOS("ios", 8388608),


    /**
     * 00001 总控台
     */
    CONSOLE("console", 1),

    /**
     * 00010 金融，资信，财务
     */
    FINANCE("finance", 2),

    /**
     * 00100 商城运营
     */
    OPERATION("operation", 4),

    /**
     * 01000 控销
     */
    CONTROL("control", 8);

    private final String value;
    private final Integer code;


    /**
     * 转换为对应的平台Enum
     *
     * @param value value
     * @return return
     */
    public static PlatformEnum of(int value) {
        switch (value) {
            case 1:
                return CONSOLE;
            case 2:
                return FINANCE;
            case 4:
                return OPERATION;
            case 8:
                return CONTROL;
            case 524288:
                return PC;
            case 1048576:
                return WX_PUB;
            case 2097152:
                return WX_MINI;
            case 4194304:
                return ANDROID;
            case 8388608:
                return IOS;
            default:
                throw new AppException("错误的平台");
        }
    }

    /**
     * 转换为对应的平台Enum
     *
     * @param valueOrCode value
     * @return return
     */
    public static PlatformEnum of(String valueOrCode) {
        switch (valueOrCode) {
            case "console":
            case "1":
                return CONSOLE;
            case "finance":
            case "2":
                return FINANCE;
            case "operation":
            case "4":
                return OPERATION;
            case "controll":
            case "8":
                return CONTROL;
            case "mall":
            case "524288":
                return PC;
            case "wx_pub":
            case "1048576":
                return WX_PUB;
            case "wx_mini":
            case "2097152":
                return WX_MINI;
            case "android":
            case "4194304":
                return ANDROID;
            case "ios":
            case "8388608":
                return IOS;
            default:
                throw new AppException("错误的平台");
        }
    }
}
