package com.xy.base.core.util;

import cn.hutool.core.util.StrUtil;
import com.xy.base.core.exception.AppException;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2022年09月08日 15:41
 */
public class MapUtils {


    /**
     * 将前端地图控件选择的区域做优化，保留最大区域
     *
     * @param rawMapSelect rawMapSelect
     * @return List<String>
     */
    public static List<String> extractMapSelect(List<String> rawMapSelect) {
        // 原始数据中可能会出现重复的区域，比如选择了四川省，又展开了成都市，就会出现
        // ['51', '5101', '5103', '5104', '5105', '5106', '5107', '5108', '5109', '5110', '5111', '5113', '5114', '5115', '5116', '5117', '5118', '5119', '5120', '5132', '5133', '5134', '110101001', '120101']
        // 这样的数据
        // 目的是优化为['51','110101001', '120101']

        rawMapSelect.sort((a, b) -> {
            if (a.length() > b.length()) {
                return 1;
            } else if (a.length() < b.length()) {
                return -1;
            }
            return 0;
        });
        List<String> extracts = new ArrayList<>();

        rawMapSelect.forEach(raw -> {
            boolean drop = false;
            for (String ext : extracts) {
                if (raw.startsWith(ext)) {
                    drop = true;
                    break;
                }
            }
            if (!drop) {
                extracts.add(raw);
            }
        });

        return extracts;
    }


    /**
     * 将前端地图控件选择的区域做优化，保留最大区域，返回区域字符串
     *
     * @param rawMapSelect rawMapSelect
     * @return String
     */
    public static String extractMapSelect2String(List<String> rawMapSelect) {

        List<String> extracts = extractMapSelect(rawMapSelect);
        return StrUtil.join(",", extracts);
    }

    /**
     * 返回销售区域的正则表达式
     *
     * @param mapDistrict 要转换的区域
     * @return String
     */
    public static String districtStr2Regex(String mapDistrict) {

        // (^四川省/内江市.*?)|(^四川省/宜宾市.*?)|(^四川省/绵阳市.*?)|(^四川省/眉山市.*?)|(^四川省/雅安市.*?)

        String[] maps = mapDistrict.split(",");
        StringJoiner sj = new StringJoiner("|");
        for (String map : maps) {
            sj.add("(^" + map + ".*?)");
        }

        return sj.toString();
    }

    /**
     * 判断区域mapDistrict是否包含toMatch
     *
     * @param mapDistrict 地图区域
     * @param toMatch     要判断的区域
     * @param errorMsg    异常的消息(如果为空表示不跑出异常)
     * @return boolean
     */
    public static boolean matchDistrict(String mapDistrict, String toMatch, String errorMsg) {

        AppException.tnt(!StringUtils.hasText(mapDistrict), "区域为空");

        String districtRegex = districtStr2Regex(mapDistrict);

        boolean matches = Pattern.matches(districtRegex, toMatch);

        if (!matches) {
            AppException.tnt(StringUtils.hasText(errorMsg), errorMsg);
        }
        return matches;
    }


}
