# ================================
# XY-Admin Service Docker Compose
# ================================
# 用于部署xy-admin管理服务
# 已解决Spring Cloud Gateway冲突问题

version: '3.8'

services:
  xy-admin:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        PROFILE: dev-zsgl
        MARK: JB

    container_name: xy-admin-service
    hostname: xy-admin

    # 端口映射
    ports:
      - "8915:8915"

    # 数据卷挂载
    volumes:
      # 外部依赖库目录（对应Dockerfile中的/app/lib）
      - /data/xy-admin/lib:/app/lib:ro
      # 日志目录
      - /data/xy-admin/logs:/app/logs
      # 临时文件目录
      - /tmp/xy-admin:/tmp

    # 环境变量
    environment:
      - SPRING_PROFILE=dev-zsgl
      - MARK_NAME=JB
      - TZ=Asia/Shanghai
      # JVM调优参数（可根据实际情况调整）
      - JAVA_OPTS=-server -XX:MetaspaceSize=64m -XX:MaxMetaspaceSize=512m -Xms512m -Xmx1024m -Xmn256m -Xss256k -XX:+UseParNewGC -XX:+UseConcMarkSweepGC

    # 重启策略
    restart: unless-stopped

    # 网络配置
    networks:
      - 1panel-network
# 网络配置
networks:
  1panel-network:
    external: true