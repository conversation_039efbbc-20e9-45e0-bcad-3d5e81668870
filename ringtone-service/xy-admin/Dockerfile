# ================================
# XY-Admin Service Dockerfile
# ================================
# 基于OpenJDK 8构建xy-admin管理服务
# 解决Spring Cloud Gateway与Spring MVC冲突问题

# 使用官方OpenJDK 8基础镜像
FROM openjdk:8-jdk-alpine

# 设置维护者信息
LABEL maintainer="nto" \
      service="xy-admin" \
      version="1.0.0" \
      description="XY Admin Management Service"

# 构建参数定义
ARG PROFILE=dev-test
ARG MARK=JB
ARG JAR_FILE=target/xy-admin-1.0.0-boot.jar

# 环境变量设置
ENV SPRING_PROFILE=${PROFILE} \
    MARK_NAME=${MARK} \
    TZ=Asia/Shanghai \
    JAVA_OPTS="-server -XX:MetaspaceSize=32m -XX:MaxMetaspaceSize=256m -Xms256m -Xmx256m -Xmn128m -Xss256k -XX:+UseParNewGC -XX:+UseConcMarkSweepGC" \
#    JAVA_DEBUG_OPTS=" -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5006"
    # 开启调试模式时，需要在Dockerfile中添加该环境变量
    JAVA_DEBUG_OPTS=""

# 设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建应用目录
WORKDIR /app

# 复制应用文件
COPY ${JAR_FILE} /app/app.jar
COPY src/main/resources/ /app/resources/

# 创建外部依赖库目录（用于-Dloader.path）
#RUN mkdir -p /app/lib

# 设置数据卷
VOLUME ["/tmp", "/app/logs"]

# 暴露端口
EXPOSE 8915 5005

# 启动命令 - 包含Gateway冲突解决方案
CMD ["sh", "-c", "java ${JAVA_OPTS} ${JAVA_DEBUG_OPTS} \
    -Dmark.name=${MARK_NAME} \
    -Dloader.path=/app/lib \
    -Dspring.autoconfigure.exclude=org.springframework.cloud.gateway.config.GatewayAutoConfiguration,org.springframework.cloud.gateway.config.GatewayClassPathWarningAutoConfiguration,org.springframework.cloud.gateway.config.GatewayRedisAutoConfiguration,org.springframework.cloud.gateway.config.GatewayLoadBalancerClientAutoConfiguration,org.springframework.cloud.gateway.config.GatewayMetricsAutoConfiguration \
    -jar /app/app.jar \
    --spring.config.location=classpath:/,classpath:/application-${SPRING_PROFILE}.yml"]
#    --spring.profiles.active=${SPRING_PROFILE}"]
