package com.xy.admin.vo.productCompanyInfo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 公司表新增请求VO
 * <AUTHOR>
 * @since 2025/7/16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductCompanyInfoAddVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 公司ID
     */
    @NotBlank(message = "公司ID不能为空")
    @Size(max = 255, message = "公司ID最大长度要小于 255")
    private String companyId;
    
    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空")
    @Size(max = 255, message = "公司名称最大长度要小于 255")
    private String companyName;
}
