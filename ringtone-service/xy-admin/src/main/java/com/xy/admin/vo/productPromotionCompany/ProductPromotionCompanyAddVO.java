package com.xy.admin.vo.productPromotionCompany;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 推广商表新增请求VO
 * <AUTHOR>
 * @since 2025/7/16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductPromotionCompanyAddVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 公司ID
     */
    @NotBlank(message = "公司ID不能为空")
    @Size(max = 256, message = "公司ID最大长度要小于 256")
    private String companyId;
    
    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空")
    @Size(max = 256, message = "公司名称最大长度要小于 256")
    private String companyName;
    
    // 注意：appId和secretKey不需要在新增VO中，因为会自动生成
}
