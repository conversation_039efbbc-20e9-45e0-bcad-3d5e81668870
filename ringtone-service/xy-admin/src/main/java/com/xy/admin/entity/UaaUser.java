package com.xy.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@TableName(value = "uaa_user")
@Data
public class UaaUser implements Serializable {

    /**
     * 主键
	 * @NeverNull
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 用户名（微信昵称）
     */
    private String username;
    /**
     * 密码
     */
    private String password;
    /**
     * 状态：0禁用，1启用
	 * @NeverNull
     */
    private Integer status;
    /**
     * 联系电话
     */
    private String phone;
    /**
     * 用户openid
     */
    private String openid;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
