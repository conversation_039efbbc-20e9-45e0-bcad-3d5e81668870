package com.xy.admin.controller;

import com.xy.admin.dto.OverViewQueryDTO;
import com.xy.admin.entity.StatOverview;
import com.xy.admin.service.StatOverviewService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

/**
 * 统计分析相关
 *
 * <AUTHOR>
 * @since 2024/2/28 14:03
 */
@Slf4j
@RestController
@RequestMapping("/ana")
@AllArgsConstructor
public class StatAnaController {

    private final StatOverviewService statOverviewService;

    /**
     * 概览数据（不会用到分页）
     */
    @PostMapping("/overview/all")
    public List<StatOverview> overviewAll(@RequestBody OverViewQueryDTO query) {

        if (query.getStartDate() == null) {
            query.setEndDate(LocalDate.now());
            query.setStartDate(LocalDate.now().minusDays(7));
        }

        return statOverviewService.lambdaQuery()
                .ge(query.getStartDate() != null, StatOverview::getDate, query.getStartDate())
                .le(query.getEndDate() != null, StatOverview::getDate, query.getEndDate())
                .eq(StringUtils.hasText(query.getChannel()), StatOverview::getChannel, query.getChannel())
                .eq(StringUtils.hasText(query.getSource()), StatOverview::getSource, query.getSource())
                .eq(StringUtils.hasText(query.getPlatform()), StatOverview::getPlatform, query.getPlatform())
                .orderByDesc(StatOverview::getDate)
                .list();
    }

}
