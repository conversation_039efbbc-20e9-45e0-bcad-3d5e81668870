package com.xy.admin.mapper;

import com.xy.admin.dto.productAnalysis.ProductAnalysisStatsDTO;
import com.xy.admin.vo.productAnalysis.ProductAnalysisQueryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品分析Mapper
 * <AUTHOR>
 * @since 2025/08/04
 */
@Mapper
public interface ProductAnalysisMapper {

    /**
     * 获取概览统计数据
     * @param queryVO 查询条件
     * @return 统计数据
     */
    ProductAnalysisStatsDTO getOverviewStats(@Param("query") ProductAnalysisQueryVO queryVO);

    /**
     * 获取按日期分组的统计数据（用于图表展示）
     * @param queryVO 查询条件
     * @return 按日期分组的统计数据列表
     */
    List<ProductAnalysisStatsDTO> getDailyStats(@Param("query") ProductAnalysisQueryVO queryVO);
}