package com.xy.admin.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.admin.entity.DiyUser;
import com.xy.admin.entity.RingtoneOrder;
import com.xy.admin.entity.UaaUser;
import com.xy.admin.mapper.DiyUserMapper;
import com.xy.admin.service.AliOssService;
import com.xy.admin.service.DiyUserService;
import com.xy.admin.service.RingtoneOrderService;
import com.xy.admin.service.UaaUserService;
import com.xy.admin.util.AliyunVoiceComponent;
import com.xy.admin.util.CVUtil;
import com.xy.base.core.constant.CommonConsts;
import com.xy.base.core.util.IdUtils;
import com.xy.lib.migu.online.RingOperation;
import lombok.RequiredArgsConstructor;
import org.jaudiotagger.audio.AudioFile;
import org.jaudiotagger.audio.AudioFileIO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 针对表【diy_user(用户diy数据)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Service
@RequiredArgsConstructor
public class DiyUserServiceImpl extends ServiceImpl<DiyUserMapper, DiyUser>
        implements DiyUserService {

    @Value("${oss.local}")
    private String local;

    private final UaaUserService uaaUserService;
    private final RingtoneOrderService ringtoneOrderService;

    private final AliyunVoiceComponent voiceComponent;
    private final AliOssService aliOssService;


    @Override
    public void compose(DiyUser diy) {

        // 图文需要先合成
        if (diy.getType().equals(CommonConsts.TWO)) {
            // 需要先合成音频
            String targetVoicePath = StrUtil.format("{}{}{}/{}.mp3", local, aliOssService.getFolder(4), diy.getUserId(), IdUtils.nextIdWithPrefix("voice"));

            // zhida zhiyue zhinan zhiru zhitian_emo
            voiceComponent.speechLongSynthesizer(diy.getText(), diy.getTextTimbre(), targetVoicePath, f -> {
                // 将图片地址转为本地地址
                List<String> localPics = new ArrayList<>();
                for (String pic : diy.getPics()) {
                    String s = aliOssService.url2LocalPath(pic);
                    localPics.add(s);
                }
                try {
                    // 语音合成完毕后合成视频
                    String targetFolder = local + aliOssService.getFolder(4) + diy.getUserId();
                    AudioFile audioFile = AudioFileIO.read(new File(targetVoicePath));
                    int seconds = audioFile.getAudioHeader().getTrackLength();
                    String composed = CVUtil.compose(f, localPics, seconds, targetFolder);
                    diy.setTargetVoice(targetVoicePath);
                    diy.setTargetVideo(composed);

                    updateById(diy);

                    // 合成完毕
                    setRing(diy.getUserId(), composed, diy);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        } else {
            String videoPath = aliOssService.url2LocalPath(diy.getVideo());
            setRing(diy.getUserId(), videoPath, diy);
        }
        // 上传视频并且设置为用户的彩铃
    }

    /**
     * 生成订单，然后上传
     */
    private void setRing(Integer userId, String videoPath, DiyUser diy) {

        UaaUser user = uaaUserService.getById(userId);

        RingtoneOrder order = new RingtoneOrder();

        String orderNumber = Long.toUnsignedString(IdUtil.getSnowflakeNextId(), 32).toUpperCase();

        String ringName = "DIY" + (diy.getType().equals(CommonConsts.ONE) ? "视频" : "图文");

        order.setOrderNumber(orderNumber);
        order.setCreateTime(LocalDateTime.now());
        order.setOpenid(user.getOpenid());
        order.setUserId(user.getId());
        order.setPhone(user.getPhone());
        order.setFee(0);
        order.setCurrent(true);
        order.setProductId("diy");
        order.setRingtoneCover(aliOssService.local2Url(videoPath));
        order.setRingtoneName(ringName);
        order.setPayed(true);
        order.setRingtoneId(diy.getId() + "");
        order.setPayTime(LocalDateTime.now());
        order.setStatus(10);

        ringtoneOrderService.lambdaUpdate()
                .eq(RingtoneOrder::getUserId, user.getId())
                .set(RingtoneOrder::getCurrent, false)
                .update();
        ringtoneOrderService.save(order);

        RingOperation.uploadWithOrder4Phone(orderNumber, user.getPhone(), ringName, videoPath, "D:/copyright/通用证书.pdf");

    }


}




