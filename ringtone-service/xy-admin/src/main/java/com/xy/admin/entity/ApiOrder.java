package com.xy.admin.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * 
 * <AUTHOR>
 * @since 2025/7/30 15:39
 */
/**
 * 营销推广订单综合表
 */
@Data
@Accessors(chain = true)
@SuperBuilder
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "api_order")
public class ApiOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "主键ID不能为null")
    private Long id;

    /**
     * 应用ID
     */
    @TableField(value = "app_id")
    @Size(max = 32,message = "应用ID最大长度要小于 32")
    private String appId;

    /**
     * 渠道编号
     */
    @TableField(value = "channel_no")
    @Size(max = 32,message = "渠道编号最大长度要小于 32")
    private String channelNo;

    /**
     * 产品编号
     */
    @TableField(value = "product_no")
    @Size(max = 32,message = "产品编号最大长度要小于 32")
    private String productNo;

    /**
     * 手机号码
     */
    @TableField(value = "mobile_no")
    @Size(max = 20,message = "手机号码最大长度要小于 20")
    private String mobileNo;

    /**
     * 订单ID
     */
    @TableField(value = "order_id")
    @Size(max = 64,message = "订单ID最大长度要小于 64")
    private String orderId;

    /**
     * 订单状态码
     */
    @TableField(value = "order_status")
    @Size(max = 16,message = "订单状态码最大长度要小于 16")
    private String orderStatus;

    /**
     * 外部订单状态
     */
    @TableField(value = "out_order_status")
    @Size(max = 16,message = "外部订单状态最大长度要小于 16")
    private String outOrderStatus;

    /**
     * 外部订单ID
     */
    @TableField(value = "out_order_id")
    @Size(max = 64,message = "外部订单ID最大长度要小于 64")
    private String outOrderId;

    /**
     * 操作类型：SMS-短信发送，ORDER-订单提交
     */
    @TableField(value = "operation_type")
    @Size(max = 32,message = "操作类型：SMS-短信发送，ORDER-订单提交最大长度要小于 32")
    private String operationType;

    /**
     * 客户端IP
     */
    @TableField(value = "client_ip")
    @Size(max = 64,message = "客户端IP最大长度要小于 64")
    private String clientIp;

    /**
     * 用户代理
     */
    @TableField(value = "user_agent")
    @Size(max = 1024,message = "用户代理最大长度要小于 1024")
    private String userAgent;

    /**
     * 应用包名
     */
    @TableField(value = "app_package")
    @Size(max = 128,message = "应用包名最大长度要小于 128")
    private String appPackage;

    /**
     * 应用名称
     */
    @TableField(value = "app_name")
    @Size(max = 128,message = "应用名称最大长度要小于 128")
    private String appName;

    /**
     * 平台名称
     */
    @TableField(value = "platform")
    @Size(max = 64,message = "平台名称最大长度要小于 64")
    private String platform;

    /**
     * 页面URL
     */
    @TableField(value = "page_url")
    @Size(max = 1024,message = "页面URL最大长度要小于 1024")
    private String pageUrl;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @Size(max = 512,message = "备注最大长度要小于 512")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "创建时间不能为null")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "更新时间不能为null")
    private Date updateTime;

    /**
     * 服务器
     */
    @TableField(value = "server")
    @Size(max = 32,message = "服务器最大长度要小于 32")
    private String server;
}