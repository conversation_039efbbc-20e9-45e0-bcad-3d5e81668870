package com.xy.admin.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.admin.entity.RingtoneCategory;
import com.xy.admin.service.RingtoneCategoryService;
import com.xy.admin.mapper.RingtoneCategoryMapper;
import com.xy.admin.vo.CategoryTreeVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 针对表【ringtone_category(模板分类)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2023-12-26
 */
@Service
public class RingtoneCategoryServiceImpl extends ServiceImpl<RingtoneCategoryMapper, RingtoneCategory>
        implements RingtoneCategoryService {

    @Override
    public List<CategoryTreeVO> tree() {
        List<RingtoneCategory> categories = lambdaQuery().orderByDesc(RingtoneCategory::getId).list();
        return buildTree(categories, 0);
    }

    private List<CategoryTreeVO> buildTree(List<RingtoneCategory> categories, Integer parentId) {
        List<CategoryTreeVO> trees = null;
        for (RingtoneCategory category : categories) {
            if (category.getPid().equals(parentId)) {
                CategoryTreeVO tree = new CategoryTreeVO();
                BeanUtils.copyProperties(category, tree);
                tree.setChildren(buildTree(categories, category.getId()));
                if (null == trees) {
                    trees = new ArrayList<>();
                }
                trees.add(tree);
            }
        }
        return trees;
    }
}




