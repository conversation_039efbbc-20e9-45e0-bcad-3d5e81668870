package com.xy.admin.controller;

import com.xy.admin.service.AliOssService;
import com.xy.base.core.enums.ExceptionResultEnum;
import com.xy.base.core.exception.AppException;
import com.xy.base.core.response.Result;
import com.xy.base.starter.security.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传
 *
 * <AUTHOR>
 * @since 2023/12/26 11:30
 */
@RequiredArgsConstructor
@RestController
@RequestMapping(value = "/upload")
public class UploadController {

    private final AliOssService aliOssService;

    /**
     * @param multipartFile multipartFile
     * @param type          1模板
     * @ignore 上传文件到oss并返回访问地址
     **/
    @RequestMapping("/oss")
    public Result<String> oss(@RequestParam("file") MultipartFile multipartFile,
                              @RequestParam(value = "type", required = false) Integer type) {

        boolean flag = multipartFile.isEmpty() || !StringUtils.hasText(multipartFile.getOriginalFilename());
        AppException.tnt(flag, ExceptionResultEnum.PARAMS_ERROR);
        return Result.success(aliOssService.uploadAliOss(multipartFile, type));
    }

    /**
     * @param multipartFile multipartFile
     * @param type          1模板
     * @ignore 上传文件到本地
     **/
    @RequestMapping("/local")
    public Result<String> local(@RequestParam("file") MultipartFile multipartFile,
                                @RequestParam(value = "type", required = false) Integer type) {

        boolean flag = multipartFile.isEmpty() || !StringUtils.hasText(multipartFile.getOriginalFilename());
        AppException.tnt(flag, ExceptionResultEnum.PARAMS_ERROR);

        String folder = aliOssService.getFolder(type);
        if (type.equals(4)) {
            if (SecurityUtils.isAdmin()) {
                folder = folder + "sys/";
            } else {
                folder = folder + SecurityUtils.getId() + "/";
            }
        }
        return Result.success(aliOssService.uploadLocal(multipartFile, folder));
    }
}
