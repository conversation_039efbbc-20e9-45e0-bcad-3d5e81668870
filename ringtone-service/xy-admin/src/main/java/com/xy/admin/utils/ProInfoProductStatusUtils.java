package com.xy.admin.utils;

import com.xy.admin.enums.DevelopmentStatusEnum;
import com.xy.admin.enums.ProductStatusEnum;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品推广信息状态工具类
 * 
 * <AUTHOR>
 * @since 2025/7/18
 */
public class ProInfoProductStatusUtils {
    
    /**
     * 获取所有产品状态列表
     * 
     * @return 产品状态列表
     */
    public static List<ProductStatusEnum> getAllProductStatuses() {
        return Arrays.asList(ProductStatusEnum.values());
    }
    
    /**
     * 获取所有开发对接状态列表
     * 
     * @return 开发对接状态列表
     */
    public static List<DevelopmentStatusEnum> getAllDevelopmentStatuses() {
        return Arrays.asList(DevelopmentStatusEnum.values());
    }
    
    /**
     * 获取产品状态选项（用于前端下拉框）
     * 
     * @return 状态选项列表，格式：[{code: 1, description: "待接入"}, ...]
     */
    public static List<StatusOption> getProductStatusOptions() {
        return Arrays.stream(ProductStatusEnum.values())
                .map(status -> new StatusOption(status.getCode(), status.getDescription()))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取开发对接状态选项（用于前端下拉框）
     * 
     * @return 状态选项列表，格式：[{code: 1, description: "排期中"}, ...]
     */
    public static List<StatusOption> getDevelopmentStatusOptions() {
        return Arrays.stream(DevelopmentStatusEnum.values())
                .map(status -> new StatusOption(status.getCode(), status.getDescription()))
                .collect(Collectors.toList());
    }
    
    /**
     * 验证产品状态是否有效
     * 
     * @param productStatus 产品状态码
     * @return 是否有效
     */
    public static boolean isValidProductStatus(Byte productStatus) {
        return ProductStatusEnum.isValidCode(productStatus);
    }
    
    /**
     * 验证开发对接状态是否有效
     * 
     * @param developmentStatus 开发对接状态码
     * @return 是否有效
     */
    public static boolean isValidDevelopmentStatus(Byte developmentStatus) {
        return DevelopmentStatusEnum.isValidCode(developmentStatus);
    }
    
    /**
     * 获取产品状态描述
     * 
     * @param productStatus 产品状态码
     * @return 状态描述
     */
    public static String getProductStatusDescription(Byte productStatus) {
        return ProductStatusEnum.getDescriptionByCode(productStatus);
    }
    
    /**
     * 获取开发对接状态描述
     * 
     * @param developmentStatus 开发对接状态码
     * @return 状态描述
     */
    public static String getDevelopmentStatusDescription(Byte developmentStatus) {
        return DevelopmentStatusEnum.getDescriptionByCode(developmentStatus);
    }
    
    /**
     * 状态选项内部类
     */
    public static class StatusOption {
        private Byte code;
        private String description;
        
        public StatusOption(Byte code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public Byte getCode() {
            return code;
        }
        
        public void setCode(Byte code) {
            this.code = code;
        }
        
        public String getDescription() {
            return description;
        }
        
        public void setDescription(String description) {
            this.description = description;
        }
    }
}
