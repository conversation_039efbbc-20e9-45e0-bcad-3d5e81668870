package com.xy.admin.service;

import com.xy.admin.annotation.DataSource;
import com.xy.admin.util.DataSourceContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 多数据源服务示例
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Service
@Slf4j
public class MultiDataSourceService {

  @Autowired
  private JdbcTemplate jdbcTemplate;

  /**
   * 查询主数据库
   */
  @DataSource("primary")
  public List<Map<String, Object>> queryFromPrimary() {
    log.info("查询主数据库(ringtone1)");
    return jdbcTemplate.queryForList("SELECT * FROM package_filter LIMIT 10");
  }

  /**
   * 查询次要数据库
   */
  @DataSource("secondary")
  public List<Map<String, Object>> queryFromSecondary() {
    log.info("查询次要数据库(ringpromote1)");
    return jdbcTemplate.queryForList("SELECT * FROM config_huichuan LIMIT 10");
  }

  /**
   * 查询第三数据库
   */
  @DataSource("tertiary")
  public List<Map<String, Object>> queryFromTertiary() {
    log.info("查询第三数据库(promote1)");
    return jdbcTemplate.queryForList("SELECT * FROM product_company_info LIMIT 10");
  }

  /**
   * 查询第四数据库
   */
  @DataSource("fourth")
  public List<Map<String, Object>> queryFromFourth() {
    log.info("查询第四数据库(traffic1)");
    return jdbcTemplate.queryForList("SELECT * FROM ad_recommend_record LIMIT 10");
  }

  /**
   * 在同一个方法中查询多个数据源
   * 注意：这种方式需要手动切换数据源
   */
  public Map<String, Object> queryMultipleDataSources() {
    Map<String, Object> result = new java.util.HashMap<>();

    try {
      // 查询主数据库
      DataSourceContextHolder.setDataSource("primary");
      List<Map<String, Object>> primaryData = jdbcTemplate.queryForList("SELECT COUNT(*) as count FROM package_filter");
      result.put("primary", primaryData);

      // 切换到次要数据库
      DataSourceContextHolder.setDataSource("secondary");
      List<Map<String, Object>> secondaryData = jdbcTemplate.queryForList("SELECT COUNT(*) as count FROM dliang_info");
      result.put("secondary", secondaryData);

      // 切换到第三数据库
      DataSourceContextHolder.setDataSource("tertiary");
      List<Map<String, Object>> tertiaryData = jdbcTemplate.queryForList("SELECT COUNT(*) as count FROM pro_order");
      result.put("tertiary", tertiaryData);

      // 切换到第四数据库
      DataSourceContextHolder.setDataSource("fourth");
      List<Map<String, Object>> fourthData = jdbcTemplate.queryForList("SELECT COUNT(*) as count FROM ad_recommend_record");
      result.put("fourth", fourthData);

    } finally {
      // 清除数据源上下文
      DataSourceContextHolder.clearDataSource();
    }

    return result;
  }

  /**
   * 带事务的多数据源操作示例
   * 注意：每个数据源需要单独的事务管理器
   */
  @DataSource("primary")
  @Transactional
  public void transactionalOperation() {
    log.info("在主数据库中执行事务操作");
    // 执行数据库操作
    jdbcTemplate.update("UPDATE test SET field = ? WHERE id = ?", "value", 1);
  }

  /**
   * 批量操作示例 - 在不同数据源中插入数据
   */
  public void batchOperationExample() {
    // 在主数据库中插入数据
    DataSourceContextHolder.setDataSource("primary");
    try {
      jdbcTemplate.update("INSERT INTO test (field) VALUES (?)", "primary_data");
      log.info("在主数据库中插入数据成功");
    } finally {
      DataSourceContextHolder.clearDataSource();
    }

    // 在次要数据库中插入数据
    DataSourceContextHolder.setDataSource("secondary");
    try {
      jdbcTemplate.update("INSERT INTO test (field) VALUES (?)", "secondary_data");
      log.info("在次要数据库中插入数据成功");
    } finally {
      DataSourceContextHolder.clearDataSource();
    }

    // 在第三数据库中插入数据
    DataSourceContextHolder.setDataSource("tertiary");
    try {
      jdbcTemplate.update("INSERT INTO test (field) VALUES (?)", "tertiary_data");
      log.info("在第三数据库中插入数据成功");
    } finally {
      DataSourceContextHolder.clearDataSource();
    }

    // 在第四数据库中插入数据
    DataSourceContextHolder.setDataSource("fourth");
    try {
      jdbcTemplate.update("INSERT INTO test (field) VALUES (?)", "fourth_data");
      log.info("在第四数据库中插入数据成功");
    } finally {
      DataSourceContextHolder.clearDataSource();
    }
  }
}