package com.xy.admin.vo.productAnalysis;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 产品分析查询VO
 * <AUTHOR>
 * @since 2025/08/04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductAnalysisQueryVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 开始时间（必填）
     */
    @NotNull(message = "开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间（必填）
     */
    @NotNull(message = "结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 渠道编号（可选）
     */
    private String channelNo;

    /**
     * 产品编号（可选）
     */
    private String productNo;

    /**
     * 服务器（可选）
     */
    private String server;
}