package com.xy.admin.aspect;

import com.xy.admin.annotation.DataSource;
import com.xy.admin.util.DataSourceContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 数据源切换切面
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Aspect
@Component
@Slf4j
@Order(1) // 确保在事务之前执行
public class DataSourceAspect {
	
	// 扩展切点，包括MyBatis Mapper方法调用
	@Pointcut("@annotation(com.xy.admin.annotation.DataSource) || @within(com.xy.admin.annotation.DataSource) || execution(* com.xy.admin.mapper..*(..))")
	public void dataSourcePointCut() {
	}
	
	@Before("dataSourcePointCut()")
	public void switchDataSource(JoinPoint point) {
		MethodSignature signature = (MethodSignature) point.getSignature();
		Method method = signature.getMethod();
		
		// 首先检查方法级别的注解
		DataSource dataSource = method.getAnnotation(DataSource.class);
		
		// 如果方法上没有注解，检查类级别的注解
		if (dataSource == null) {
			dataSource = method.getDeclaringClass().getAnnotation(DataSource.class);
		}
		
		// 如果还是没有，检查调用栈中是否有带@DataSource注解的方法
		if (dataSource == null) {
			dataSource = findDataSourceInCallStack();
		}
		
		if (dataSource != null) {
			String dsName = dataSource.value();
			DataSourceContextHolder.setDataSource(dsName);
			log.debug("切换到数据源: {}, 方法: {}.{}", dsName, method.getDeclaringClass().getSimpleName(), method.getName());
			
			// 添加验证日志
			String currentDataSource = DataSourceContextHolder.getDataSource();
			if (!dsName.equals(currentDataSource)) {
				log.warn("数据源切换可能失败！期望：{}，实际：{}", dsName, currentDataSource);
			} else {
				log.debug("数据源切换成功验证：{}", currentDataSource);
			}
		} else {
			String currentDataSource = DataSourceContextHolder.getDataSource();
			if (currentDataSource != null) {
				log.debug("继承使用当前数据源: {}, 方法: {}.{}",
						currentDataSource,
						point.getSignature().getDeclaringType().getSimpleName(),
						point.getSignature().getName());
			} else {
				log.debug("方法 {}.{} 使用默认数据源",
						point.getSignature().getDeclaringType().getSimpleName(),
						point.getSignature().getName());
			}
		}
	}
	
	@After("dataSourcePointCut()")
	public void restoreDataSource(JoinPoint point) {
		String currentDataSource = DataSourceContextHolder.getDataSource();
		
		// 只有当方法或类上有@DataSource注解时才清除上下文
		MethodSignature signature = (MethodSignature) point.getSignature();
		Method method = signature.getMethod();
		DataSource dataSource = method.getAnnotation(DataSource.class);
		if (dataSource == null) {
			dataSource = method.getDeclaringClass().getAnnotation(DataSource.class);
		}
		
		if (dataSource != null) {
			DataSourceContextHolder.clearDataSource();
			log.debug("清除数据源上下文, 方法: {}.{}, 之前的数据源: {}",
					point.getSignature().getDeclaringType().getSimpleName(),
					point.getSignature().getName(),
					currentDataSource);
		}
	}
	
	/**
	 * 在调用栈中查找@DataSource注解
	 */
	private DataSource findDataSourceInCallStack() {
		StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
		for (StackTraceElement element : stackTrace) {
			try {
				Class<?> clazz = Class.forName(element.getClassName());
				Method[] methods = clazz.getDeclaredMethods();
				for (Method method : methods) {
					if (method.getName().equals(element.getMethodName())) {
						DataSource annotation = method.getAnnotation(DataSource.class);
						if (annotation != null) {
							log.debug("在调用栈中找到数据源注解: {}", annotation.value());
							return annotation;
						}
						annotation = clazz.getAnnotation(DataSource.class);
						if (annotation != null) {
							log.debug("在调用栈中找到数据源注解: {}", annotation.value());
							return annotation;
						}
					}
				}
			} catch (ClassNotFoundException e) {
				// 忽略类未找到的异常
			}
		}
		return null;
	}
}