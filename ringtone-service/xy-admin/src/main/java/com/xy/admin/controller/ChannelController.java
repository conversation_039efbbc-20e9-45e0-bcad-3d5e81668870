package com.xy.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xy.admin.entity.ChChannel;
import com.xy.admin.entity.ChRing;
import com.xy.admin.entity.ChSource;
import com.xy.admin.service.ChChannelService;
import com.xy.admin.service.ChRingService;
import com.xy.admin.service.ChSourceService;
import com.xy.base.core.exception.AppException;
import com.xy.base.starter.dto.CommonPageQuery;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 渠道商管理
 *
 * <AUTHOR>
 * @since 2024/2/20
 */
@RestController
@RequestMapping("/channel")
@RequiredArgsConstructor
public class ChannelController {

    private final ChChannelService channelService;
    private final ChRingService ringService;
    private final ChSourceService sourceService;

    /**
     * 供应商列表
     */
    @PostMapping("/channel/list")
    public IPage<ChChannel> channelList(@RequestBody CommonPageQuery query) {
        Page<ChChannel> page = query.buildPage();
        return channelService.lambdaQuery()
                .and(StringUtils.hasText(query.getKeyword()),
                        w -> w.like(ChChannel::getName, query.getKeyword()).or()
                                .like(ChChannel::getChannel, query.getKeyword())
                )
                .orderByDesc(ChChannel::getCreateTime)
                .page(page);
    }


    /**
     * 供应商列表不分页
     */
    @GetMapping("/channel/all")
    public List<ChChannel> channelAll() {
        return channelService.lambdaQuery()
                .select(ChChannel::getChannel, ChChannel::getName)
                .orderByDesc(ChChannel::getCreateTime).list();
    }


    /**
     * 新增或修改渠道商信息
     */
    @PostMapping("/channel/saveOrUpdate")
    public ChChannel channelSaveOrUpdate(@RequestBody @Validated ChChannel channel) {

        channelService.saveOrUpdate(channel);
        return channel;
    }


    /**
     * 删除渠道商信息
     */
    @GetMapping("/channel/del/{channel}")
    public boolean channelDel(@PathVariable String channel) {

        if (sourceService.lambdaQuery().eq(ChSource::getChannel, channel).exists()) {
            throw new AppException("渠道商还有来源数据");
        }
        if (ringService.lambdaQuery().eq(ChRing::getChannel, channel).exists()) {
            throw new AppException("渠道商还有铃音数据");
        }

        return channelService.removeById(channel);
    }


    /**
     * 来源列表
     */
    @PostMapping("/source/list/{channel}")
    public IPage<ChSource> sourceList(@PathVariable("channel") String channel, @RequestBody CommonPageQuery query) {
        Page<ChSource> page = query.buildPage();
        return sourceService.lambdaQuery()
                .eq(ChSource::getChannel, channel)
                .orderByDesc(ChSource::getCreateTime)
                .page(page);
    }

    /**
     * 来源列表
     */
    @GetMapping("/source/all/{channel}")
    public List<ChSource> sourceAll(@PathVariable("channel") String channel) {
        return sourceService.lambdaQuery()
                .eq(ChSource::getChannel, channel)
                .select(ChSource::getChannel, ChSource::getSource, ChSource::getName)
                .orderByDesc(ChSource::getCreateTime)
                .list();
    }

    /**
     * 新增或修改来源
     */
    @PostMapping("/source/saveOrUpdate")
    public ChSource sourceSaveOrUpdate(@RequestBody @Validated ChSource source) {

        boolean exists = sourceService.lambdaQuery()
                .eq(ChSource::getChannel, source.getChannel())
                .eq(ChSource::getSource, source.getSource())
                .exists();

        if (exists) {
            sourceService.lambdaUpdate()
                    .eq(ChSource::getChannel, source.getChannel())
                    .eq(ChSource::getSource, source.getSource())
                    .update(source);
        } else {
            sourceService.save(source);
        }

        return source;
    }


    /**
     * 删除来源
     */
    @GetMapping("/source/del/{channel}/{source}")
    public boolean sourceDel(@PathVariable("channel") String channel, @PathVariable("source") String source) {

        if (ringService.lambdaQuery().eq(ChRing::getSource, source).exists()) {
            throw new AppException("来源还有铃音数据");
        }

        return sourceService.lambdaUpdate()
                .eq(ChSource::getChannel, channel)
                .eq(ChSource::getSource, source)
                .remove();
    }

    /**
     * 铃音列表
     */
    @PostMapping("/ring/list/{channel}/{source}")
    public IPage<ChRing> ringList(@PathVariable("channel") String channel, @PathVariable("source") String source, @RequestBody CommonPageQuery query) {
        Page<ChRing> page = query.buildPage();
        return ringService.lambdaQuery()
                .eq(ChRing::getSource, source)
                .eq(ChRing::getChannel, channel)
                .orderByDesc(ChRing::getCreateTime)
                .page(page);
    }

    /**
     * 新增或修改铃音
     */
    @PostMapping("/ring/saveOrUpdate")
    public ChRing ringSaveOrUpdate(@RequestBody @Validated ChRing ring) {

        boolean exists = ringService.lambdaQuery()
                .eq(ChRing::getChannel, ring.getChannel())
                .eq(ChRing::getSource, ring.getSource())
                .eq(ChRing::getRingId, ring.getRingId())
                .exists();

        if (exists) {
            ringService.lambdaUpdate()
                    .eq(ChRing::getChannel, ring.getChannel())
                    .eq(ChRing::getSource, ring.getSource())
                    .eq(ChRing::getRingId, ring.getRingId())
                    .update(ring);
        } else {
            ringService.save(ring);
        }
        return ring;
    }

    /**
     * 删除铃音
     */
    @GetMapping("/ring/del/{channel}/{source}/{id}")
    public boolean ringDel(@PathVariable("channel") String channel, @PathVariable("source") String source, @PathVariable("id") String id) {
        return ringService.lambdaUpdate()
                .eq(ChRing::getChannel, channel)
                .eq(ChRing::getSource, source)
                .eq(ChRing::getRingId, id)
                .remove();
    }
}
