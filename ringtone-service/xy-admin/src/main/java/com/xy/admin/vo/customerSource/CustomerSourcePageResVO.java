package com.xy.admin.vo.customerSource;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 客户来源分页查询响应VO
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerSourcePageResVO {
	
	/**
	 * 总记录数
	 */
	private Long total;
	
	/**
	 * 当前页码
	 */
	private Integer pageNo;
	
	/**
	 * 每页大小
	 */
	private Integer pageSize;
	
	/**
	 * 总页数
	 */
	private Integer totalPages;
	
	/**
	 * 数据列表
	 */
	private List<CustomerSourceItemVO> list;
	
	/**
	 * 客户来源条目VO
	 */
	@Data
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class CustomerSourceItemVO {
		/**
		 * 主键ID
		 */
		private Long id;
		
		/**
		 * 手机号
		 */
		private String phone;
		
		/**
		 * 推广商名称
		 */
		private String channelName;
		
		/**
		 * 推广商编码
		 */
		private String channel;
		
		/**
		 * 来源
		 */
		private String source;
		
		/**
		 * APP信息
		 */
		private String app;
		
		/**
		 * 点击ID
		 */
		private String clickid;
		
		/**
		 * 回传状态：0-未回传，1-已回传
		 */
		private Integer huichuanStatus;
		
		/**
		 * 回传状态描述
		 */
		private String huichuanStatusDesc;
		
		/**
		 * 产品信息
		 */
		private String pro;
		
		/**
		 * 平台：ks, dy, llg, csj, gdt
		 */
		private String platform;
		
		/**
		 * 创建时间
		 */
		@JsonFormat(pattern = "yyyy-MM-dd HH:mm")
		private Date createTime;
		
		/**
		 * 是否有来源链路
		 */
		private Boolean hasTrace;
	}
}