package com.xy.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xy.admin.entity.RingtoneTemplate;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xy.admin.vo.RingtoneTemplateVO;
import com.xy.base.starter.dto.CommonPageQuery;

/**
 * 针对表【ringtone_template(铃音视频模板)】的数据库操作Service
 *
 * <AUTHOR>
 * @since 2023-12-26
 */
public interface RingtoneTemplateService extends IService<RingtoneTemplate> {

    /**
     * 带分类的返回
     */
    IPage<RingtoneTemplateVO> listVO(CommonPageQuery query);
}
