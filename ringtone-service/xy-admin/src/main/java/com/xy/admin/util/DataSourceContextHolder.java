package com.xy.admin.util;

/**
 * 数据源上下文工具类
 * 用于手动切换数据源
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
public class DataSourceContextHolder {

  private static final ThreadLocal<String> CONTEXT_HOLDER = new ThreadLocal<>();

  /**
   * 设置数据源
   */
  public static void setDataSource(String dataSource) {
    CONTEXT_HOLDER.set(dataSource);
  }

  /**
   * 获取数据源
   */
  public static String getDataSource() {
    return CONTEXT_HOLDER.get();
  }

  /**
   * 清除数据源
   */
  public static void clearDataSource() {
    CONTEXT_HOLDER.remove();
  }
}