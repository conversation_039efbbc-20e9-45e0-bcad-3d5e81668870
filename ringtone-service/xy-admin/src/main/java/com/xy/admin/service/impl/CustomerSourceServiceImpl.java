package com.xy.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xy.admin.annotation.DataSource;
import com.xy.admin.vo.customerSource.CustomerSourceQueryReqVO;
import com.xy.admin.vo.customerSource.CustomerSourcePageReqVO;
import com.xy.admin.vo.customerSource.CustomerSourcePageResVO;
import com.xy.admin.entity.AdRecommendRecord;
import com.xy.admin.entity.ChannelReport;
import com.xy.admin.entity.ChChannel;
import com.xy.admin.entity.ProOrder;
import com.xy.admin.mapper.AdRecommendRecordMapper;
import com.xy.admin.mapper.ChannelReportMapper;
import com.xy.admin.mapper.ChChannelMapper;
import com.xy.admin.mapper.ProOrderMapper;
import com.xy.admin.service.CustomerSourceService;
import com.xy.admin.vo.customerSource.CustomerSourceResVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 客户来源查询服务实现类
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CustomerSourceServiceImpl implements CustomerSourceService {
	
	private final ChannelReportMapper channelReportMapper;
	private final ChChannelMapper chChannelMapper;
	private final ProOrderMapper proOrderMapper;
	private final AdRecommendRecordMapper adRecommendRecordMapper;
	
	@Override
	public CustomerSourceResVO queryCustomerSource(CustomerSourceQueryReqVO queryDTO) {
		String phone = queryDTO.getPhone();
		String pro = queryDTO.getPro();
		// 根据项目类型查询用户信息
		CustomerSourceResVO result = queryUserFrom(phone, pro);
		if (result == null) {
			return null;
		}
		
		// 获取渠道信息
		Map<String, String> channelMap = getChannelMap();
		
		// 递归查询来源链路
		List<CustomerSourceResVO.CustomerSourceTraceVO> traceList = new ArrayList<>();
		querySourceTrace(result, channelMap, traceList);
		
		result.setTraceList(traceList);
		return result;
	}
	
	@Override
	public CustomerSourcePageResVO pageQueryCustomerSource(CustomerSourcePageReqVO pageReqVO) {
		log.info("开始分页查询客户来源信息，参数：{}", pageReqVO);
		
		String pro = pageReqVO.getPro();
		if (StringUtils.hasText(pro)) {
			if ("zsgl".equals(pro)) {
				return pageQueryFromPrimaryDataSource(pageReqVO);
			} else if ("nmch".equals(pro)) {
				return pageQueryFromSecondaryDataSource(pageReqVO);
			} else if ("other".equals(pro)) {
				return pageQueryFromTertiaryDataSource(pageReqVO);
			}
		}
		
		// 如果没有指定项目类型，默认查询zsgl项目
		return pageQueryFromPrimaryDataSource(pageReqVO);
	}
	
	/**
	 * 根据手机号和项目类型查询用户信息
	 */
	private CustomerSourceResVO queryUserFrom(String phone, String pro) {
		try {
			if ("zsgl".equals(pro)) {
				return queryFromPrimaryDataSource(phone);
			} else if ("nmch".equals(pro)) {
				return queryFromSecondaryDataSource(phone);
			} else {
				return queryFromTertiaryDataSource(phone);
			}
		} catch (Exception e) {
			log.error("查询用户来源信息失败，手机号：{}，项目类型：{}", phone, pro, e);
			return null;
		}
	}
	
	/**
	 * 从主数据源查询（zsgl项目）
	 */
	@DataSource("primary")
	public CustomerSourceResVO queryFromPrimaryDataSource(String phone) {
		LambdaQueryWrapper<ChannelReport> wrapper = new LambdaQueryWrapper<ChannelReport>()
				.eq(ChannelReport::getPhone, phone)
				.orderByDesc(ChannelReport::getReportStatus)
				.last("LIMIT 1");
		
		ChannelReport channelReport = channelReportMapper.selectOne(wrapper);
		if (channelReport == null) {
			return null;
		}
		
		return convertToCustomerSourceVO(channelReport);
	}
	
	/**
	 * 从次要数据源查询（nmch项目）
	 */
	@DataSource("secondary")
	public CustomerSourceResVO queryFromSecondaryDataSource(String phone) {
		LambdaQueryWrapper<ChannelReport> wrapper = new LambdaQueryWrapper<ChannelReport>()
				.eq(ChannelReport::getPhone, phone)
				.orderByDesc(ChannelReport::getReportStatus)
				.last("LIMIT 1");
		
		ChannelReport channelReport = channelReportMapper.selectOne(wrapper);
		if (channelReport == null) {
			return null;
		}
		
		return convertToCustomerSourceVO(channelReport);
	}
	
	/**
	 * 从第三数据源查询（other项目）
	 */
	@DataSource("tertiary")
	public CustomerSourceResVO queryFromTertiaryDataSource(String phone) {
		
		LambdaQueryWrapper<ProOrder> wrapper = new LambdaQueryWrapper<ProOrder>()
				.eq(ProOrder::getPhone, phone)
				.orderByDesc(ProOrder::getReportStatus)
				.last("LIMIT 1");
		
		ProOrder proOrder = proOrderMapper.selectOne(wrapper);
		if (proOrder == null) {
			return null;
		}
		return convertToCustomerSourceVO(proOrder);
	}
	
	/**
	 * 获取渠道映射
	 */
	@DataSource("primary")
	public Map<String, String> getChannelMap() {
		List<ChChannel> channels = chChannelMapper.selectList(null);
		Map<String, String> channelMap = new HashMap<>();
		for (ChChannel channel : channels) {
			channelMap.put(channel.getChannel(), channel.getName());
		}
		return channelMap;
	}
	
	/**
	 * 递归查询来源链路
	 */
	private void querySourceTrace(CustomerSourceResVO sourceVO, Map<String, String> channelMap,
	                              List<CustomerSourceResVO.CustomerSourceTraceVO> traceList) {
		
		String callback = sourceVO.getClickid();
		if (!StringUtils.hasText(callback) || !callback.startsWith("CK")) {
			return;
		}
		
		// 查询推荐记录
		AdRecommendRecord recommendRecord = queryRecommendRecord(callback);
		if (recommendRecord == null) {
			return;
		}
		
		// 解析URL获取参数
		Map<String, String> urlParams = parseUrl(recommendRecord.getRawUrl());
		
		CustomerSourceResVO.CustomerSourceTraceVO trace = CustomerSourceResVO.CustomerSourceTraceVO.builder()
				.phone(recommendRecord.getPhone())
				.channelName(channelMap.get(urlParams.get("channel")))
				.source(urlParams.get("source"))
				.app(recommendRecord.getUa())
				.clickid(urlParams.get("clickid"))
				.createTime(recommendRecord.getCreateTime())
				.rawUrl(recommendRecord.getRawUrl())
				.isFinal(false)
				.build();
		
		traceList.add(trace);
		
		// 继续递归查询
		if (StringUtils.hasText(trace.getClickid()) && trace.getClickid().startsWith("CK")) {
			querySourceTrace(CustomerSourceResVO.builder()
					.clickid(trace.getClickid())
					.build(), channelMap, traceList);
		} else {
			// 标记为最终来源
			trace.setIsFinal(true);
		}
	}
	
	/**
	 * 查询推荐记录
	 */
	@DataSource("fourth")
	public AdRecommendRecord queryRecommendRecord(String id) {
		return adRecommendRecordMapper.selectById(id);
	}
	
	/**
	 * 解析URL参数
	 */
	private Map<String, String> parseUrl(String urlString) {
		Map<String, String> result = new HashMap<>();
		try {
			URL url = new URL(urlString);
			String query = url.getQuery();
			if (query != null) {
				String[] pairs = query.split("&");
				for (String pair : pairs) {
					String[] keyValue = pair.split("=", 2);
					if (keyValue.length == 2) {
						String key = URLDecoder.decode(keyValue[0], StandardCharsets.UTF_8.name());
						String value = URLDecoder.decode(keyValue[1], StandardCharsets.UTF_8.name());
						result.put(key, value);
					}
				}
			}
			
			// 提取channel、source、clickid
			String channel = result.get("channel");
			String source = result.get("source");
			String clickid = null;
			
			if (result.containsKey("clickid")) {
				clickid = result.get("clickid");
			} else if (result.containsKey("clickidname")) {
				String clickidName = result.get("clickidname");
				clickid = result.get(clickidName);
			} else {
				clickid = result.get("llgclickid");
			}
			
			Map<String, String> finalResult = new HashMap<>();
			finalResult.put("channel", channel);
			finalResult.put("source", source);
			finalResult.put("clickid", clickid);
			return finalResult;
			
		} catch (Exception e) {
			log.error("解析URL失败：{}", urlString, e);
			return result;
		}
	}
	
	/**
	 * 转换ChannelReport为CustomerSourceVO
	 */
	private CustomerSourceResVO convertToCustomerSourceVO(ChannelReport channelReport) {
		Map<String, String> channelMap = getChannelMap();
		
		return CustomerSourceResVO.builder()
				.phone(channelReport.getPhone())
				.channel(channelReport.getChannel())
				.channelName(channelMap.get(channelReport.getChannel()))
				.source(channelReport.getSource())
				.app(channelReport.getParams())
				.clickid(channelReport.getCallback())
				.huichuanStatus(channelReport.getHuichuanStatus())
				.pro(channelReport.getPro())
				.platform(channelReport.getPlatform())
				.createTime(channelReport.getCreateTime())
				.rawUrl(channelReport.getUrl())
				.build();
	}
	
	/**
	 * 转换ProOrder为CustomerSourceVO
	 */
	private CustomerSourceResVO convertToCustomerSourceVO(ProOrder proOrder) {
		Map<String, String> channelMap = getChannelMap();
		
		return CustomerSourceResVO.builder()
				.phone(proOrder.getPhone())
				.channel(proOrder.getChannel())
				.channelName(channelMap.get(proOrder.getChannel()))
				.source(proOrder.getSource())
				.app(proOrder.getUa())
				.clickid(proOrder.getCallback())
				.huichuanStatus(proOrder.getHuichuanStatus())
				.pro(proOrder.getPro())
				.platform(proOrder.getPlatform())
				.createTime(proOrder.getCreateTime())
				.rawUrl(proOrder.getUrl())
				.build();
	}
	
	/**
	 * 从主数据源分页查询（zsgl项目）
	 */
	@DataSource("primary")
	private CustomerSourcePageResVO pageQueryFromPrimaryDataSource(CustomerSourcePageReqVO pageReqVO) {
		// 构建查询条件
		LambdaQueryWrapper<ChannelReport> wrapper = buildChannelReportWrapper(pageReqVO);
		
		// 分页查询
		Page<ChannelReport> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
		IPage<ChannelReport> pageResult = channelReportMapper.selectPage(page, wrapper);
		
		// 转换结果
		return convertToPageResVO(pageResult, pageReqVO);
	}
	
	/**
	 * 从次要数据源分页查询（nmch项目）
	 */
	@DataSource("secondary")
	private CustomerSourcePageResVO pageQueryFromSecondaryDataSource(CustomerSourcePageReqVO pageReqVO) {
		// 构建查询条件
		LambdaQueryWrapper<ChannelReport> wrapper = buildChannelReportWrapper(pageReqVO);
		
		// 分页查询
		Page<ChannelReport> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
		IPage<ChannelReport> pageResult = channelReportMapper.selectPage(page, wrapper);
		
		// 转换结果
		return convertToPageResVO(pageResult, pageReqVO);
	}
	
	/**
	 * 从第三数据源分页查询（other项目）
	 */
	@DataSource("tertiary")
	private CustomerSourcePageResVO pageQueryFromTertiaryDataSource(CustomerSourcePageReqVO pageReqVO) {
		// 构建查询条件
		LambdaQueryWrapper<ProOrder> wrapper = buildProOrderWrapper(pageReqVO);
		
		// 分页查询
		Page<ProOrder> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
		IPage<ProOrder> pageResult = proOrderMapper.selectPage(page, wrapper);
		
		// 转换结果
		return convertProOrderToPageResVO(pageResult, pageReqVO);
	}
	
	/**
	 * 构建ChannelReport查询条件
	 */
	private LambdaQueryWrapper<ChannelReport> buildChannelReportWrapper(CustomerSourcePageReqVO pageReqVO) {
		LambdaQueryWrapper<ChannelReport> wrapper = new LambdaQueryWrapper<>();
		
		// 项目类型查询
		if (StringUtils.hasText(pageReqVO.getPro())) {
			wrapper.eq(ChannelReport::getPro, pageReqVO.getPro());
		}
		
		// 手机号模糊查询
		if (StringUtils.hasText(pageReqVO.getPhone())) {
			wrapper.like(ChannelReport::getPhone, pageReqVO.getPhone());
		}
		
		// 渠道查询
		if (StringUtils.hasText(pageReqVO.getChannel())) {
			wrapper.eq(ChannelReport::getChannel, pageReqVO.getChannel());
		}
		
		// 来源查询
		if (StringUtils.hasText(pageReqVO.getSource())) {
			wrapper.eq(ChannelReport::getSource, pageReqVO.getSource());
		}
		
		// 回传状态查询
		if (pageReqVO.getHuichuanStatus() != null) {
			wrapper.eq(ChannelReport::getHuichuanStatus, pageReqVO.getHuichuanStatus());
		}
		
		// 平台查询
		if (StringUtils.hasText(pageReqVO.getPlatform())) {
			wrapper.eq(ChannelReport::getPlatform, pageReqVO.getPlatform());
		}
		
		// 时间范围查询
		if (pageReqVO.getStartTime() != null) {
			wrapper.ge(ChannelReport::getCreateTime, pageReqVO.getStartTime());
		}
		if (pageReqVO.getEndTime() != null) {
			wrapper.le(ChannelReport::getCreateTime, pageReqVO.getEndTime());
		}
		
		// 排序
		if ("desc".equalsIgnoreCase(pageReqVO.getSortOrder())) {
			if ("phone".equals(pageReqVO.getSortField())) {
				wrapper.orderByDesc(ChannelReport::getPhone);
			} else if ("channel".equals(pageReqVO.getSortField())) {
				wrapper.orderByDesc(ChannelReport::getChannel);
			} else {
				wrapper.orderByDesc(ChannelReport::getCreateTime);
			}
		} else {
			if ("phone".equals(pageReqVO.getSortField())) {
				wrapper.orderByAsc(ChannelReport::getPhone);
			} else if ("channel".equals(pageReqVO.getSortField())) {
				wrapper.orderByAsc(ChannelReport::getChannel);
			} else {
				wrapper.orderByAsc(ChannelReport::getCreateTime);
			}
		}
		
		return wrapper;
	}
	
	/**
	 * 构建ProOrder查询条件
	 */
	private LambdaQueryWrapper<ProOrder> buildProOrderWrapper(CustomerSourcePageReqVO pageReqVO) {
		LambdaQueryWrapper<ProOrder> wrapper = new LambdaQueryWrapper<>();
		
		// 手机号模糊查询
		if (StringUtils.hasText(pageReqVO.getPhone())) {
			wrapper.like(ProOrder::getPhone, pageReqVO.getPhone());
		}
		
		// 渠道查询
		if (StringUtils.hasText(pageReqVO.getChannel())) {
			wrapper.eq(ProOrder::getChannel, pageReqVO.getChannel());
		}
		
		// 来源查询
		if (StringUtils.hasText(pageReqVO.getSource())) {
			wrapper.eq(ProOrder::getSource, pageReqVO.getSource());
		}
		
		// 回传状态查询
		if (pageReqVO.getHuichuanStatus() != null) {
			wrapper.eq(ProOrder::getHuichuanStatus, pageReqVO.getHuichuanStatus());
		}
		
		// 平台查询
		if (StringUtils.hasText(pageReqVO.getPlatform())) {
			wrapper.eq(ProOrder::getPlatform, pageReqVO.getPlatform());
		}
		
		// 时间范围查询
		if (pageReqVO.getStartTime() != null) {
			wrapper.ge(ProOrder::getCreateTime, pageReqVO.getStartTime());
		}
		if (pageReqVO.getEndTime() != null) {
			wrapper.le(ProOrder::getCreateTime, pageReqVO.getEndTime());
		}
		
		// 排序
		if ("desc".equalsIgnoreCase(pageReqVO.getSortOrder())) {
			if ("phone".equals(pageReqVO.getSortField())) {
				wrapper.orderByDesc(ProOrder::getPhone);
			} else if ("channel".equals(pageReqVO.getSortField())) {
				wrapper.orderByDesc(ProOrder::getChannel);
			} else {
				wrapper.orderByDesc(ProOrder::getCreateTime);
			}
		} else {
			if ("phone".equals(pageReqVO.getSortField())) {
				wrapper.orderByAsc(ProOrder::getPhone);
			} else if ("channel".equals(pageReqVO.getSortField())) {
				wrapper.orderByAsc(ProOrder::getChannel);
			} else {
				wrapper.orderByAsc(ProOrder::getCreateTime);
			}
		}
		
		return wrapper;
	}
	
	/**
	 * 转换ChannelReport分页结果
	 */
	private CustomerSourcePageResVO convertToPageResVO(IPage<ChannelReport> pageResult,
	                                                   CustomerSourcePageReqVO pageReqVO) {
		Map<String, String> channelMap = getChannelMap();
		
		List<CustomerSourcePageResVO.CustomerSourceItemVO> list = pageResult.getRecords().stream()
				.map(channelReport -> CustomerSourcePageResVO.CustomerSourceItemVO.builder()
						.id(channelReport.getId())
						.phone(channelReport.getPhone())
						.channel(channelReport.getChannel())
						.channelName(channelMap.get(channelReport.getChannel()))
						.source(channelReport.getSource())
						.app(channelReport.getParams())
						.clickid(channelReport.getCallback())
						.huichuanStatus(channelReport.getHuichuanStatus())
						.huichuanStatusDesc(channelReport.getHuichuanStatus() == 1 ? "已回传" : "未回传")
						.pro(channelReport.getPro())
						.platform(channelReport.getPlatform())
						.createTime(channelReport.getCreateTime())
						.hasTrace(StringUtils.hasText(channelReport.getCallback()) &&
								channelReport.getCallback().startsWith("CK"))
						.build())
				.collect(java.util.stream.Collectors.toList());
		
		return CustomerSourcePageResVO.builder()
				.total(pageResult.getTotal())
				.pageNo(pageReqVO.getPageNo())
				.pageSize(pageReqVO.getPageSize())
				.totalPages((int) Math.ceil((double) pageResult.getTotal() / pageReqVO.getPageSize()))
				.list(list)
				.build();
	}
	
	/**
	 * 转换ProOrder分页结果
	 */
	private CustomerSourcePageResVO convertProOrderToPageResVO(IPage<ProOrder> pageResult,
	                                                           CustomerSourcePageReqVO pageReqVO) {
		Map<String, String> channelMap = getChannelMap();
		
		List<CustomerSourcePageResVO.CustomerSourceItemVO> list = pageResult.getRecords().stream()
				.map(proOrder -> CustomerSourcePageResVO.CustomerSourceItemVO.builder()
						.id(proOrder.getId())
						.phone(proOrder.getPhone())
						.channel(proOrder.getChannel())
						.channelName(channelMap.get(proOrder.getChannel()))
						.source(proOrder.getSource())
						.app(proOrder.getUa())
						.clickid(proOrder.getCallback())
						.huichuanStatus(proOrder.getHuichuanStatus())
						.huichuanStatusDesc(proOrder.getHuichuanStatus() == 1 ? "已回传" : "未回传")
						.pro(proOrder.getPro())
						.platform(proOrder.getPlatform())
						.createTime(proOrder.getCreateTime())
						.hasTrace(StringUtils.hasText(proOrder.getCallback()) &&
								proOrder.getCallback().startsWith("CK"))
						.build())
				.collect(java.util.stream.Collectors.toList());
		
		return CustomerSourcePageResVO.builder()
				.total(pageResult.getTotal())
				.pageNo(pageReqVO.getPageNo())
				.pageSize(pageReqVO.getPageSize())
				.totalPages((int) Math.ceil((double) pageResult.getTotal() / pageReqVO.getPageSize()))
				.list(list)
				.build();
	}
}