package com.xy.admin.controller;

import com.xy.admin.vo.customerSource.CustomerSourceQueryReqVO;
import com.xy.admin.vo.customerSource.CustomerSourcePageReqVO;
import com.xy.admin.vo.customerSource.CustomerSourcePageResVO;
import com.xy.admin.entity.ChChannel;
import com.xy.admin.service.ChChannelService;
import com.xy.admin.service.CustomerSourceService;
import com.xy.admin.vo.customerSource.CustomerSourceResVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户来源查询控制器
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@RestController
@RequestMapping("/customer-source")
@Slf4j
@RequiredArgsConstructor
public class CustomerSourceController {

	private final CustomerSourceService customerSourceService;
	private final ChChannelService channelService;

	/**
	 * 查询pro
	 * 
	 * @return pro列表
	 */
	@GetMapping("/query/pro")
	public List<ChChannel> queryPro() {
		return channelService.getBaseMapper().selectList(null);
	}

	/**
	 * 查询客户来源信息
	 *
	 * @param queryDTO 查询参数
	 * @return 客户来源信息
	 */
	@PostMapping("/query")
	public CustomerSourceResVO queryCustomerSource(@RequestBody @Validated CustomerSourceQueryReqVO queryDTO) {
		log.info("收到客户来源查询请求：{}", queryDTO);

		CustomerSourceResVO result = customerSourceService.queryCustomerSource(queryDTO);

		if (result == null) {
			log.info("未找到客户来源信息，手机号：{}", queryDTO.getPhone());
		} else {
			log.info("查询客户来源信息成功，手机号：{}，推广商：{}", result.getPhone(), result.getChannelName());
		}

		return result;
	}

	/**
	 * 分页查询客户来源信息
	 *
	 * @param pageReqVO 分页查询参数
	 * @return 分页查询结果
	 */
	@PostMapping("/page")
	public CustomerSourcePageResVO pageQueryCustomerSource(@RequestBody @Validated CustomerSourcePageReqVO pageReqVO) {
		log.info("收到客户来源分页查询请求：{}", pageReqVO);

		CustomerSourcePageResVO result = customerSourceService.pageQueryCustomerSource(pageReqVO);

		log.info("分页查询客户来源信息完成，总记录数：{}，当前页：{}",
				result.getTotal(), result.getPageNo());

		return result;
	}

	/**
	 * 根据手机号查询客户来源信息（GET方式）
	 *
	 * @param phone 手机号
	 * @param pro   项目类型，默认zsgl
	 * @return 客户来源信息
	 */
	@GetMapping("/query/{phone}")
	public CustomerSourceResVO queryCustomerSourceByPhone(
			@PathVariable String phone,
			@RequestParam(value = "pro", defaultValue = "zsgl") String pro) {

		CustomerSourceQueryReqVO queryDTO = new CustomerSourceQueryReqVO();
		queryDTO.setPhone(phone);
		queryDTO.setPro(pro);

		log.info("收到客户来源查询请求（GET）：手机号={}，项目类型={}", phone, pro);

		CustomerSourceResVO result = customerSourceService.queryCustomerSource(queryDTO);

		if (result == null) {
			log.info("未找到客户来源信息，手机号：{}", phone);
		} else {
			log.info("查询客户来源信息成功，手机号：{}，推广商：{}",
					result.getPhone(), result.getChannelName());
		}

		return result;
	}

}