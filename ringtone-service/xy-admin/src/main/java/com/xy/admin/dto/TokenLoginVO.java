package com.xy.admin.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/7/1
 */

@Data
public class TokenLoginVO implements Serializable {

    /**
     *
     */
    @TableId
    private Long id;

    /**
     *
     */
    private Integer type;
    /**
     *
     */
    private String phone;
    /**
     *
     */
    private String province;
    /**
     *
     */
    private String content;
    /**
     *
     */
    private String response;


    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
