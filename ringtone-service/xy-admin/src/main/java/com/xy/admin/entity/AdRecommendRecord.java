package com.xy.admin.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 广告推荐记录表
 *
 * <AUTHOR>
 * @since 2025/06/26
 * @TableName 【traffic库】ad_recommend_record
 */
@TableName(value = "ad_recommend_record")
@Data
public class AdRecommendRecord implements Serializable {
  /**
   * 记录id,也是clickid
   */
  @TableId
  private String id;

  /**
   * 租户ID
   */
  private String tenantId;

  /**
   * 消费这个的ssp id
   */
  private String sspTenantId;

  /**
   * ssp广告位id
   */
  private Long sspAdId;

  /**
   * ssp场景
   */
  private String sspScene;

  /**
   * 手机号
   */
  private String phone;

  /**
   * 省份
   */
  private String province;

  /**
   * 城市
   */
  private String city;

  /**
   * 广告ID
   */
  private Long adId;

  /**
   * 项目ID
   */
  private Long projectId;

  /**
   * 广告出价
   */
  private BigDecimal bid;

  /**
   * 广告连接
   */
  private String link;

  /**
   * 是否转换激活
   */
  private Boolean active;

  /**
   * UserAgent
   */
  private String ua;

  /**
   * url原始地址，来源地址（完整地址包含域名和参数）
   */
  private String rawUrl;

  /**
   * 激活时间
   */
  private Date activeTime;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 原链接zssource
   */
  private String zssource;

  /**
   * 原链接zslevel
   */
  private String zslevel;

  /**
   * 原链接clickid
   */
  private String clickid;

  @TableField(exist = false)
  private static final long serialVersionUID = 1L;
}