package com.xy.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xy.admin.dto.TokenLoginVO;
import com.xy.admin.entity.LogApi;
import com.xy.admin.entity.LogSys;
import com.xy.admin.entity.PhoneLac;
import com.xy.admin.service.LogApiService;
import com.xy.admin.service.LogSysService;
import com.xy.admin.service.PhoneLacService;
import com.xy.base.starter.dto.CommonPageQuery;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;

/**
 * 日志管理
 * <AUTHOR>
 * @since 2023/11/21 13:45
 */
@RestController
@RequestMapping("/log")
@RequiredArgsConstructor
public class LogController {

    private final LogSysService logSysService;
    private final LogApiService logApiService;
    private final PhoneLacService phoneLacService;

    /**
     * api日志列表，需要权限 log:api:list
     */
    @PreAuthorize("hasAuthority('log:api:list')")
    @PostMapping("/apis/list")
    public IPage<LogApi> apiList(@RequestBody CommonPageQuery query) {
        Page<LogApi> page = query.buildPage();
        return logApiService.lambdaQuery()
                .eq(query.getType() != null, LogApi::getType, query.getType())
                .orderByDesc(LogApi::getCreateTime)
                .page(page);
    }

    /**
     * 系统日志列表，需要权限 log:sys:list
     */
    @PreAuthorize("hasAuthority('log:api:list')")
    @PostMapping("/sys/list")
    public IPage<LogSys> sysList(@RequestBody CommonPageQuery query) {
        Page<LogSys> page = query.buildPage();
        return logSysService.lambdaQuery()
                .orderByDesc(LogSys::getId)
                .page(page);
    }

    @PostMapping("/sys/tokenLoginList")
    public IPage<TokenLoginVO> tokenLoginList(@RequestBody CommonPageQuery query) {
        Page<LogApi> page = query.buildPage();
        String province;
        if (StringUtils.hasText(query.getKeyword()) && query.getKeyword().length() == 11) {

            String phoneSeg = query.getKeyword().substring(0, 7);
            PhoneLac phoneLac = phoneLacService.getById(phoneSeg);
            if (phoneLac != null) {
                province = phoneLac.getProvince();
            } else {
                province = "";
            }

            logApiService.lambdaQuery()
                    .eq(LogApi::getType, 0)
                    .like(LogApi::getUrl, query.getKeyword())
                    .orderByDesc(LogApi::getCreateTime)
                    .page(page);


        } else {
            province = "";
            page.setTotal(0);
            page.setCurrent(1);
            page.setSize(10L);
            page.setRecords(new ArrayList<>());
        }


        return page.convert(l->{
            String sig = DigestUtils.md5DigestAsHex((query.getKeyword()+l.getCreateTime()).getBytes(StandardCharsets.UTF_8)).toUpperCase();
            TokenLoginVO vo = new TokenLoginVO();
            vo.setId(l.getId());
            vo.setType(l.getType());
            vo.setPhone(query.getKeyword());
            vo.setProvince(province);
            vo.setContent("signature=" + sig);
            vo.setResponse(sig);
            vo.setCreateTime(l.getCreateTime());
            return vo;
        });
    }

}
