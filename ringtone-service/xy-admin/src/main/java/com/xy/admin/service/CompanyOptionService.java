package com.xy.admin.service;

import com.xy.admin.vo.common.CompanyOptionVO;

import java.util.List;

/**
 * 公司选项服务接口
 * 
 * <AUTHOR>
 * @since 2025/7/18
 */
public interface CompanyOptionService {
    
    /**
     * 获取所有公司选项
     * 优先从Redis缓存获取，如果缓存不存在则从数据库查询并缓存
     * 
     * @return 公司选项列表
     */
    List<CompanyOptionVO> getAllCompanyOptions();
    
    /**
     * 刷新公司选项缓存
     * 清除Redis缓存，强制从数据库重新查询
     * 
     * @return 刷新后的公司选项列表
     */
    List<CompanyOptionVO> refreshCompanyOptions();
    
    /**
     * 清除公司选项缓存
     */
    void clearCompanyOptionsCache();
}
