package com.xy.admin.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @since 2025/7/18 14:21
 */
@Data
@Accessors(chain = true)
@SuperBuilder
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "phone_lac")
public class PhoneLac implements Serializable {
		private static final long serialVersionUID = 1L;
		
		@TableId(value = "seg", type = IdType.INPUT)
		@Size(max = 7, message = "最大长度要小于 7")
		@NotBlank(message = "不能为空")
		private String seg;
		
		@TableField(value = "province")
		@Size(max = 25, message = "最大长度要小于 25")
		private String province;
		
		@TableField(value = "city")
		@Size(max = 55, message = "最大长度要小于 55")
		private String city;
		
		@TableField(value = "carrier")
		@Size(max = 25, message = "最大长度要小于 25")
		private String carrier;
		
		/**
		 * 虚拟运营商
		 */
		@TableField(value = "vcarrier")
		@Size(max = 25, message = "虚拟运营商最大长度要小于 25")
		private String vcarrier;
		
		@TableField(value = "create_time", fill = FieldFill.INSERT)
		@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
		private Date createTime;
		
		@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
		@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
		private Date updateTime;
}