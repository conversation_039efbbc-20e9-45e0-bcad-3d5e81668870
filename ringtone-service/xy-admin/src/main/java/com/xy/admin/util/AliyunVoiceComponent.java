package com.xy.admin.util;

import com.alibaba.nls.client.AccessToken;
import com.alibaba.nls.client.protocol.NlsClient;
import com.alibaba.nls.client.protocol.OutputFormatEnum;
import com.alibaba.nls.client.protocol.SampleRateEnum;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizer;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizerListener;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizerResponse;
import com.xy.base.core.exception.AppException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.function.Consumer;

@Component
@Slf4j
public class AliyunVoiceComponent {

    @Value("${oss.ak}")
    private String ak;

    @Value("${oss.secret}")
    private String secret;

    @Value("${oss.voiceKey}")
    private String voiceKey;


    private long expireTime = 0;

    private String token = null;

    private void init() {

        if (StringUtils.isEmpty(token) || expireTime < (System.currentTimeMillis() / 1000)) {
            AccessToken accessToken = new AccessToken(ak, secret);
            try {
                accessToken.apply();
                token = accessToken.getToken();
                expireTime = accessToken.getExpireTime();
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 合成音频
     *
     * @param text       文本
     * @param targetFile 合成的文件地址
     */
    public void speechLongSynthesizer(String text, String voice, String targetFile, Consumer<String> consumer) {

        init();

        NlsClient client = new NlsClient(token);
        SpeechSynthesizer synthesizer = null;
        try {
            //创建实例，建立连接。
            synthesizer = new SpeechSynthesizer(client, getSynthesizerListener(targetFile, consumer));
            synthesizer.setAppKey(voiceKey);
            //设置返回音频的编码格式。
            synthesizer.setFormat(OutputFormatEnum.MP3);
            //设置返回音频的采样率。
            synthesizer.setSampleRate(SampleRateEnum.SAMPLE_RATE_16K);
            //发音人
            //zhida zhiyue zhinan zhiru zhitian_emo
            synthesizer.setVoice(voice);
            //语调，范围是-500~500，可选，默认是0。
            synthesizer.setPitchRate(0);
            //语速，范围是-500~500，默认是0。
            synthesizer.setSpeechRate(0);
            //设置用于语音合成的文本
            // 此处调用的是setLongText接口（原语音合成接口是setText）。
            synthesizer.setText(text);
            //此方法将以上参数设置序列化为JSON发送给服务端，并等待服务端确认。
            long start = System.currentTimeMillis();
            synthesizer.start();
            log.info("tts start latency " + (System.currentTimeMillis() - start) + " ms");
            //等待语音合成结束
            synthesizer.waitForComplete();
            log.info("tts stop latency " + (System.currentTimeMillis() - start) + " ms");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new AppException("合成语音失败");
        } finally {
            //关闭连接
            if (null != synthesizer) {
                synthesizer.close();
            }
        }
        client.shutdown();
    }

    private SpeechSynthesizerListener getSynthesizerListener(String targetFile, Consumer<String> consume) throws FileNotFoundException {


        return new SpeechSynthesizerListener() {

            final File f = new File(targetFile);
            final FileOutputStream fout = new FileOutputStream(f);
            private boolean firstRecvBinary = true;

            //语音合成结束
            @Override
            public void onComplete(SpeechSynthesizerResponse response) {
                try {
                    fout.close();
                    consume.accept(targetFile);
                } catch (IOException e) {
                    e.printStackTrace();
                }
                // 调用onComplete时，表示所有TTS数据已经接收完成，因此为整个合成数据的延迟。该延迟可能较大，不一定满足实时场景。
                System.out.println("name: " + response.getName() + ", status: " + response.getStatus() + ", output file :" + f.getAbsolutePath());
            }

            //语音合成的语音二进制数据
            @Override
            public void onMessage(ByteBuffer message) {
                try {
                    if (firstRecvBinary) {
                        // 此处计算首包语音流的延迟，收到第一包语音流时，即可以进行语音播放，以提升响应速度（特别是实时交互场景下）。
                        firstRecvBinary = false;
                        long now = System.currentTimeMillis();
                    }
                    byte[] bytesArray = new byte[message.remaining()];
                    message.get(bytesArray, 0, bytesArray.length);
                    //System.out.println("write array:" + bytesArray.length);
                    fout.write(bytesArray);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFail(SpeechSynthesizerResponse response) {
                try {
                    fout.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
                // task_id是调用方和服务端通信的唯一标识，当遇到问题时，需要提供此task_id以便排查。
                System.out.println(
                        "task_id: " + response.getTaskId() +
                                //状态码
                                ", status: " + response.getStatus() +
                                //错误信息
                                ", status_text: " + response.getStatusText());
            }
        };
    }


}
