package com.xy.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 模板分类
 *
 * <AUTHOR>
 * @since 2023-12-26
 */
@TableName(value = "ringtone_category")
@Data
public class RingtoneCategory implements Serializable {

    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 0为根目录
     */
    private Integer pid;

    /**
     *
     */
    @NotNull
    @Size(max = 5, min = 1, message = "名称必须大于等于1或小于等于5")
    private String name;

    /**
     * 排序
     */
    private Integer sort;
    /**
     *
     */
    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
