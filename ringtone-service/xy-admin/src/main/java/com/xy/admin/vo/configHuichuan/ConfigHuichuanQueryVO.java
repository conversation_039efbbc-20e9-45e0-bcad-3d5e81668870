package com.xy.admin.vo.configHuichuan;

import com.xy.base.starter.dto.CommonPageQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 回传配置查询VO
 * <AUTHOR>
 * @since 2025/7/28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ConfigHuichuanQueryVO extends CommonPageQuery {
    private static final long serialVersionUID = 1L;
    
    /**
     * 渠道
     */
    private String channel;
    
    /**
     * 来源
     */
    private String source;
    
    /**
     * 参数名
     */
    private String paramName;
    
    /**
     * 平台：ks, dy, llg, csj, gdt
     */
    private String platform;
    
    /**
     * 0延时回传，1立即回传
     */
    private Integer nodelay;
}
