package com.xy.admin.vo.proInfoProduct;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * 产品推广信息VO
 * <AUTHOR>
 * @since 2025/7/17 16:48
 */
@Data
@Accessors(chain = true)
@SuperBuilder
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProInfoProductVO implements Serializable {
		private static final long serialVersionUID = 1L;
		
		/**
		 * ID
		 */
		private Integer id;
		
		/**
		 * 公司
		 */
		private String company;

		/**
		 * 代理商：zsgl, sqs
		 */
		private String agent;

		/**
		 * 产品ID
		 */
		private String product;

		/**
		 * pro编号
		 */
		private String pro;

		/**
		 * 运营商
		 */
		private String carrier;

		/**
		 * 产品编号
		 */
		private String productNo;

		/**
		 * 产品名称
		 */
		private String productName;
		
		/**
		 * 产品状态: 1-待接入 2-已暂停 3-已下线 4-可推广
		 */
		private Byte productStatus;
		
		/**
		 * 产品价格
		 */
		private String productPrice;

		/**
		 * 推广省份
		 */
		private String promotionProvince;

		/**
		 * 业务要求
		 */
		private String businessRequirements;

		/**
		 * 推广价格
		 */
		private String promotionPrice;

		/**
		 * 请求参数
		 */
		private String params;
		
		/**
		 * 开发对接状态：1-排期中 2-计划中 3-开发中 4-测试中 5-等页面 6-审核中 7-联调中 8-等修复 9-待测试 10-已上线
		 */
		private Byte developmentStatus;
		
		/**
		 * 参考图片URL
		 */
		private String referenceImage;

		/**
		 * 参考链接URL
		 */
		private String referenceLink;
		
		/**
		 * 备注信息
		 */
		private String remark;
		
		/**
		 * 创建时间
		 */
		@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
		private Date createTime;
		
		/**
		 * 更新时间
		 */
		@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
		private Date updateTime;
}
