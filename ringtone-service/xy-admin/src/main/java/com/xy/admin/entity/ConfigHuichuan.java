package com.xy.admin.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * 
 * <AUTHOR>
 * @since 2025/7/28 09:52
 */
@Data
@Accessors(chain = true)
@SuperBuilder
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "config_huichuan")
public class ConfigHuichuan implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "不能为null")
    private Long id;

    /**
     * 渠道
     */
    @TableField(value = "channel")
    @Size(max = 52,message = "渠道最大长度要小于 52")
    private String channel;

    /**
     * 来源
     */
    @TableField(value = "`source`")
    @Size(max = 52,message = "来源最大长度要小于 52")
    private String source;

    /**
     * 参数名
     */
    @TableField(value = "param_name")
    @Size(max = 52,message = "参数名最大长度要小于 52")
    private String paramName;

    /**
     * 平台：ks, dy, llg, csj, gdt
     */
    @TableField(value = "platform")
    @Size(max = 255,message = "平台：ks, dy, llg, csj, gdt最大长度要小于 255")
    private String platform;

    /**
     * 0延时回传，1立即回传
     */
    @TableField(value = "nodelay")
    private Short nodelay;

    /**
     * 回传百分比
     */
    @TableField(value = "huichuan_pct")
    private Integer huichuanPct;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}