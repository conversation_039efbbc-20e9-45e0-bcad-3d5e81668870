package com.xy.admin.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.admin.entity.UaaUser;
import com.xy.admin.service.UaaUserService;
import com.xy.admin.mapper.UaaUserMapper;
import org.springframework.stereotype.Service;

/**
 * 针对表【uaa_user(用户)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Service
public class UaaUserServiceImpl extends ServiceImpl<UaaUserMapper, UaaUser>
        implements UaaUserService {

}




