package com.xy.admin.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * 推广商表
 * <AUTHOR>
 * @since 2025/7/15 15:53
 */
@Data
@Accessors(chain = true)
@SuperBuilder
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "product_promotion_company")
public class ProductPromotionCompany implements Serializable {
		private static final long serialVersionUID = 1L;
		
		/**
		 * ID
		 */
		@TableId(value = "id", type = IdType.AUTO)
		@NotNull(message = "ID不能为null")
		private Integer id;
		
		/**
		 * 公司ID
		 */
		@TableField(value = "company_id")
		@Size(max = 256, message = "公司ID最大长度要小于 256")
		private String companyId;
		
		/**
		 * 公司名称
		 */
		@TableField(value = "company_name")
		@Size(max = 256, message = "公司名称最大长度要小于 256")
		private String companyName;
		
		/**
		 * appID
		 */
		@TableField(value = "app_id")
		@Size(max = 256, message = "appID最大长度要小于 256")
		private String appId;
		
		/**
		 * secretKey
		 */
		@TableField(value = "secret_key")
		@Size(max = 256, message = "secretKey最大长度要小于 256")
		private String secretKey;
		
		/**
		 * 创建时间
		 */
		@TableField(value = "create_time", fill = FieldFill.INSERT)
		@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
		@NotNull(message = "创建时间不能为null")
		private Date createTime;
		
		/**
		 * 更新时间
		 */
		@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
		@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
		@NotNull(message = "更新时间不能为null")
		private Date updateTime;
}