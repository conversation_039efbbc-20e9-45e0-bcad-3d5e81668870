package com.xy.admin.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2023-12-01
 */
@TableName(value = "log_sys")
@Data
public class LogSys implements Serializable {

    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 代码位置
     */
    private String exceptionMessage;
    /**
     * 
     */
    private String exceptionName;
    /**
     * 
     */
    private String exceptionTrace;
    /**
     * 
     */
    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
