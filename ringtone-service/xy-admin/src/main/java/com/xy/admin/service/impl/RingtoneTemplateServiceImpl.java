package com.xy.admin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.admin.entity.RingtoneCategory;
import com.xy.admin.entity.RingtoneTemplate;
import com.xy.admin.mapper.RingtoneTemplateMapper;
import com.xy.admin.service.RingtoneCategoryService;
import com.xy.admin.service.RingtoneTemplateService;
import com.xy.admin.vo.RingtoneTemplateVO;
import com.xy.base.starter.dto.CommonPageQuery;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 针对表【ringtone_template(铃音视频模板)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2023-12-26
 */
@Service
@RequiredArgsConstructor
public class RingtoneTemplateServiceImpl extends ServiceImpl<RingtoneTemplateMapper, RingtoneTemplate>
        implements RingtoneTemplateService {

    private final RingtoneCategoryService categoryService;

    @Override
    public IPage<RingtoneTemplateVO> listVO(CommonPageQuery query) {

        Page<RingtoneTemplate> page = query.buildPage();

        lambdaQuery()
                .like(StringUtils.hasText(query.getKeyword()), RingtoneTemplate::getName, query.getKeyword())
                .orderByAsc(RingtoneTemplate::getSort)
                .orderByDesc(RingtoneTemplate::getCreateTime)
                .page(page);

        Set<String> cateIds = new HashSet<>();
        page.getRecords().forEach(t -> {
            if (StringUtils.hasText(t.getCategories())) {
                String[] ids = t.getCategories().split(",");
                Collections.addAll(cateIds, ids);
            }
        });
        HashMap<Integer, RingtoneCategory> cateDict = new HashMap<>();
        if (cateIds.size() > 0) {
            List<RingtoneCategory> list = categoryService.lambdaQuery().in(RingtoneCategory::getId, cateIds).list();
            list.forEach(l -> cateDict.put(l.getId(), l));
        }

        return page.convert(r -> {
            RingtoneTemplateVO vo = new RingtoneTemplateVO();
            vo.setCates(new ArrayList<>());
            if (StringUtils.hasText(r.getCategories())) {
                String[] ids = r.getCategories().split(",");
                for (String id : ids) {
                    vo.getCates().add(cateDict.get(Integer.parseInt(id)));
                }
            }
            BeanUtils.copyProperties(r, vo);
            return vo;
        });
    }
}




