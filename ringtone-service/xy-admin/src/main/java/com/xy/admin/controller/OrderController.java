package com.xy.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xy.admin.entity.RingtoneOrder;
import com.xy.admin.entity.SubscriptionOrder;
import com.xy.admin.service.RingtoneOrderService;
import com.xy.admin.service.SubscriptionOrderService;
import com.xy.base.starter.dto.CommonPageQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 订单管理
 * <AUTHOR>
 * @since 2023/11/24 16:29
 */
@Slf4j
@RestController
@RequestMapping("/order")
@RequiredArgsConstructor
public class OrderController {

    private final RingtoneOrderService ringtoneOrderService;
    private final SubscriptionOrderService subscriptionOrderService;

    /**
     * 订单列表
     */
    @PostMapping("/list")
    public IPage<RingtoneOrder> list(@RequestBody CommonPageQuery query) {

        Page<RingtoneOrder> page = query.buildPage();
        return ringtoneOrderService.lambdaQuery()
                .or(StringUtils.hasText(query.getKeyword()),
                        w -> w.like(RingtoneOrder::getPhone, query.getKeyword()).or().like(RingtoneOrder::getRingtoneName, query.getKeyword()))
                .ge(query.getStartTime() != null, RingtoneOrder::getCreateTime, query.getStartTime())
                .le(query.getEndTime() != null, RingtoneOrder::getCreateTime, query.getEndTime())
                .orderByDesc(RingtoneOrder::getOrderNumber)
                .page(page);
    }

    /**
     * 订阅订单列表
     */
    @PostMapping("/listSubscription")
    public IPage<SubscriptionOrder> listSubscription(@RequestBody CommonPageQuery query) {

        Page<SubscriptionOrder> page = query.buildPage();
        return subscriptionOrderService.lambdaQuery()
                .or(StringUtils.hasText(query.getKeyword()),
                        w -> w.like(SubscriptionOrder::getPhone, query.getKeyword()))
                .ge(query.getStartTime() != null, SubscriptionOrder::getCreateTime, query.getStartTime())
                .le(query.getEndTime() != null, SubscriptionOrder::getCreateTime, query.getEndTime())
                .orderByDesc(SubscriptionOrder::getCreateTime)
                .page(page);
    }

}
