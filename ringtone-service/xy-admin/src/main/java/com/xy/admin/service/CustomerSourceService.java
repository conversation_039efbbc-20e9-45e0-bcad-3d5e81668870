package com.xy.admin.service;

import com.xy.admin.vo.customerSource.CustomerSourceQueryReqVO;
import com.xy.admin.vo.customerSource.CustomerSourceResVO;
import com.xy.admin.vo.customerSource.CustomerSourcePageReqVO;
import com.xy.admin.vo.customerSource.CustomerSourcePageResVO;
import java.util.Map;

/**
 * 客户来源查询服务接口
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
public interface CustomerSourceService {
	
	/**
	 * 查询客户来源信息
	 *
	 * @param queryDTO 查询参数
	 * @return 客户来源信息
	 */
	CustomerSourceResVO queryCustomerSource(CustomerSourceQueryReqVO queryDTO);
	
	/**
	 * 分页查询客户来源信息
	 *
	 * @param pageReqVO 分页查询参数
	 * @return 分页查询结果
	 */
	CustomerSourcePageResVO pageQueryCustomerSource(CustomerSourcePageReqVO pageReqVO);
	
	/**
	 * 获取渠道映射
	 */
	Map<String, String> getChannelMap();
	
}