package com.xy.admin.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
@TableName(value = "stat_overview")
@Data
public class StatOverview implements Serializable {

    /**
     * 主键
     */
    @TableId
    private String id;

    /**
     * 渠道
     */
    private String channel;
    /**
     * 来源
     */
    private String source;
    /**
     * 平台
     */
    private String platform;
    /**
     * 日期
     */
    private LocalDate date;
    /**
     * 提交数
     */
    private Double submitNum;
    /**
     * 成功数
     */
    private Double successNum;
    /**
     * 失败数
     */
    private Double failNum;
    /**
     * 失败率
     */
    private Double failRatio;
    /**
     * 退订数
     */
    private Double tdNum;
    /**
     * 退订率
     */
    private Double tdRatio;
    /**
     * 净增数
     */
    private Integer jzNum;
    /**
     * 花费
     */
    private Double cost;
    /**
     * 成本-新增
     */
    private Double costXz;
    /**
     * 成本-净增
     */
    private Double costJz;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
