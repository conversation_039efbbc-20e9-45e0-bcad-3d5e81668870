package com.xy.admin.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2024-02-21
 */
@TableName(value = "member_manage")
@Data
public class MemberManage implements Serializable {

    /**
     * 主键
     */
    @TableId
    private String id;

    /**
     * 操作时间
     */
    private LocalDateTime createTime;
    /**
     * 用户手机号
     */
    private String billNum;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 渠道
     */
    private String channel;
    /**
     * 来源
     */
    private String source;
    /**
     * 媒体平台
     */
    private String platform;
    /**
     * 回传状态：0未回传，1已回传
     */
    private String reportStatus;
    /**
     * 操作类型
     */
    private String operType;
    /**
     * 数据更新时间
     */
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
