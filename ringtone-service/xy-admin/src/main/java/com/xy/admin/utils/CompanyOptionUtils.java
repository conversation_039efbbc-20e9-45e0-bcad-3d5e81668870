package com.xy.admin.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xy.admin.vo.common.CompanyOptionVO;

import java.util.List;
import java.util.Optional;

/**
 * 公司选项工具类
 * 
 * <AUTHOR>
 * @since 2025/7/18
 */
public class CompanyOptionUtils {
    
    /**
     * 根据公司编号查找公司名称
     * 
     * @param companyOptions 公司选项列表
     * @param companyId 公司编号
     * @return 公司名称，如果未找到则返回null
     */
    public static String getCompanyNameById(List<CompanyOptionVO> companyOptions, String companyId) {
        if (CollUtil.isEmpty(companyOptions) || StrUtil.isBlank(companyId)) {
            return null;
        }
        
        Optional<CompanyOptionVO> option = companyOptions.stream()
            .filter(company -> companyId.equals(company.getCompanyId()))
            .findFirst();
        
        return option.map(CompanyOptionVO::getCompanyName).orElse(null);
    }
    
    /**
     * 根据公司名称查找公司编号
     * 
     * @param companyOptions 公司选项列表
     * @param companyName 公司名称
     * @return 公司编号，如果未找到则返回null
     */
    public static String getCompanyIdByName(List<CompanyOptionVO> companyOptions, String companyName) {
        if (CollUtil.isEmpty(companyOptions) || StrUtil.isBlank(companyName)) {
            return null;
        }
        
        Optional<CompanyOptionVO> option = companyOptions.stream()
            .filter(company -> companyName.equals(company.getCompanyName()))
            .findFirst();
        
        return option.map(CompanyOptionVO::getCompanyId).orElse(null);
    }
    
    /**
     * 验证公司编号是否存在
     * 
     * @param companyOptions 公司选项列表
     * @param companyId 公司编号
     * @return 是否存在
     */
    public static boolean isValidCompanyId(List<CompanyOptionVO> companyOptions, String companyId) {
        return getCompanyNameById(companyOptions, companyId) != null;
    }
    
    /**
     * 验证公司名称是否存在
     * 
     * @param companyOptions 公司选项列表
     * @param companyName 公司名称
     * @return 是否存在
     */
    public static boolean isValidCompanyName(List<CompanyOptionVO> companyOptions, String companyName) {
        return getCompanyIdByName(companyOptions, companyName) != null;
    }
    
    /**
     * 获取公司选项的显示文本（格式：编号 - 名称）
     * 
     * @param companyOption 公司选项
     * @return 显示文本
     */
    public static String getDisplayText(CompanyOptionVO companyOption) {
        if (companyOption == null) {
            return "";
        }
        
        String companyId = StrUtil.blankToDefault(companyOption.getCompanyId(), "");
        String companyName = StrUtil.blankToDefault(companyOption.getCompanyName(), "");
        
        if (StrUtil.isBlank(companyId) && StrUtil.isBlank(companyName)) {
            return "";
        } else if (StrUtil.isBlank(companyId)) {
            return companyName;
        } else if (StrUtil.isBlank(companyName)) {
            return companyId;
        } else {
            return companyId + " - " + companyName;
        }
    }
    
    /**
     * 获取公司选项的显示文本（根据公司编号）
     * 
     * @param companyOptions 公司选项列表
     * @param companyId 公司编号
     * @return 显示文本
     */
    public static String getDisplayTextById(List<CompanyOptionVO> companyOptions, String companyId) {
        if (CollUtil.isEmpty(companyOptions) || StrUtil.isBlank(companyId)) {
            return companyId;
        }
        
        Optional<CompanyOptionVO> option = companyOptions.stream()
            .filter(company -> companyId.equals(company.getCompanyId()))
            .findFirst();
        
        return option.map(CompanyOptionUtils::getDisplayText).orElse(companyId);
    }
}
