package com.xy.admin.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.admin.entity.ProductCompanyInfo;
import com.xy.admin.mapper.ProductCompanyInfoMapper;
import com.xy.admin.service.ProductCompanyInfoService;
/**
 * 公司表(product_company_info)表服务层实现类
 * <AUTHOR>
 * @since 2025/7/15 15:55
 */
@Service
public class ProductCompanyInfoServiceImpl extends ServiceImpl<ProductCompanyInfoMapper, ProductCompanyInfo> implements ProductCompanyInfoService{

}
