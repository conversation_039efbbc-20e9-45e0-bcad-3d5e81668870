package com.xy.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xy.admin.entity.ConfigHuichuan;
import com.xy.admin.vo.configHuichuan.ConfigHuichuanQueryVO;
import com.xy.admin.vo.configHuichuan.ConfigHuichuanVO;
    /**
 * 
 * <AUTHOR>
 * @since 2025/7/28 09:52
 */
public interface ConfigHuichuanService extends IService<ConfigHuichuan>{

    /**
     * 分页查询回传配置信息
     * @param queryVO 查询条件
     * @return 分页结果
     */
    IPage<ConfigHuichuanVO> queryPage(ConfigHuichuanQueryVO queryVO);

}
