package com.xy.admin.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2023-12-03
 */
@TableName(value = "ring_department")
@Data
public class RingDepartment implements Serializable {

    /**
     * 
     */
    @TableId
    private String departmentId;
    /**
     * 
     */
    private String departmentName;
    /**
     * 部门创建来源，0前端创建。1后端创建
     */
    private Integer type;
    /**
     * 二级渠道手机号
     */
    private String adminMsisdn;
    /**
     * 
     */
    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
