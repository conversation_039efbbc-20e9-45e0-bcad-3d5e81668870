package com.xy.admin.vo.configHuichuan;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * 回传配置响应VO
 * <AUTHOR>
 * @since 2025/7/28
 */
@Data
@Accessors(chain = true)
@SuperBuilder
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ConfigHuichuanVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 渠道
     */
    private String channel;
    
    /**
     * 来源
     */
    private String source;
    
    /**
     * 参数名
     */
    private String paramName;
    
    /**
     * 平台：ks, dy, llg, csj, gdt
     */
    private String platform;
    
    /**
     * 0延时回传，1立即回传
     */
    private Integer nodelay;
    
    /**
     * 回传百分比
     */
    private Integer huichuanPct;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
