package com.xy.admin.vo.customerSource;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.Date;

/**
 * 客户来源分页查询请求VO
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerSourcePageReqVO {
	
	/**
	 * 页码，从1开始
	 */
	@Min(value = 1, message = "页码不能小于1")
	private Integer pageNo = 1;
	
	/**
	 * 每页大小
	 */
	@Min(value = 1, message = "每页大小不能小于1")
	@Max(value = 1000, message = "每页大小不能超过1000")
	private Integer pageSize = 10;
	
	/**
	 * 项目类型：zsgl、nmch、other
	 */
	private String pro;
	
	/**
	 * 手机号（支持模糊查询）
	 */
	private String phone;
	
	/**
	 * 渠道编码
	 */
	private String channel;
	
	/**
	 * 来源
	 */
	private String source;
	
	/**
	 * 回传状态：0-未回传，1-已回传
	 */
	private Integer huichuanStatus;
	
	/**
	 * 平台：ks, dy, llg, csj, gdt
	 */
	private String platform;
	
	/**
	 * 开始时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date startTime;
	
	/**
	 * 结束时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date endTime;
	
	/**
	 * 排序字段：create_time、phone、channel等
	 */
	private String sortField = "create_time";
	
	/**
	 * 排序方向：asc、desc
	 */
	private String sortOrder = "desc";
}