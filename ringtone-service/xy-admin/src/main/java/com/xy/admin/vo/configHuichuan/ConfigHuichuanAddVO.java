package com.xy.admin.vo.configHuichuan;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 回传配置新增VO
 * <AUTHOR>
 * @since 2025/7/28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ConfigHuichuanAddVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 渠道
     */
    @NotBlank(message = "渠道不能为空")
    @Size(max = 52, message = "渠道最大长度要小于 52")
    private String channel;
    
    /**
     * 来源
     */
    @NotBlank(message = "来源不能为空")
    @Size(max = 52, message = "来源最大长度要小于 52")
    private String source;
    
    /**
     * 参数名
     */
    @Size(max = 52, message = "参数名最大长度要小于 52")
    private String paramName;
    
    /**
     * 平台：ks, dy, llg, csj, gdt
     */
    @Size(max = 52, message = "平台最大长度要小于 52")
    private String platform;
    
    /**
     * 0延时回传，1立即回传
     */
    @NotNull(message = "回传方式不能为空")
    @Min(value = 0, message = "回传方式值必须为0或1")
    @Max(value = 1, message = "回传方式值必须为0或1")
    private Integer nodelay;
    
    /**
     * 回传百分比
     */
    @NotNull(message = "回传百分比不能为空")
    @Min(value = 0, message = "回传百分比不能小于0")
    @Max(value = 100, message = "回传百分比不能大于100")
    private Integer huichuanPct;
}
