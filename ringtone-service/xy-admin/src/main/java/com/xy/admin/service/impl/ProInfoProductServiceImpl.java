package com.xy.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.admin.entity.ProInfoProduct;
import com.xy.admin.mapper.ProInfoProductMapper;
import com.xy.admin.service.ProInfoProductService;
import com.xy.admin.vo.proInfoProduct.ProInfoProductQueryVO;
import com.xy.admin.vo.proInfoProduct.ProInfoProductVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品推广信息服务实现类
 * <AUTHOR>
 * @since 2025/7/17 15:35
 */
@Service
public class ProInfoProductServiceImpl extends ServiceImpl<ProInfoProductMapper, ProInfoProduct> implements ProInfoProductService{

    @Override
    public IPage<ProInfoProductVO> queryPage(ProInfoProductQueryVO queryVO) {
        // 构建查询条件
        LambdaQueryWrapper<ProInfoProduct> wrapper = new LambdaQueryWrapper<>();

        // 公司查询
        if (StrUtil.isNotBlank(queryVO.getCompany())) {
            wrapper.like(ProInfoProduct::getCompany, queryVO.getCompany());
        }

        // 运营商查询
        if (StrUtil.isNotBlank(queryVO.getCarrier())) {
            wrapper.like(ProInfoProduct::getCarrier, queryVO.getCarrier());
        }

        // 产品编号查询
        if (StrUtil.isNotBlank(queryVO.getProductNo())) {
            wrapper.like(ProInfoProduct::getProductNo, queryVO.getProductNo());
        }

        // 产品名称查询
        if (StrUtil.isNotBlank(queryVO.getProductName())) {
            wrapper.like(ProInfoProduct::getProductName, queryVO.getProductName());
        }

        // 产品状态查询
        if (queryVO.getProductStatus() != null) {
            wrapper.eq(ProInfoProduct::getProductStatus, queryVO.getProductStatus());
        }

        // 产品价格查询
        if (StrUtil.isNotBlank(queryVO.getProductPrice())) {
            wrapper.eq(ProInfoProduct::getProductPrice, queryVO.getProductPrice());
        }

        // 推广价格查询
        if (StrUtil.isNotBlank(queryVO.getPromotionPrice())) {
            wrapper.eq(ProInfoProduct::getPromotionPrice, queryVO.getPromotionPrice());
        }

        // 开发对接状态查询
        if (queryVO.getDevelopmentStatus() != null) {
            wrapper.eq(ProInfoProduct::getDevelopmentStatus, queryVO.getDevelopmentStatus());
        }

        // 推广省份查询
        if (StrUtil.isNotBlank(queryVO.getPromotionProvince())) {
            wrapper.like(ProInfoProduct::getPromotionProvince, queryVO.getPromotionProvince());
        }

        // 业务要求查询
        if (StrUtil.isNotBlank(queryVO.getBusinessRequirements())) {
            wrapper.like(ProInfoProduct::getBusinessRequirements, queryVO.getBusinessRequirements());
        }

        // 参考链接查询
        if (StrUtil.isNotBlank(queryVO.getReferenceLink())) {
            wrapper.like(ProInfoProduct::getReferenceLink, queryVO.getReferenceLink());
        }

        // 备注查询
        if (StrUtil.isNotBlank(queryVO.getRemark())) {
            wrapper.like(ProInfoProduct::getRemark, queryVO.getRemark());
        }

        // 创建时间范围查询
        if (queryVO.getCreateTimeStart() != null) {
            wrapper.ge(ProInfoProduct::getCreateTime, queryVO.getCreateTimeStart());
        }
        if (queryVO.getCreateTimeEnd() != null) {
            wrapper.le(ProInfoProduct::getCreateTime, queryVO.getCreateTimeEnd());
        }

        // 更新时间范围查询
        if (queryVO.getUpdateTimeStart() != null) {
            wrapper.ge(ProInfoProduct::getUpdateTime, queryVO.getUpdateTimeStart());
        }
        if (queryVO.getUpdateTimeEnd() != null) {
            wrapper.le(ProInfoProduct::getUpdateTime, queryVO.getUpdateTimeEnd());
        }

        // 排序处理
        if (StrUtil.isNotBlank(queryVO.getOrderField()) && StrUtil.isNotBlank(queryVO.getOrderType())) {
            boolean isAsc = "asc".equalsIgnoreCase(queryVO.getOrderType());
            switch (queryVO.getOrderField()) {
                case "company":
                    wrapper.orderBy(true, isAsc, ProInfoProduct::getCompany);
                    break;
                case "createTime":
                    wrapper.orderBy(true, isAsc, ProInfoProduct::getCreateTime);
                    break;
                case "updateTime":
                    wrapper.orderBy(true, isAsc, ProInfoProduct::getUpdateTime);
                    break;
                case "productName":
                    wrapper.orderBy(true, isAsc, ProInfoProduct::getProductName);
                    break;
                case "productPrice":
                    wrapper.orderBy(true, isAsc, ProInfoProduct::getProductPrice);
                    break;
                case "promotionPrice":
                    wrapper.orderBy(true, isAsc, ProInfoProduct::getPromotionPrice);
                    break;
                case "productStatus":
                    wrapper.orderBy(true, isAsc, ProInfoProduct::getProductStatus);
                    break;
                case "developmentStatus":
                    wrapper.orderBy(true, isAsc, ProInfoProduct::getDevelopmentStatus);
                    break;
                default:
                    wrapper.orderByDesc(ProInfoProduct::getCreateTime);
                    break;
            }
        } else {
            // 默认按创建时间倒序
            wrapper.orderByDesc(ProInfoProduct::getCreateTime);
        }

        // 分页查询
        Page<ProInfoProduct> page = queryVO.buildPage();
        IPage<ProInfoProduct> pageResult = this.page(page, wrapper);

        // 转换为VO
        List<ProInfoProductVO> voList = pageResult.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        // 构建返回结果
        Page<ProInfoProductVO> voPage = new Page<>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal());
        voPage.setRecords(voList);

        return voPage;
    }

    /**
     * 实体转VO
     */
    private ProInfoProductVO convertToVO(ProInfoProduct entity) {
        ProInfoProductVO vo = new ProInfoProductVO();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

}
