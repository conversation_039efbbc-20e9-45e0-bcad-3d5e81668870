package com.xy.admin.controller;

import com.xy.admin.service.MultiDataSourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 多数据源控制器示例
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@RestController
@RequestMapping("/admin/datasource")
@Slf4j
public class MultiDataSourceController {

  @Autowired
  private MultiDataSourceService multiDataSourceService;

  /**
   * 测试主数据源
   */
  @GetMapping("/test/primary")
  public List<Map<String, Object>> testPrimary() {
    return multiDataSourceService.queryFromPrimary();
  }

  /**
   * 测试次要数据源
   */
  @GetMapping("/test/secondary")
  public List<Map<String, Object>> testSecondary() {
    return multiDataSourceService.queryFromSecondary();
  }

  /**
   * 测试第三数据源
   */
  @GetMapping("/test/tertiary")
  public List<Map<String, Object>> testTertiary() {
    return multiDataSourceService.queryFromTertiary();
  }

  /**
   * 测试第四数据源
   */
  @GetMapping("/test/fourth")
  public List<Map<String, Object>> testFourth() {
    return multiDataSourceService.queryFromFourth();
  }

  /**
   * 测试多数据源查询
   */
  @GetMapping("/test/multiple")
  public Map<String, Object> testMultiple() {
    return multiDataSourceService.queryMultipleDataSources();
  }

  /**
   * 测试批量操作
   */
  @GetMapping("/test/batch")
  public String testBatch() {
    try {
      multiDataSourceService.batchOperationExample();
      return "批量操作成功";
    } catch (Exception e) {
      log.error("批量操作失败", e);
      return "批量操作失败：" + e.getMessage();
    }
  }
  
}