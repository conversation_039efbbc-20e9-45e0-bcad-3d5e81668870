package com.xy.admin.controller;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xy.admin.annotation.DataSource;
import com.xy.admin.entity.ProductPromotionCompany;
import com.xy.admin.service.ProductPromotionCompanyService;
import com.xy.admin.vo.productPromotionCompany.ProductPromotionCompanyVO;
import com.xy.admin.vo.productPromotionCompany.ProductPromotionCompanyAddVO;
import com.xy.admin.vo.productPromotionCompany.ProductPromotionCompanyEditVO;
import com.xy.base.starter.dto.CommonPageQuery;
import com.xy.base.core.response.Result;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;

import org.springframework.beans.factory.annotation.Autowired;

/**
* 推广商表表控制层
*
* <AUTHOR>
* @since 2025/7/15 15:54
*/
@RestController
@RequestMapping("/productPromotionCompany")
public class ProductPromotionCompanyController {

    @Autowired
    private ProductPromotionCompanyService productPromotionCompanyService;
    
    /**
     * 列表查询
     */
    @PostMapping("/list")
    @DataSource("tertiary")
    public IPage<ProductPromotionCompanyVO> userList(@RequestBody CommonPageQuery query) {

        Page<ProductPromotionCompany> page = query.buildPage();

        // 构建查询条件
        IPage<ProductPromotionCompany> entityPage;
        if (StringUtils.hasText(query.getKeyword())) {
            // 有关键字时进行模糊查询
            entityPage = productPromotionCompanyService.lambdaQuery()
                    .and(wrapper -> wrapper
                            .like(ProductPromotionCompany::getCompanyName, query.getKeyword())
                            .or()
                            .like(ProductPromotionCompany::getCompanyId, query.getKeyword())
                            .or()
                            .like(ProductPromotionCompany::getAppId, query.getKeyword())
                    )
                    .orderByDesc(ProductPromotionCompany::getCreateTime)
                    .page(page);
        } else {
            // 无关键字时查询全部
            entityPage = productPromotionCompanyService.lambdaQuery()
                    .orderByDesc(ProductPromotionCompany::getCreateTime)
                    .page(page);
        }

        // 转换为VO
        return entityPage.convert(entity -> {
            ProductPromotionCompanyVO vo = new ProductPromotionCompanyVO();
            BeanUtils.copyProperties(entity, vo);
            return vo;
        });
    }
    
    /**
     * 新增
     * appId和secretKey会自动生成，无需手动填写
     */
    @PostMapping("/add")
    @DataSource("tertiary")
    public Result<ProductPromotionCompanyVO> add(@RequestBody @Validated ProductPromotionCompanyAddVO addVO) {
        // 转换为实体类
        ProductPromotionCompany entity = new ProductPromotionCompany();
        BeanUtils.copyProperties(addVO, entity);

        // 服务层会自动生成appId和secretKey
        boolean success = productPromotionCompanyService.save(entity);

        if (success) {
            // 返回新增后的数据
            ProductPromotionCompanyVO vo = new ProductPromotionCompanyVO();
            BeanUtils.copyProperties(entity, vo);
            return Result.success(vo);
        } else {
            return Result.error(500, "新增失败");
        }
    }
    
    /**
     * 编辑
     */
    @PostMapping("/edit")
    @DataSource("tertiary")
    public Result<ProductPromotionCompanyVO> edit(@RequestBody @Validated ProductPromotionCompanyEditVO editVO) {
        // 转换为实体类
        ProductPromotionCompany entity = new ProductPromotionCompany();
        BeanUtils.copyProperties(editVO, entity);

        boolean success = productPromotionCompanyService.updateById(entity);

        if (success) {
            // 返回更新后的数据
            ProductPromotionCompany updatedEntity = productPromotionCompanyService.getById(editVO.getId());
            ProductPromotionCompanyVO vo = new ProductPromotionCompanyVO();
            BeanUtils.copyProperties(updatedEntity, vo);
            return Result.success(vo);
        } else {
            return Result.error(500, "更新失败");
        }
    }

    /**
     * 删除
     */
    @GetMapping("/delete/{id}")
    @DataSource("tertiary")
    public Result<String> delete(@PathVariable Long id) {
        // 只需要ID即可删除
        boolean success = productPromotionCompanyService.removeById(id);
        if (success) {
            return Result.success("删除成功");
        } else {
            return Result.error(500, "删除失败");
        }
    }
    
}
