package com.xy.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.admin.entity.ProOrder;
import com.xy.admin.mapper.ProOrderMapper;
import com.xy.admin.service.ProOrderService;
import com.xy.admin.vo.proOrder.ProOrderQueryVO;
import com.xy.admin.vo.proOrder.ProOrderVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 针对表【pro_order】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2025/8/1
 */
@Service
public class ProOrderServiceImpl extends ServiceImpl<ProOrderMapper, ProOrder>
        implements ProOrderService {

    @Override
    public IPage<ProOrderVO> queryPage(ProOrderQueryVO queryVO) {
        // 构建查询条件
        LambdaQueryWrapper<ProOrder> wrapper = new LambdaQueryWrapper<>();

        // 渠道查询
        if (StrUtil.isNotBlank(queryVO.getChannel())) {
            wrapper.like(ProOrder::getChannel, queryVO.getChannel());
        }

        // 请求来源查询
        if (StrUtil.isNotBlank(queryVO.getSource())) {
            wrapper.like(ProOrder::getSource, queryVO.getSource());
        }

        // 手机号查询
        if (StrUtil.isNotBlank(queryVO.getPhone())) {
            wrapper.like(ProOrder::getPhone, queryVO.getPhone());
        }

        // 逻辑回传状态查询
        if (queryVO.getReportStatus() != null) {
            wrapper.eq(ProOrder::getReportStatus, queryVO.getReportStatus());
        }

        // 物理回传状态查询
        if (queryVO.getHuichuanStatus() != null) {
            wrapper.eq(ProOrder::getHuichuanStatus, queryVO.getHuichuanStatus());
        }

        // 推广产品名查询
        if (StrUtil.isNotBlank(queryVO.getPro())) {
            wrapper.like(ProOrder::getPro, queryVO.getPro());
        }

        // 回调字符串查询
        if (StrUtil.isNotBlank(queryVO.getCallback())) {
            wrapper.like(ProOrder::getCallback, queryVO.getCallback());
        }

        // 平台查询
        if (StrUtil.isNotBlank(queryVO.getPlatform())) {
            wrapper.like(ProOrder::getPlatform, queryVO.getPlatform());
        }

        // 广告项目ID查询
        if (StrUtil.isNotBlank(queryVO.getProjectid())) {
            wrapper.eq(ProOrder::getProjectid, queryVO.getProjectid());
        }

        // 广告ID查询
        if (StrUtil.isNotBlank(queryVO.getPromotionid())) {
            wrapper.eq(ProOrder::getPromotionid, queryVO.getPromotionid());
        }

        // 广告主订单ID查询
        if (StrUtil.isNotBlank(queryVO.getOrderId())) {
            wrapper.eq(ProOrder::getOrderId, queryVO.getOrderId());
        }

        // URL查询
        if (StrUtil.isNotBlank(queryVO.getUrl())) {
            wrapper.like(ProOrder::getUrl, queryVO.getUrl());
        }

        // UserAgent查询
        if (StrUtil.isNotBlank(queryVO.getUa())) {
            wrapper.like(ProOrder::getUa, queryVO.getUa());
        }

        // APP名查询
        if (StrUtil.isNotBlank(queryVO.getApp())) {
            wrapper.like(ProOrder::getApp, queryVO.getApp());
        }

        // 创建时间范围查询
        if (queryVO.getCreateTimeStart() != null) {
            wrapper.ge(ProOrder::getCreateTime, queryVO.getCreateTimeStart());
        }
        if (queryVO.getCreateTimeEnd() != null) {
            wrapper.le(ProOrder::getCreateTime, queryVO.getCreateTimeEnd());
        }

        // 修改时间范围查询
        if (queryVO.getUpdateTimeStart() != null) {
            wrapper.ge(ProOrder::getUpdateTime, queryVO.getUpdateTimeStart());
        }
        if (queryVO.getUpdateTimeEnd() != null) {
            wrapper.le(ProOrder::getUpdateTime, queryVO.getUpdateTimeEnd());
        }

        // 通用关键字查询（如果有keyword，则在多个字段中搜索）
        if (StrUtil.isNotBlank(queryVO.getKeyword())) {
            wrapper.and(w -> w
                    .like(ProOrder::getChannel, queryVO.getKeyword())
                    .or().like(ProOrder::getSource, queryVO.getKeyword())
                    .or().like(ProOrder::getPhone, queryVO.getKeyword())
                    .or().like(ProOrder::getPro, queryVO.getKeyword())
                    .or().like(ProOrder::getCallback, queryVO.getKeyword())
                    .or().like(ProOrder::getPlatform, queryVO.getKeyword())
                    .or().like(ProOrder::getProjectid, queryVO.getKeyword())
                    .or().like(ProOrder::getPromotionid, queryVO.getKeyword())
                    .or().like(ProOrder::getOrderId, queryVO.getKeyword())
                    .or().like(ProOrder::getApp, queryVO.getKeyword())
            );
        }

        // 排序处理
        wrapper.orderByDesc(ProOrder::getCreateTime);

        // 分页查询
        Page<ProOrder> page = queryVO.buildPage();
        IPage<ProOrder> pageResult = this.page(page, wrapper);

        // 转换为VO
        List<ProOrderVO> voList = pageResult.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        // 构建返回结果
        Page<ProOrderVO> voPage = new Page<>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal());
        voPage.setRecords(voList);

        return voPage;
    }

    /**
     * 实体转换为VO
     */
    private ProOrderVO convertToVO(ProOrder entity) {
        ProOrderVO vo = new ProOrderVO();
        BeanUtils.copyProperties(entity, vo);
        
        // 设置状态描述
        vo.setReportStatusDesc(getReportStatusDesc(entity.getReportStatus()));
        vo.setHuichuanStatusDesc(getHuichuanStatusDesc(entity.getHuichuanStatus()));
        
        return vo;
    }

    /**
     * 获取逻辑回传状态描述
     */
    private String getReportStatusDesc(Integer reportStatus) {
        if (reportStatus == null) {
            return "未知";
        }
        switch (reportStatus) {
            case 0:
                return "未回传";
            case 1:
                return "已回传";
            default:
                return "未知";
        }
    }

    /**
     * 获取物理回传状态描述
     */
    private String getHuichuanStatusDesc(Integer huichuanStatus) {
        if (huichuanStatus == null) {
            return "未知";
        }
        switch (huichuanStatus) {
            case 0:
                return "未回传";
            case 1:
                return "已回传";
            default:
                return "未知";
        }
    }
}
