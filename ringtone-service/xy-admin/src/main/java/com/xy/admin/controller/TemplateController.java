package com.xy.admin.controller;

import cn.hutool.extra.servlet.ServletUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xy.admin.entity.RingtoneCategory;
import com.xy.admin.entity.RingtoneTemplate;
import com.xy.admin.service.RingtoneCategoryService;
import com.xy.admin.service.RingtoneTemplateService;
import com.xy.admin.vo.CategoryTreeVO;
import com.xy.admin.vo.RingtoneTemplateVO;
import com.xy.base.core.exception.AppException;
import com.xy.base.starter.dto.CommonPageQuery;
import com.xy.base.starter.redis.RedisUtils;
import com.xy.base.starter.security.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 模板管理
 *
 * <AUTHOR>
 * @since 2023/12/26 14:20
 */
@RequiredArgsConstructor
@RestController
@RequestMapping(value = "/template")
public class TemplateController {

    private final RingtoneTemplateService templateService;
    private final RingtoneCategoryService categoryService;


    /**
     * @ignore
     */
    @GetMapping("/env")
    public Map<String, Object> env(HttpServletRequest request) {

        Map<String, Object> infos = new HashMap<>(10);

        infos.put("time2", LocalDateTime.now());
        RedisUtils.set("test", "ok", 10);
        infos.put("redis", RedisUtils.get("test"));

        infos.put("ip", ServletUtil.getClientIP(request));
        infos.put("login", SecurityUtils.isLogin());
        infos.put("x-forwarded-for", request.getHeader("x-forwarded-for"));
        infos.put("getRemoteAddress", request.getRemoteAddr());
        infos.put("X-Real-IP", request.getHeader("X-Real-IP"));

        infos.put("db", categoryService.getById(7));

        return infos;
    }

    /**
     * 分类列表
     */
    @PreAuthorize("hasAuthority('template:cate:list')")
    @PostMapping("/cate/list")
    public IPage<RingtoneCategory> cateList(@RequestBody CommonPageQuery query) {

        Page<RingtoneCategory> page = query.buildPage();
        return categoryService.lambdaQuery()
                .like(StringUtils.hasText(query.getKeyword()), RingtoneCategory::getName, query.getKeyword())
                .orderByDesc(RingtoneCategory::getId)
                .page(page);
    }

    /**
     * 分类树，需要权限 template:cate:list
     */
    @PreAuthorize("hasAuthority('template:cate:list')")
    @GetMapping("/cate/tree")
    public List<CategoryTreeVO> cateTree() {

        return categoryService.tree();
    }

    /**
     * 所有分类
     */
    @PreAuthorize("hasAuthority('template:cate:list')")
    @GetMapping("/cate/all")
    public List<RingtoneCategory> cateAll() {

        return categoryService.lambdaQuery().orderByDesc(RingtoneCategory::getId).list();
    }


    /**
     * 新增或修改分类
     */
    @PreAuthorize("hasAuthority('template:cate:save')")
    @PostMapping("/cate/saveOrUpdate")
    public RingtoneCategory cateSaveOrUpdate(@RequestBody @Validated RingtoneCategory category) {

        if (category.getId() == null) {
            category.setCreateTime(LocalDateTime.now());
        }
        categoryService.saveOrUpdate(category);
        return category;
    }

    /**
     * 删除分类
     */
    @PreAuthorize("hasAuthority('template:cate:save')")
    @GetMapping("/cate/del/{id}")
    public boolean cateDel(@PathVariable Integer id) {

        // 判断该分类下是否有其他分类
        boolean cateExist = categoryService.lambdaQuery().eq(RingtoneCategory::getPid, id).exists();
        AppException.tnt(cateExist, "该分类下还有其他分类，不能删除");

        // 判断该分类下是否还有视频
        boolean exists = templateService.lambdaQuery().apply("FIND_IN_SET({0}, categories)", id).exists();
        AppException.tnt(exists, "该分类下还有模板，不能删除");
        return categoryService.removeById(id);
    }

    /**
     * 模板列表
     */
    @PreAuthorize("hasAuthority('template:template:list')")
    @PostMapping("/template/list")
    public IPage<RingtoneTemplateVO> templateList(@RequestBody CommonPageQuery query) {

        return templateService.listVO(query);

    }


    /**
     * 新增或修改分类
     */
    @PreAuthorize("hasAuthority('template:template:save')")
    @PostMapping("/template/saveOrUpdate")
    @Transactional(rollbackFor = RuntimeException.class)
    public RingtoneTemplate templateSaveOrUpdate(@RequestBody RingtoneTemplate template) {

        int categoryId = 0;

        if (StringUtils.hasText(template.getCategories())) {
            List<RingtoneCategory> categories = categoryService
                    .lambdaQuery()
                    .in(RingtoneCategory::getId, Arrays.asList(template.getCategories().split(",")))
                    .list();
            categoryId = categories.get(0).getPid();
            for (RingtoneCategory category : categories) {
                if (categoryId != category.getPid()) {
                    throw new AppException("请勿跨分类选择");
                }
            }
        }
        template.setCategory(categoryId);
        if (template.getCreateTime() == null) {
            template.setCreateTime(LocalDateTime.now());
            templateService.save(template);
        } else {
            templateService.updateById(template);
        }

        if (template.getIsDefault() == 1) {
            templateService.lambdaUpdate()
                    .ne(RingtoneTemplate::getId, template.getId())
                    .set(RingtoneTemplate::getIsDefault, 0)
                    .update();
        }

        return template;
    }

    /**
     * 删除模板
     */
    @PreAuthorize("hasAuthority('template:template:save')")
    @GetMapping("/template/del/{id}")
    public boolean templateDel(@PathVariable String id) {

        return templateService.removeById(id);
    }
}
