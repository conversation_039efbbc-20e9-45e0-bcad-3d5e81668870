package com.xy.admin.controller;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xy.admin.annotation.DataSource;
import com.xy.admin.entity.ProductCompanyInfo;
import com.xy.admin.service.ProductCompanyInfoService;
import com.xy.admin.vo.productCompanyInfo.ProductCompanyInfoVO;
import com.xy.admin.vo.productCompanyInfo.ProductCompanyInfoAddVO;
import com.xy.admin.vo.productCompanyInfo.ProductCompanyInfoEditVO;
import com.xy.base.starter.dto.CommonPageQuery;
import com.xy.base.core.response.Result;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;

import org.springframework.beans.factory.annotation.Autowired;

/**
* 公司表表控制层
*
* <AUTHOR>
* @since 2025/7/15 15:54
*/
@RestController
@RequestMapping("/productCompanyInfo")
public class ProductCompanyInfoController {
/**
* 服务对象
*/
    @Autowired
    private ProductCompanyInfoService productCompanyInfoService;

    /**
     * 列表查询
     */
    @PostMapping("/list")
    @DataSource("tertiary")
    public IPage<ProductCompanyInfoVO> userList(@RequestBody CommonPageQuery query) {

        Page<ProductCompanyInfo> page = query.buildPage();

        // 构建查询条件
        IPage<ProductCompanyInfo> entityPage;
        if (StringUtils.hasText(query.getKeyword())) {
            // 有关键字时进行模糊查询
            entityPage = productCompanyInfoService.lambdaQuery()
                    .and(wrapper -> wrapper
                            .like(ProductCompanyInfo::getCompanyName, query.getKeyword())
                            .or()
                            .like(ProductCompanyInfo::getCompanyId, query.getKeyword())
                    )
                    .orderByDesc(ProductCompanyInfo::getCreateTime)
                    .page(page);
        } else {
            // 无关键字时查询全部
            entityPage = productCompanyInfoService.lambdaQuery()
                    .orderByDesc(ProductCompanyInfo::getCreateTime)
                    .page(page);
        }

        // 转换为VO
        return entityPage.convert(entity -> {
            ProductCompanyInfoVO vo = new ProductCompanyInfoVO();
            BeanUtils.copyProperties(entity, vo);
            return vo;
        });
    }
    
    /**
     * 新增
     */
    @PostMapping("/add")
    @DataSource("tertiary")
    public Result<ProductCompanyInfoVO> add(@RequestBody @Validated ProductCompanyInfoAddVO addVO) {
        // 转换为实体类
        ProductCompanyInfo entity = new ProductCompanyInfo();
        BeanUtils.copyProperties(addVO, entity);

        boolean success = productCompanyInfoService.save(entity);
        if (success) {
            // 返回新增后的数据
            ProductCompanyInfoVO vo = new ProductCompanyInfoVO();
            BeanUtils.copyProperties(entity, vo);
            return Result.success(vo);
        } else {
            return Result.error(500, "新增失败");
        }
    }

    /**
     * 编辑
     */
    @PostMapping("/edit")
    @DataSource("tertiary")
    public Result<ProductCompanyInfoVO> edit(@RequestBody @Validated ProductCompanyInfoEditVO editVO) {
        // 转换为实体类
        ProductCompanyInfo entity = new ProductCompanyInfo();
        BeanUtils.copyProperties(editVO, entity);

        boolean success = productCompanyInfoService.updateById(entity);
        if (success) {
            // 返回更新后的数据
            ProductCompanyInfo updatedEntity = productCompanyInfoService.getById(editVO.getId());
            ProductCompanyInfoVO vo = new ProductCompanyInfoVO();
            BeanUtils.copyProperties(updatedEntity, vo);
            return Result.success(vo);
        } else {
            return Result.error(500, "更新失败");
        }
    }

    /**
     * 删除
     */
    @GetMapping("/delete/{id}")
    @DataSource("tertiary")
    public Result<String> delete(@PathVariable Long id) {
        // 只需要ID即可删除
        boolean success = productCompanyInfoService.removeById(id);
        if (success) {
            return Result.success("删除成功");
        } else {
            return Result.error(500, "删除失败");
        }
    }
}
