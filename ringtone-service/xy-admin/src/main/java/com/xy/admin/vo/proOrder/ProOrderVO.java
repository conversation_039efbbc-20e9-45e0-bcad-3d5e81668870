package com.xy.admin.vo.proOrder;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 推广订单展示VO
 * <AUTHOR>
 * @since 2025/8/1
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProOrderVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * ID
     */
    private Long id;
    
    /**
     * 渠道：推广商
     */
    private String channel;
    
    /**
     * 请求来源：app、app2、ks01
     */
    private String source;
    
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 0：未回传，1：已回传，逻辑回传标志
     */
    private Integer reportStatus;
    
    /**
     * 逻辑回传状态描述
     */
    private String reportStatusDesc;
    
    /**
     * 0：未回传，1：已回传，物理回传标志
     */
    private Integer huichuanStatus;
    
    /**
     * 物理回传状态描述
     */
    private String huichuanStatusDesc;
    
    /**
     * 推广的产品名，例如：gzgc_spcl_dxyy10
     */
    private String pro;
    
    /**
     * 回调字符串
     */
    private String callback;
    
    /**
     * 平台：ks, dy, llg, csj, gdt
     */
    private String platform;
    
    /**
     * 广告项目ID
     */
    private String projectid;
    
    /**
     * 广告ID
     */
    private String promotionid;
    
    /**
     * 广告主订单ID
     */
    private String orderId;
    
    /**
     * window.location.hostname
     */
    private String url;
    
    /**
     * UserAgent
     */
    private String ua;
    
    /**
     * APP名，一般来自对UA的解析
     */
    private String app;
    
    /**
     * 创建日期
     */
    private Date createTime;
    
    /**
     * 修改日期
     */
    private Date updateTime;
}
