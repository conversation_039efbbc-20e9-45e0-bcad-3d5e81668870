package com.xy.admin.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 开发对接状态枚举
 * 
 * <AUTHOR>
 * @since 2025/7/18
 */
@Getter
@AllArgsConstructor
public enum DevelopmentStatusEnum {
    
    /**
     * 1-排期中
     */
    SCHEDULING((byte) 1, "排期中"),
    
    /**
     * 2-计划中
     */
    PLANNING((byte) 2, "计划中"),
    
    /**
     * 3-开发中
     */
    DEVELOPING((byte) 3, "开发中"),
    
    /**
     * 4-测试中
     */
    TESTING((byte) 4, "测试中"),
    
    /**
     * 5-等页面
     */
    WAITING_PAGE((byte) 5, "等页面"),
    
    /**
     * 6-审核中
     */
    REVIEWING((byte) 6, "审核中"),
    
    /**
     * 7-联调中
     */
    INTEGRATING((byte) 7, "联调中"),
    
    /**
     * 8-等修复
     */
    WAITING_FIX((byte) 8, "等修复"),
    
    /**
     * 9-待测试
     */
    PENDING_TEST((byte) 9, "待测试"),
    
    /**
     * 10-已上线
     */
    ONLINE((byte) 10, "已上线");
    
    /**
     * 状态码
     */
    private final Byte code;
    
    /**
     * 状态描述
     */
    private final String description;
    
    /**
     * 根据状态码获取枚举
     * 
     * @param code 状态码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static DevelopmentStatusEnum getByCode(Byte code) {
        if (code == null) {
            return null;
        }
        for (DevelopmentStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 根据状态码获取描述
     * 
     * @param code 状态码
     * @return 状态描述，如果不存在则返回null
     */
    public static String getDescriptionByCode(Byte code) {
        DevelopmentStatusEnum status = getByCode(code);
        return status != null ? status.getDescription() : null;
    }
    
    /**
     * 验证状态码是否有效
     * 
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValidCode(Byte code) {
        return getByCode(code) != null;
    }
}
