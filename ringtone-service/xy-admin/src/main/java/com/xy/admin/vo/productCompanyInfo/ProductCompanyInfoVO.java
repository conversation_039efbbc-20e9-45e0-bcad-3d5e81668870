package com.xy.admin.vo.productCompanyInfo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 公司表响应VO
 * <AUTHOR>
 * @since 2025/7/16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductCompanyInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * ID
     */
    private Integer id;
    
    /**
     * 公司ID
     */
    private String companyId;
    
    /**
     * 公司名称
     */
    private String companyName;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
