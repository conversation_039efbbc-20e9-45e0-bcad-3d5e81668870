package com.xy.admin.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.admin.entity.ProductPromotionCompany;
import com.xy.admin.mapper.ProductPromotionCompanyMapper;
import com.xy.admin.service.ProductPromotionCompanyService;
import org.springframework.stereotype.Service;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 公司表(product_promotion_company)表服务层实现类
 * <AUTHOR>
 * @since 2025/7/15 15:44
 */
@Service
public class ProductPromotionCompanyServiceImpl extends ServiceImpl<ProductPromotionCompanyMapper, ProductPromotionCompany> implements ProductPromotionCompanyService {

    /**
     * 保存推广商信息，自动生成appId和secretKey
     */
    @Override
    public boolean save(ProductPromotionCompany entity) {
        // 如果appId为空，自动生成
        if (StrUtil.isBlank(entity.getAppId())) {
            entity.setAppId(generateUniqueAppId());
        }

        // 如果secretKey为空，自动生成
        if (StrUtil.isBlank(entity.getSecretKey())) {
            entity.setSecretKey(generateSecretKey());
        }

        return super.save(entity);
    }

    /**
     * 生成唯一的AppId
     * 格式：基于时间戳的数字ID，确保唯一性
     */
    private String generateUniqueAppId() {
        String appId;
        int attempts = 0;
        int maxAttempts = 100;

        do {
            // 生成基于时间戳的AppId，格式类似现有数据：100142, 100217, 2100001
            long timestamp = System.currentTimeMillis();
            // 取时间戳后6位并加上前缀，确保是7位数字
            String timestampSuffix = String.valueOf(timestamp % 1000000);
            appId = "21" + String.format("%05d", Integer.parseInt(timestampSuffix) % 100000);

            attempts++;
            if (attempts >= maxAttempts) {
                // 如果尝试次数过多，使用随机数生成
                appId = "21" + RandomUtil.randomNumbers(5);
                break;
            }
        } while (isAppIdExists(appId));

        return appId;
    }

    /**
     * 检查AppId是否已存在
     */
    private boolean isAppIdExists(String appId) {
        LambdaQueryWrapper<ProductPromotionCompany> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductPromotionCompany::getAppId, appId);
        return this.count(queryWrapper) > 0;
    }

    /**
     * 生成32位的secretKey
     * 使用MD5算法生成，基于当前时间戳和随机数
     */
    private String generateSecretKey() {
        try {
            // 组合时间戳、随机数和固定字符串作为原始数据
            String rawData = System.currentTimeMillis() + RandomUtil.randomString(16) + "XY_SECRET_KEY";

            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(rawData.getBytes());
            byte[] digest = md.digest();

            // 转换为32位十六进制字符串
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b & 0xff));
            }

            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            // 如果MD5不可用，使用备用方案
            return RandomUtil.randomString("0123456789abcdef", 32);
        }
    }
}

