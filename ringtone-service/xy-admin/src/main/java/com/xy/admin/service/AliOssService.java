package com.xy.admin.service;

import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @since 2023-12-26
 */
public interface AliOssService {

    /**
     * 上传文件到ali-oss
     *
     * @return java.lang.String
     * <AUTHOR>
     * @since 17:11 2022.09.20
     **/
    String uploadAliOss(MultipartFile multipartFile, Integer type);

    /**
     * 上传文件到本地磁盘
     */
    String uploadLocal(MultipartFile multipartFile, String folder);

    String getFolder(Integer type);

    String url2LocalPath(String url);

    String local2Url(String localPath);
}
