package com.xy.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xy.admin.entity.ProOrder;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xy.admin.vo.proOrder.ProOrderQueryVO;
import com.xy.admin.vo.proOrder.ProOrderVO;

/**
 * 针对表【pro_order】的数据库操作Service
 *
 * <AUTHOR>
 * @since 2025/8/1
 */
public interface ProOrderService extends IService<ProOrder> {

    /**
     * 多字段分页查询
     * @param queryVO 查询条件
     * @return 分页结果
     */
    IPage<ProOrderVO> queryPage(ProOrderQueryVO queryVO);

}
