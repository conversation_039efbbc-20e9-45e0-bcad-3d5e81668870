package com.xy.admin.service.impl;


import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.crypto.digest.MD5;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.CreateBucketRequest;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.xy.admin.service.AliOssService;
import com.xy.base.core.exception.AppException;
import com.xy.base.core.util.CommonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;

/**
 * <AUTHOR>
 * @since 2023-12-26
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AliOssServiceImpl implements AliOssService {

    @Value("${oss.endPoint}")
    private String endPoint;

    @Value("${oss.ak}")
    private String ak;

    @Value("${oss.secret}")
    private String secret;

    @Value("${oss.bucket}")
    private String bucket;

    @Value("${oss.access}")
    private String access;

    @Value("${oss.accessLocal}")
    private String accessLocal;

    @Value("${oss.local}")
    private String local;


    public OSS creatOssClient() {
        try {
            return new OSSClientBuilder().build(endPoint, ak, secret);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String uploadAliOss(MultipartFile multipartFile, Integer type) {
        OSS ossClient = creatOssClient();
        AppException.tnt(null == ossClient, "ali-oss连接错误");
        try {
            String folder = getFolder(type);
            String fileName = multipartFile.getOriginalFilename();
            String finalFileName = MD5.create().digestHex(multipartFile.getBytes()) + "." + FileUtil.extName(fileName);
            String objectName = folder + finalFileName;

            // 如果是模板视频，本地保存一份
            uploadLocal(multipartFile, folder);

            // 判断存储空间是否存在
            boolean bucketExist = ossClient.doesBucketExist(bucket);
            if (!bucketExist) {
                CreateBucketRequest createBucketRequest = new CreateBucketRequest(bucket);
                createBucketRequest.setCannedACL(CannedAccessControlList.PublicRead);
                ossClient.createBucket(createBucketRequest);
            }
            // 判断文件是否存在
            boolean objectExist = ossClient.doesObjectExist(bucket, objectName);
            if (objectExist) {
                return getAliOssAccessPath(objectName);
            }
            // 上传部分
            byte[] fileBytes = multipartFile.getBytes();

            ByteArrayInputStream inputStream = new ByteArrayInputStream(fileBytes);
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, objectName, inputStream);
            // 设置文件元信息，包含权限
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setObjectAcl(CannedAccessControlList.PublicRead);
            metadata.setContentType(multipartFile.getContentType());
            putObjectRequest.setMetadata(metadata);
            ossClient.putObject(putObjectRequest);
            inputStream.close();
            return getAliOssAccessPath(objectName);
        } catch (Exception e) {
            log.error("上传文件到ali-oss错误信息>>>>>:{}", e.getMessage());
            e.printStackTrace();
        } finally {
            ossClient.shutdown();
        }
        throw new AppException("上传失败，请稍后重试");
    }

    @Override
    public String uploadLocal(MultipartFile multipartFile, String folder) {
        try {
            String finalDir = local + folder;
            String finalFileName = MD5.create().digestHex(multipartFile.getBytes()) + "." + FileUtil.extName(multipartFile.getOriginalFilename());
            String finalFile = finalDir + finalFileName;

            if (!FileUtil.exist(finalDir)) {
                FileUtil.mkdir(finalDir);
            }
            FileUtil.writeBytes(multipartFile.getBytes(), finalFile);

            return accessLocal + folder + finalFileName;
        } catch (Exception e) {
            log.error("上传文件到本地错误信息>>>>>" + e.getMessage(), e);
        }
        throw new AppException("上传失败，请稍后重试");

    }

    @Override
    public String getFolder(Integer type) {
        String folder;
        int tp = type == null ? 0 : type;
        String date = CommonUtils.getDateStr("yyyyMMdd") + "/";
        switch (tp) {
            case 1:
                folder = "templates/" + date;
                break;
            case 2:
                folder = "user/" + date;
                break;
            case 3:
                folder = "copyright/";
                break;
            case 4:
                folder = "diy/";
                break;
            default:
                folder = "other/" + date;
                break;
        }
        return folder;
    }

    public String getAliOssAccessPath(String objectName) {
        StrBuilder sb = new StrBuilder();
        sb.append(access)
                .append("/").append(objectName);
        return sb.toString();
    }

    @Override
    public String url2LocalPath(String url) {
        return local + url.replace(accessLocal, "");
    }

    @Override
    public String local2Url(String localPath) {
        return accessLocal + localPath.replace(local, "");

    }
}
