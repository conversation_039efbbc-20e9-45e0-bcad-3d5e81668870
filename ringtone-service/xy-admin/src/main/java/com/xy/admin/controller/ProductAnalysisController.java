package com.xy.admin.controller;

import com.xy.admin.annotation.DataSource;
import com.xy.admin.service.ProductAnalysisService;
import com.xy.admin.vo.productAnalysis.ProductAnalysisQueryVO;
import com.xy.admin.vo.productAnalysis.ProductAnalysisResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 产品分析控制器
 * <AUTHOR>
 * @since 2025/08/04
 */
@Slf4j
@RestController
@RequestMapping("/product_analysis")
public class ProductAnalysisController {

    @Autowired
    private ProductAnalysisService productAnalysisService;

    /**
     * 获取产品分析数据
     * @param queryVO 查询条件
     * @return 分析结果
     */
    @PostMapping("/analysis")
    @DataSource("tertiary")
    public ProductAnalysisResultVO getAnalysisData(@Valid @RequestBody ProductAnalysisQueryVO queryVO) {
        log.info("收到产品分析请求，查询条件: {}", queryVO);
        
        ProductAnalysisResultVO result = productAnalysisService.getAnalysisData(queryVO);
        
        log.info("产品分析请求处理完成");
        return result;
    }
}