package com.xy.admin.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xy.base.starter.config.LocalJacksonTypeHandler;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户diy数据
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@TableName(value = "diy_user", autoResultMap = true)
@Data
public class DiyUser implements Serializable {

    /**
     * 
	 * @NeverNull
     */
    @TableId
    private Long id;

    /**
     * 
	 * @NeverNull
     */
    private Integer userId;
    /**
     * 
     */
    private String userPhone;
    /**
     * diy类型，1视频，2图文
	 * @NeverNull
     */
    private Integer type;
    /**
     * 自定义上传视频地址
     */
    private String video;
    /**
     * 自定义文本
     */
    private String text;
    /**
     * 音色
     */
    private String textTimbre;
    /**
     * 自定义图片（也可能是选择的素材）
     */
    @TableField(typeHandler =LocalJacksonTypeHandler.class)
    private String[] pics;
    /**
     * 状态 0未审核 1审核通过，合成中 2合成并上传完成 -1审核拒绝
	 * @NeverNull
     */
    private Integer status;
    /**
     * 拒绝原因
     */
    private String reason;
    /**
     * 音色
     */
    private String targetVoice;
    /**
     * 音色
     */
    private String targetVideo;
    /**
     * 
     */
    private LocalDateTime createTime;
    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
