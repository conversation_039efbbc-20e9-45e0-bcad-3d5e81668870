package com.xy.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xy.admin.annotation.DataSource;
import com.xy.admin.service.ProOrderService;
import com.xy.admin.vo.proOrder.ProOrderQueryVO;
import com.xy.admin.vo.proOrder.ProOrderVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 推广订单控制层
 * <AUTHOR>
 * @since 2025/8/1 17:07
 */
@RestController
@RequestMapping("/proOrder")
public class ProOrderController {

    @Autowired
    private ProOrderService proOrderService;

    /**
     * 列表查询 - 支持多字段查询、分页和排序
     */
    @PostMapping("/list")
    @DataSource("tertiary")
    public IPage<ProOrderVO> list(@Valid @RequestBody ProOrderQueryVO queryVO) {
        return proOrderService.queryPage(queryVO);
    }
}
