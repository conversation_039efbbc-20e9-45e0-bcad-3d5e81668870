package com.xy.admin.vo.proOrder;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xy.base.starter.dto.CommonPageQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 推广订单查询VO
 * <AUTHOR>
 * @since 2025/8/1
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ProOrderQueryVO extends CommonPageQuery {
    private static final long serialVersionUID = 1L;
    
    /**
     * 渠道：推广商
     */
    private String channel;
    
    /**
     * 请求来源：app、app2、ks01
     */
    private String source;
    
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 0：未回传，1：已回传，逻辑回传标志
     */
    private Integer reportStatus;
    
    /**
     * 0：未回传，1：已回传，物理回传标志
     */
    private Integer huichuanStatus;
    
    /**
     * 推广的产品名，例如：gzgc_spcl_dxyy10
     */
    private String pro;
    
    /**
     * 回调字符串
     */
    private String callback;
    
    /**
     * 平台：ks, dy, llg, csj, gdt
     */
    private String platform;
    
    /**
     * 广告项目ID
     */
    private String projectid;
    
    /**
     * 广告ID
     */
    private String promotionid;
    
    /**
     * 广告主订单ID
     */
    private String orderId;
    
    /**
     * window.location.hostname
     */
    private String url;
    
    /**
     * UserAgent
     */
    private String ua;
    
    /**
     * APP名，一般来自对UA的解析
     */
    private String app;
    
    /**
     * 创建时间范围查询 - 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeStart;
    
    /**
     * 创建时间范围查询 - 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;
    
    /**
     * 修改时间范围查询 - 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTimeStart;
    
    /**
     * 修改时间范围查询 - 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTimeEnd;
}
