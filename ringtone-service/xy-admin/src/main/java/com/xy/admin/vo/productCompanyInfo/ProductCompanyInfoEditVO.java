package com.xy.admin.vo.productCompanyInfo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 公司表编辑请求VO
 * <AUTHOR>
 * @since 2025/7/16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductCompanyInfoEditVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * ID
     */
    @NotNull(message = "ID不能为null")
    private Integer id;
    
    /**
     * 公司ID
     */
    @Size(max = 255, message = "公司ID最大长度要小于 255")
    private String companyId;
    
    /**
     * 公司名称
     */
    @Size(max = 255, message = "公司名称最大长度要小于 255")
    private String companyName;
}
