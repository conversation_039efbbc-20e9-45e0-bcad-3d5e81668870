package com.xy.admin.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2024-02-20
 */
@TableName(value = "ch_source")
@Data
public class ChSource implements Serializable {

    /**
     * 来源代码
     */
    @TableId
    @NotNull
    private String source;
    /**
     * 来源名称
     */
    @NotNull
    private String name;
    /**
     * 对应的媒体平台
     */
    private String platform;
    /**
     * 渠道商代码
     */
    private String channel;
    /**
     * 落地页文件对应的url
     */
    private String url;
    /**
     * 回传参数，支持多个，多个之间用半角逗号间隔
     */
    private String paramName;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
