package com.xy.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName channel_report
 */
@TableName(value ="channel_report")
@Data
public class ChannelReport implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 渠道：推广商
     */
    private String channel;

    /**
     * 请求来源：app、app2、ks01
     */
    private String source;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 0：未回传，1：已回传，逻辑回传标志
     */
    private Integer reportStatus;

    /**
     * 0：未回传，1：已回传，物理回传标志
     */
    private Integer huichuanStatus;

    /**
     * promote商
     */
    private String pro;

    /**
     * 回调字符串
     */
    private String callback;

    /**
     * 平台：ks, dy, llg, csj, gdt
     */
    private String platform;

    /**
     * 引导用户进来时素材对应的铃音id
     */
    private String ring;

    /**
     * remoteHost
     */
    private String url;

    /**
     * 页面携带进来的所有参数
     */
    private String params;

    /**
     * 创建日期
     */
    private Date createTime;

    /**
     * 修改日期
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ChannelReport other = (ChannelReport) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getChannel() == null ? other.getChannel() == null : this.getChannel().equals(other.getChannel()))
            && (this.getSource() == null ? other.getSource() == null : this.getSource().equals(other.getSource()))
            && (this.getPhone() == null ? other.getPhone() == null : this.getPhone().equals(other.getPhone()))
            && (this.getReportStatus() == null ? other.getReportStatus() == null : this.getReportStatus().equals(other.getReportStatus()))
            && (this.getHuichuanStatus() == null ? other.getHuichuanStatus() == null : this.getHuichuanStatus().equals(other.getHuichuanStatus()))
            && (this.getPro() == null ? other.getPro() == null : this.getPro().equals(other.getPro()))
            && (this.getCallback() == null ? other.getCallback() == null : this.getCallback().equals(other.getCallback()))
            && (this.getPlatform() == null ? other.getPlatform() == null : this.getPlatform().equals(other.getPlatform()))
            && (this.getRing() == null ? other.getRing() == null : this.getRing().equals(other.getRing()))
            && (this.getUrl() == null ? other.getUrl() == null : this.getUrl().equals(other.getUrl()))
            && (this.getParams() == null ? other.getParams() == null : this.getParams().equals(other.getParams()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getChannel() == null) ? 0 : getChannel().hashCode());
        result = prime * result + ((getSource() == null) ? 0 : getSource().hashCode());
        result = prime * result + ((getPhone() == null) ? 0 : getPhone().hashCode());
        result = prime * result + ((getReportStatus() == null) ? 0 : getReportStatus().hashCode());
        result = prime * result + ((getHuichuanStatus() == null) ? 0 : getHuichuanStatus().hashCode());
        result = prime * result + ((getPro() == null) ? 0 : getPro().hashCode());
        result = prime * result + ((getCallback() == null) ? 0 : getCallback().hashCode());
        result = prime * result + ((getPlatform() == null) ? 0 : getPlatform().hashCode());
        result = prime * result + ((getRing() == null) ? 0 : getRing().hashCode());
        result = prime * result + ((getUrl() == null) ? 0 : getUrl().hashCode());
        result = prime * result + ((getParams() == null) ? 0 : getParams().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", channel=").append(channel);
        sb.append(", source=").append(source);
        sb.append(", phone=").append(phone);
        sb.append(", reportStatus=").append(reportStatus);
        sb.append(", huichuanStatus=").append(huichuanStatus);
        sb.append(", pro=").append(pro);
        sb.append(", callback=").append(callback);
        sb.append(", platform=").append(platform);
        sb.append(", ring=").append(ring);
        sb.append(", url=").append(url);
        sb.append(", params=").append(params);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}