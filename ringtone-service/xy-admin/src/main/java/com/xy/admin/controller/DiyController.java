package com.xy.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xy.admin.dto.DiyUserAuditDTO;
import com.xy.admin.entity.DiySysRes;
import com.xy.admin.entity.DiyUser;
import com.xy.admin.service.DiySysResService;
import com.xy.admin.service.DiyUserService;
import com.xy.base.core.constant.CommonConsts;
import com.xy.base.core.exception.AppException;
import com.xy.base.core.util.ThreadUtil;
import com.xy.base.starter.dto.CommonPageQuery;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

/**
 * 客户自定义铃声
 *
 * <AUTHOR>
 * @since 2025/1/3 17:58
 */
@RestController
@RequestMapping("/diy")
@RequiredArgsConstructor
public class DiyController {

    private final DiySysResService sysResService;
    private final DiyUserService diyUserService;

    /**
     * 素材列表
     */
    @PostMapping("/res/list")
    public IPage<DiySysRes> resList(@RequestBody CommonPageQuery query) {

        Page<DiySysRes> page = query.buildPage();
        return sysResService.lambdaQuery()
                .orderByDesc(DiySysRes::getId)
                .page(page);
    }

    /**
     * 新增素材
     */
    @PostMapping("/res/add")
    public DiySysRes resAdd(@RequestBody @Validated DiySysRes res) {
        sysResService.save(res);
        return res;
    }

    /**
     * 删除渠道商信息
     */
    @GetMapping("/res/del/{id}")
    public boolean resDel(@PathVariable Integer id) {

        return sysResService.removeById(id);
    }

    /**
     * 用户数据
     */
    @PostMapping("/user/list")
    public IPage<DiyUser> userList(@RequestBody CommonPageQuery query) {

        Page<DiyUser> page = query.buildPage();
        return diyUserService.lambdaQuery()
                .like(StringUtils.hasText(query.getKeyword()), DiyUser::getUserPhone, query.getKeyword())
                .eq(query.getType().equals(0), DiyUser::getStatus, 0)
                .eq(query.getType().equals(-1), DiyUser::getStatus, -1)
                .ge(query.getType().equals(1), DiyUser::getStatus, 1)
                .orderByDesc(DiyUser::getId)
                .page(page);
    }

    /**
     * 审核
     */
    @PostMapping("/user/audit")
    public DiyUser userAudit(@RequestBody @Validated DiyUserAuditDTO audit) {

        DiyUser diy = diyUserService.getById(audit.getId());
        AppException.tnt(diy == null, "参数错误");

        AppException.tnt(!diy.getStatus().equals(CommonConsts.ZERO), "数据已审核");

        diy.setStatus(audit.getAudit());
        diy.setAuditTime(LocalDateTime.now());
        diy.setReason(audit.getReason());

        diyUserService.updateById(diy);

        if(audit.getAudit().equals(CommonConsts.ONE)){
            ThreadUtil.execute("合成视频", (e) -> {
                diyUserService.compose(diy);
            });
        }
        return diy;
    }

}
