package com.xy.admin.vo.customerSource;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 客户来源查询响应VO
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerSourceResVO {

	/**
	 * 手机号
	 */
	private String phone;

	/**
	 * 推广商名称
	 */
	private String channelName;

	/**
	 * 推广商编码
	 */
	private String channel;

	/**
	 * 来源
	 */
	private String source;

	/**
	 * APP信息
	 */
	private String app;

	/**
	 * 点击ID
	 */
	private String clickid;

	/**
	 * 回传状态：0-未回传，1-已回传
	 */
	private Integer huichuanStatus;

	/**
	 * 产品信息
	 */
	private String pro;

	/**
	 * 平台：ks, dy, llg, csj, gdt
	 */
	private String platform;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	/**
	 * 来源URL
	 */
	private String rawUrl;

	/**
	 * 来源链路追踪列表
	 */
	private List<CustomerSourceTraceVO> traceList;

	/**
	 * 来源链路追踪VO
	 */
	@Data
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class CustomerSourceTraceVO {
		/**
		 * 手机号
		 */
		private String phone;

		/**
		 * 推广商名称
		 */
		private String channelName;

		/**
		 * 来源
		 */
		private String source;

		/**
		 * APP信息
		 */
		private String app;

		/**
		 * 点击ID
		 */
		private String clickid;

		/**
		 * 创建时间
		 */
		@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
		private Date createTime;

		/**
		 * 来源URL
		 */
		private String rawUrl;

		/**
		 * 是否是最终来源
		 */
		private Boolean isFinal;
	}
}