package com.xy.admin.vo.customerSource;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 客户来源查询请求VO
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerSourceQueryReqVO {

	/**
	 * 手机号
	 */
	@NotBlank(message = "手机号不能为空")
	@Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
	private String phone;

	/**
	 * 项目类型：zsgl、nmch、other
	 */
	private String pro = "zsgl";
}