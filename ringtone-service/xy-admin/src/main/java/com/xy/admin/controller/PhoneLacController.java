package com.xy.admin.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xy.admin.annotation.DataSource;
import com.xy.admin.entity.PhoneLac;
import com.xy.admin.service.PhoneLacService;
import com.xy.admin.vo.phoneLac.PhoneLacAddVO;
import com.xy.admin.vo.phoneLac.PhoneLacEditVO;
import com.xy.admin.vo.phoneLac.PhoneLacQueryVO;
import com.xy.admin.vo.phoneLac.PhoneLacVO;
import com.xy.base.core.response.Result;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 手机号段控制层
 *
 * <AUTHOR>
 * @since 2025/7/18
 */
@RestController
@RequestMapping("/phone_lac")
public class PhoneLacController {

    @Autowired
    private PhoneLacService phoneLacService;

    /**
     * 手机号段列表查询接口
     * 支持多字段查询、分页和排序
     */
    @PostMapping("/list")
    @DataSource("tertiary")
    public IPage<PhoneLacVO> list(@Valid @RequestBody PhoneLacQueryVO queryVO) {
        return phoneLacService.queryPage(queryVO);
    }

    /**
     * 单个手机号段查询接口（通过号段）
     */
    @GetMapping("/{seg}")
    @DataSource("tertiary")
    public Result<PhoneLacVO> getBySeg(@PathVariable String seg) {
        PhoneLac entity = phoneLacService.getById(seg);
        if (entity == null) {
            return Result.error(404, "手机号段不存在");
        }

        PhoneLacVO vo = new PhoneLacVO();
        BeanUtils.copyProperties(entity, vo);
        return Result.success(vo);
    }

    /**
     * 手机号段新增接口
     */
    @PostMapping
    @DataSource("tertiary")
    public Result<String> add(@Valid @RequestBody PhoneLacAddVO addVO) {
        PhoneLac entity = new PhoneLac();
        BeanUtils.copyProperties(addVO, entity);
        if (StrUtil.isBlank(entity.getSeg())) {
            return Result.error(400, "号段不能为空");
        }
        boolean success = phoneLacService.save(entity);
        if (success) {
            return Result.success("新增成功");
        } else {
            return Result.error(500, "新增失败");
        }
    }

    /**
     * 手机号段修改接口
     */
    @PutMapping("/{seg}")
    @DataSource("tertiary")
    public Result<String> update(@PathVariable String seg, @Valid @RequestBody PhoneLacEditVO editVO) {
        // 确保号段一致
        editVO.setSeg(seg);
        if (StrUtil.isBlank(editVO.getSeg())) {
            return Result.error(400, "号段不能为空");
        }

        PhoneLac entity = new PhoneLac();
        BeanUtils.copyProperties(editVO, entity);

        boolean success = phoneLacService.updateById(entity);
        if (success) {
            return Result.success("修改成功");
        } else {
            return Result.error(500, "修改失败");
        }
    }

    /**
     * 手机号段删除接口
     */
    @DeleteMapping("/{seg}")
    @DataSource("tertiary")
    public Result<String> delete(@PathVariable String seg) {
        boolean success = phoneLacService.removeById(seg);
        if (success) {
            return Result.success("删除成功");
        } else {
            return Result.error(500, "删除失败");
        }
    }
}
