package com.xy.admin.vo.logApi;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * API日志展示VO
 * <AUTHOR>
 * @since 2025/7/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LogApiVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * ID
     */
    private Long id;
    
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 日志类型，0主调，1被调
     */
    private Integer type;
    
    /**
     * URL
     */
    private String url;
    
    /**
     * 请求IP
     */
    private String ip;
    
    /**
     * 方法类型
     */
    private String method;
    
    /**
     * 代码位置
     */
    private String position;
    
    /**
     * 请求内容
     */
    private String request;
    
    /**
     * 回复内容
     */
    private String response;
    
    /**
     * 花费时间，单位毫秒
     */
    private Integer timing;
    
    /**
     * 是否发生异常
     */
    private Boolean exception;
    
    /**
     * 异常名称
     */
    private String exceptionName;
    
    /**
     * 异常堆栈
     */
    private String exceptionTrace;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
