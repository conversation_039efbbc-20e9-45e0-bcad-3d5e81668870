package com.xy.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xy.admin.entity.LogApi;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xy.admin.vo.logApi.LogApiQueryVO;
import com.xy.admin.vo.logApi.LogApiVO;

/**
 * 针对表【log_api】的数据库操作Service
 *
 * <AUTHOR>
 * @since 2023-12-01
 */
public interface LogApiService extends IService<LogApi> {

    /**
     * 多字段分页查询
     * @param queryVO 查询条件
     * @return 分页结果
     */
    IPage<LogApiVO> queryPage(LogApiQueryVO queryVO);

}
