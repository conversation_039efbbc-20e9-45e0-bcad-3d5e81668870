package com.xy.admin.service.impl;

import com.xy.admin.dto.productAnalysis.ProductAnalysisStatsDTO;
import com.xy.admin.mapper.ProductAnalysisMapper;
import com.xy.admin.service.ProductAnalysisService;
import com.xy.admin.vo.productAnalysis.ProductAnalysisQueryVO;
import com.xy.admin.vo.productAnalysis.ProductAnalysisResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品分析服务实现类
 * <AUTHOR>
 * @since 2025/08/04
 */
@Slf4j
@Service
public class ProductAnalysisServiceImpl implements ProductAnalysisService {

    @Autowired
    private ProductAnalysisMapper productAnalysisMapper;

    @Override
    public ProductAnalysisResultVO getAnalysisData(ProductAnalysisQueryVO queryVO) {
        log.info("开始获取产品分析数据，查询条件: {}", queryVO);

        // 获取概览统计数据
        ProductAnalysisStatsDTO overviewStats = productAnalysisMapper.getOverviewStats(queryVO);
        
        // 获取按日期分组的统计数据
        List<ProductAnalysisStatsDTO> dailyStats = productAnalysisMapper.getDailyStats(queryVO);

        // 构建返回结果
        ProductAnalysisResultVO result = ProductAnalysisResultVO.builder()
                .overviewStats(buildOverviewStats(overviewStats))
                .orderTypeChart(buildOrderTypeChart(dailyStats))
                .successRateChart(buildSuccessRateChart(dailyStats))
                .build();

        log.info("产品分析数据获取完成，概览统计: {}", result.getOverviewStats());
        return result;
    }

    /**
     * 构建概览统计数据
     */
    private ProductAnalysisResultVO.OverviewStats buildOverviewStats(ProductAnalysisStatsDTO stats) {
        if (stats == null) {
            return ProductAnalysisResultVO.OverviewStats.builder()
                    .totalCount(0L)
                    .smsCount(0L)
                    .orderCount(0L)
                    .orderSuccessCount(0L)
                    .orderSuccessRate(BigDecimal.ZERO)
                    .productConversionRate(BigDecimal.ZERO)
                    .build();
        }

        // 计算订单提交成功率（办理成功数/订单提交数）
        BigDecimal orderSuccessRate = BigDecimal.ZERO;
        if (stats.getOrderCount() != null && stats.getOrderCount() > 0) {
            orderSuccessRate = BigDecimal.valueOf(stats.getOrderSuccessCount() == null ? 0 : stats.getOrderSuccessCount())
                    .divide(BigDecimal.valueOf(stats.getOrderCount()), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
        }

        // 计算产品转化率（订单提交数/短信发送数）
        BigDecimal productConversionRate = BigDecimal.ZERO;
        if (stats.getSmsCount() != null && stats.getSmsCount() > 0) {
            productConversionRate = BigDecimal.valueOf(stats.getOrderCount() == null ? 0 : stats.getOrderCount())
                    .divide(BigDecimal.valueOf(stats.getSmsCount()), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
        }

        return ProductAnalysisResultVO.OverviewStats.builder()
                .totalCount(stats.getTotalCount() == null ? 0L : stats.getTotalCount())
                .smsCount(stats.getSmsCount() == null ? 0L : stats.getSmsCount())
                .orderCount(stats.getOrderCount() == null ? 0L : stats.getOrderCount())
                .orderSuccessCount(stats.getOrderSuccessCount() == null ? 0L : stats.getOrderSuccessCount())
                .orderSuccessRate(orderSuccessRate)
                .productConversionRate(productConversionRate)
                .build();
    }

    /**
     * 构建订单类型图表数据
     */
    private List<ProductAnalysisResultVO.OrderTypeChartData> buildOrderTypeChart(List<ProductAnalysisStatsDTO> dailyStats) {
        if (CollectionUtils.isEmpty(dailyStats)) {
            return Arrays.asList();
        }

        return dailyStats.stream()
                .map(stats -> ProductAnalysisResultVO.OrderTypeChartData.builder()
                        .date(stats.getDate())
                        .smsCount(stats.getSmsCount() == null ? 0L : stats.getSmsCount())
                        .orderCount(stats.getOrderCount() == null ? 0L : stats.getOrderCount())
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 构建成功率图表数据
     */
    private List<ProductAnalysisResultVO.SuccessRateChartData> buildSuccessRateChart(List<ProductAnalysisStatsDTO> dailyStats) {
        if (CollectionUtils.isEmpty(dailyStats)) {
            return Arrays.asList();
        }

        return dailyStats.stream()
                .map(stats -> {
                    // 计算当日订单提交成功率
                    BigDecimal orderSuccessRate = BigDecimal.ZERO;
                    if (stats.getOrderCount() != null && stats.getOrderCount() > 0) {
                        orderSuccessRate = BigDecimal.valueOf(stats.getOrderSuccessCount() == null ? 0 : stats.getOrderSuccessCount())
                                .divide(BigDecimal.valueOf(stats.getOrderCount()), 4, RoundingMode.HALF_UP)
                                .multiply(BigDecimal.valueOf(100));
                    }

                    // 计算当日产品转化率
                    BigDecimal productConversionRate = BigDecimal.ZERO;
                    if (stats.getSmsCount() != null && stats.getSmsCount() > 0) {
                        productConversionRate = BigDecimal.valueOf(stats.getOrderCount() == null ? 0 : stats.getOrderCount())
                                .divide(BigDecimal.valueOf(stats.getSmsCount()), 4, RoundingMode.HALF_UP)
                                .multiply(BigDecimal.valueOf(100));
                    }

                    return ProductAnalysisResultVO.SuccessRateChartData.builder()
                            .date(stats.getDate())
                            .orderSuccessRate(orderSuccessRate)
                            .productConversionRate(productConversionRate)
                            .build();
                })
                .collect(Collectors.toList());
    }
}