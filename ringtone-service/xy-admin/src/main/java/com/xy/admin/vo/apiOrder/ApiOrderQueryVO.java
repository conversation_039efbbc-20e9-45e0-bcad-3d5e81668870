package com.xy.admin.vo.apiOrder;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xy.base.starter.dto.CommonPageQuery;
import lombok.*;

import java.time.LocalDateTime;

/**
 * API订单查询VO
 * <AUTHOR>
 * @since 2025/7/30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ApiOrderQueryVO extends CommonPageQuery {
    private static final long serialVersionUID = 1L;

    /**
     * 渠道编号
     */
    private String channelNo;
    
    /**
     * 产品编号
     */
    private String productNo;
    
    /**
     * 手机号码
     */
    private String mobileNo;
    
    /**
     * 订单ID
     */
    private String orderId;
    
    /**
     * 订单状态码
     */
    private String orderStatus;
    
    /**
     * 外部订单状态
     */
    private String outOrderStatus;
    
    /**
     * 外部订单ID
     */
    private String outOrderId;
    
    /**
     * 操作类型：SMS-短信发送，ORDER-订单提交
     */
    private String operationType;

    /**
     * 应用包名
     */
    private String appPackage;
    
    /**
     * 应用名称
     */
    private String appName;
    
    /**
     * 平台名称
     */
    private String platform;
    
    /**
     * 响应
     */
    private String remark;
    
    /**
     * 服务器
     */
    private String server;
    
    /**
     * 创建时间范围查询 - 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeStart;
    
    /**
     * 创建时间范围查询 - 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeEnd;
    
    /**
     * 更新时间范围查询 - 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTimeStart;
    
    /**
     * 更新时间范围查询 - 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTimeEnd;
    
    /**
     * 排序字段
     */
    private String sortField;
    
    /**
     * 排序方向：asc/desc
     */
    private String sortOrder;
}
