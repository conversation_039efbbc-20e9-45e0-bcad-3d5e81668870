package com.xy.admin.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 产品状态枚举
 * 
 * <AUTHOR>
 * @since 2025/7/18
 */
@Getter
@AllArgsConstructor
public enum ProductStatusEnum {
    
    /**
     * 1-待接入
     */
    PENDING_ACCESS((byte) 1, "待接入"),
    
    /**
     * 2-已暂停
     */
    PAUSED((byte) 2, "已暂停"),
    
    /**
     * 3-已下线
     */
    OFFLINE((byte) 3, "已下线"),
    
    /**
     * 4-可推广
     */
    PROMOTABLE((byte) 4, "可推广");
    
    /**
     * 状态码
     */
    private final Byte code;
    
    /**
     * 状态描述
     */
    private final String description;
    
    /**
     * 根据状态码获取枚举
     * 
     * @param code 状态码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ProductStatusEnum getByCode(Byte code) {
        if (code == null) {
            return null;
        }
        for (ProductStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 根据状态码获取描述
     * 
     * @param code 状态码
     * @return 状态描述，如果不存在则返回null
     */
    public static String getDescriptionByCode(Byte code) {
        ProductStatusEnum status = getByCode(code);
        return status != null ? status.getDescription() : null;
    }
    
    /**
     * 验证状态码是否有效
     * 
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValidCode(Byte code) {
        return getByCode(code) != null;
    }
}
