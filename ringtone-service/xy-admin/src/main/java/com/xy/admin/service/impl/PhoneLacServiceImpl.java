package com.xy.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.admin.entity.PhoneLac;
import com.xy.admin.mapper.PhoneLacMapper;
import com.xy.admin.service.PhoneLacService;
import com.xy.admin.vo.phoneLac.PhoneLacQueryVO;
import com.xy.admin.vo.phoneLac.PhoneLacVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 手机号段服务实现类
 * <AUTHOR>
 * @description 针对表【phone_lac】的数据库操作Service实现
 * @createDate 2024-07-17 10:57:17
 */
@Service
public class PhoneLacServiceImpl extends ServiceImpl<PhoneLacMapper, PhoneLac>
    implements PhoneLacService{

    @Override
    public IPage<PhoneLacVO> queryPage(PhoneLacQueryVO queryVO) {
        // 构建查询条件
        LambdaQueryWrapper<PhoneLac> wrapper = new LambdaQueryWrapper<>();

        // 号段查询
        if (StrUtil.isNotBlank(queryVO.getSeg())) {
            wrapper.like(PhoneLac::getSeg, queryVO.getSeg());
        }

        // 省份查询
        if (StrUtil.isNotBlank(queryVO.getProvince())) {
            wrapper.like(PhoneLac::getProvince, queryVO.getProvince());
        }

        // 城市查询
        if (StrUtil.isNotBlank(queryVO.getCity())) {
            wrapper.like(PhoneLac::getCity, queryVO.getCity());
        }

        // 运营商查询
        if (StrUtil.isNotBlank(queryVO.getCarrier())) {
            wrapper.like(PhoneLac::getCarrier, queryVO.getCarrier());
        }

        // 虚拟运营商查询
        if (StrUtil.isNotBlank(queryVO.getVcarrier())) {
            wrapper.like(PhoneLac::getVcarrier, queryVO.getVcarrier());
        }

        // 创建时间范围查询
        if (queryVO.getCreateTimeStart() != null) {
            wrapper.ge(PhoneLac::getCreateTime, queryVO.getCreateTimeStart());
        }
        if (queryVO.getCreateTimeEnd() != null) {
            wrapper.le(PhoneLac::getCreateTime, queryVO.getCreateTimeEnd());
        }

        // 更新时间范围查询
        if (queryVO.getUpdateTimeStart() != null) {
            wrapper.ge(PhoneLac::getUpdateTime, queryVO.getUpdateTimeStart());
        }
        if (queryVO.getUpdateTimeEnd() != null) {
            wrapper.le(PhoneLac::getUpdateTime, queryVO.getUpdateTimeEnd());
        }

        // 排序处理
        if (StrUtil.isNotBlank(queryVO.getOrderField()) && StrUtil.isNotBlank(queryVO.getOrderType())) {
            boolean isAsc = "asc".equalsIgnoreCase(queryVO.getOrderType());
            switch (queryVO.getOrderField()) {
                case "seg":
                    wrapper.orderBy(true, isAsc, PhoneLac::getSeg);
                    break;
                case "province":
                    wrapper.orderBy(true, isAsc, PhoneLac::getProvince);
                    break;
                case "city":
                    wrapper.orderBy(true, isAsc, PhoneLac::getCity);
                    break;
                case "carrier":
                    wrapper.orderBy(true, isAsc, PhoneLac::getCarrier);
                    break;
                case "createTime":
                    wrapper.orderBy(true, isAsc, PhoneLac::getCreateTime);
                    break;
                case "updateTime":
                    wrapper.orderBy(true, isAsc, PhoneLac::getUpdateTime);
                    break;
                default:
                    wrapper.orderByDesc(PhoneLac::getCreateTime);
                    break;
            }
        } else {
            // 默认按创建时间倒序
            wrapper.orderByDesc(PhoneLac::getCreateTime);
        }

        // 分页查询
        Page<PhoneLac> page = queryVO.buildPage();
        IPage<PhoneLac> pageResult = this.page(page, wrapper);

        // 转换为VO
        List<PhoneLacVO> voList = pageResult.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        // 构建返回结果
        Page<PhoneLacVO> voPage = new Page<>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal());
        voPage.setRecords(voList);

        return voPage;
    }

    /**
     * 实体转VO
     */
    private PhoneLacVO convertToVO(PhoneLac entity) {
        PhoneLacVO vo = new PhoneLacVO();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

}




