package com.xy.admin.config;

import com.xy.admin.util.DataSourceContextHolder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * 多数据源配置类
 */
@Configuration
public class DataSourceConfig {

    /**
     * 主数据源
     */
    @Bean
    @ConfigurationProperties("spring.datasource.dynamic.datasource.primary")
    public DataSource primaryDataSource() {
        return DataSourceBuilder.create().build();
    }

    /**
     * 次要数据源
     */
    @Bean
    @ConfigurationProperties("spring.datasource.dynamic.datasource.secondary")
    public DataSource secondaryDataSource() {
        return DataSourceBuilder.create().build();
    }

    /**
     * 第三数据源
     */
    @Bean
    @ConfigurationProperties("spring.datasource.dynamic.datasource.tertiary")
    public DataSource tertiaryDataSource() {
        return DataSourceBuilder.create().build();
    }

    /**
     * 第四数据源
     */
    @Bean
    @ConfigurationProperties("spring.datasource.dynamic.datasource.fourth")
    public DataSource fourthDataSource() {
        return DataSourceBuilder.create().build();
    }

    /**
     * 动态数据源
     */
    @Bean
    @Primary
    public DataSource dynamicDataSource(@Qualifier("primaryDataSource") DataSource primaryDataSource,
            @Qualifier("secondaryDataSource") DataSource secondaryDataSource,
            @Qualifier("tertiaryDataSource") DataSource tertiaryDataSource,
            @Qualifier("fourthDataSource") DataSource fourthDataSource) {
        DynamicRoutingDataSource dynamicDataSource = new DynamicRoutingDataSource();

        Map<Object, Object> dataSourceMap = new HashMap<>();
        dataSourceMap.put("primary", primaryDataSource);
        dataSourceMap.put("secondary", secondaryDataSource);
        dataSourceMap.put("tertiary", tertiaryDataSource);
        dataSourceMap.put("fourth", fourthDataSource);

        // 设置数据源映射
        dynamicDataSource.setTargetDataSources(dataSourceMap);
        // 设置默认数据源
        dynamicDataSource.setDefaultTargetDataSource(primaryDataSource);

        return dynamicDataSource;
    }

    /**
     * 动态路由数据源
     */
    public static class DynamicRoutingDataSource extends AbstractRoutingDataSource {
        @Override
        protected Object determineCurrentLookupKey() {
            String dataSource = DataSourceContextHolder.getDataSource();
            return dataSource != null ? dataSource : "primary";
        }
    }
}
