package com.xy.admin.vo.proInfoProduct;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 产品推广信息编辑VO
 * <AUTHOR>
 * @since 2025/7/17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProInfoProductEditVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * ID
     */
    private Integer id;
    
    /**
     * 公司
     */
    @Size(max = 50, message = "公司最大长度要小于 50")
    private String company;
    
    /**
     * 代理商：zsgl, sqs
     */
    @Size(max = 50, message = "代理商最大长度要小于 50")
    private String agent;
    
    /**
     * 产品ID
     */
    @Size(max = 100, message = "产品ID最大长度要小于 100")
    private String product;
    
    /**
     * pro编号
     */
    @Size(max = 100, message = "pro编号最大长度要小于 100")
    private String pro;
    
    /**
     * 运营商
     */
    @Size(max = 64, message = "运营商最大长度要小于 64")
    private String carrier;
    
    /**
     * 产品编号
     */
    @Size(max = 50, message = "产品编号最大长度要小于 50")
    private String productNo;
    
    /**
     * 产品名称
     */
    @Size(max = 255, message = "产品名称最大长度要小于 255")
    private String productName;
    
    /**
     * 产品状态: 1-待接入 2-已暂停 3-已下线 4-可推广
     */
    private Byte productStatus;
    
    /**
     * 产品价格
     */
    @Size(max = 256, message = "产品价格最大长度要小于 256")
    private String productPrice;
    
    /**
     * 推广省份
     */
    @Size(max = 256, message = "推广省份最大长度要小于 256")
    private String promotionProvince;
    
    /**
     * 业务要求
     */
    private String businessRequirements;
    
    /**
     * 推广价格
     */
    @Size(max = 256, message = "推广价格最大长度要小于 256")
    private String promotionPrice;
    
    /**
     * 请求参数
     */
    @Size(max = 256, message = "请求参数最大长度要小于 256")
    private String params;
    
    /**
     * 开发对接状态：1-排期中 2-计划中 3-开发中 4-测试中 5-等页面 6-审核中 7-联调中 8-等修复 9-待测试 10-已上线
     */
    private Byte developmentStatus;
    
    /**
     * 参考图片URL
     */
    @Size(max = 512, message = "参考图片URL最大长度要小于 512")
    private String referenceImage;
    
    /**
     * 参考链接URL
     */
    @Size(max = 512, message = "参考链接URL最大长度要小于 512")
    private String referenceLink;
    
    /**
     * 备注信息
     */
    private String remark;
}
