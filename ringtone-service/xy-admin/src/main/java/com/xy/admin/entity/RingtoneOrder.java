package com.xy.admin.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@TableName(value = "ringtone_order")
@Data
public class RingtoneOrder implements Serializable {

    /**
     * 订单号
     */
    @TableId
    private String orderNumber;
    /**
     * 移动返回的productId
     */
    private String productId;
    /**
     * 
     */
    private Integer userId;
    /**
     * 
     */
    private String openid;
    /**
     * 
     */
    private String phone;
    /**
     * 订单费用，单位人民币分
     */
    private Integer fee;
    /**
     * 
     */
    private String outTradeNo;
    /**
     * 
     */
    private String transactionId;
    /**
     * 
     */
    private LocalDateTime payTime;
    /**
     * 是否支付
     */
    private Boolean payed;
    /**
     * 订单状态:-10已取消
     */
    private Integer status;
    /**
     * 
     */
    private String ringtoneId;
    /**
     * 
     */
    private String ringtoneName;
    /**
     * 是否正在使用
     */
    private Boolean current;
    /**
     * 
     */
    private String ringtoneCover;
    private String departmentId;
    private String departmentName;
    /**
     * 
     */
    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
