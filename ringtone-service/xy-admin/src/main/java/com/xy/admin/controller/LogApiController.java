package com.xy.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xy.admin.annotation.DataSource;
import com.xy.admin.service.LogApiService;
import com.xy.admin.vo.logApi.LogApiQueryVO;
import com.xy.admin.vo.logApi.LogApiVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
* API日志控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/log/business")
public class LogApiController {

    @Autowired
    private LogApiService logApiService;

    /**
     * 列表查询 - 支持多字段查询、分页和排序
     */
    @PostMapping("/list")
    @DataSource("tertiary")
    public IPage<LogApiVO> list(@Valid @RequestBody LogApiQueryVO queryVO) {
        return logApiService.queryPage(queryVO);
    }
}
