package com.xy.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.admin.entity.ConfigHuichuan;
import com.xy.admin.mapper.ConfigHuichuanMapper;
import com.xy.admin.service.ConfigHuichuanService;
import com.xy.admin.vo.configHuichuan.ConfigHuichuanQueryVO;
import com.xy.admin.vo.configHuichuan.ConfigHuichuanVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;
/**
 * 回传配置服务实现类
 * <AUTHOR>
 * @since 2025/7/28 09:52
 */
@Service
public class ConfigHuichuanServiceImpl extends ServiceImpl<ConfigHuichuanMapper, ConfigHuichuan> implements ConfigHuichuanService{

    @Override
    public IPage<ConfigHuichuanVO> queryPage(ConfigHuichuanQueryVO queryVO) {
        // 构建查询条件
        LambdaQueryWrapper<ConfigHuichuan> wrapper = new LambdaQueryWrapper<>();

        // 渠道查询
        if (StrUtil.isNotBlank(queryVO.getChannel())) {
            wrapper.like(ConfigHuichuan::getChannel, queryVO.getChannel());
        }

        // 来源查询
        if (StrUtil.isNotBlank(queryVO.getSource())) {
            wrapper.like(ConfigHuichuan::getSource, queryVO.getSource());
        }

        // 参数名查询
        if (StrUtil.isNotBlank(queryVO.getParamName())) {
            wrapper.like(ConfigHuichuan::getParamName, queryVO.getParamName());
        }

        // 平台查询
        if (StrUtil.isNotBlank(queryVO.getPlatform())) {
            wrapper.like(ConfigHuichuan::getPlatform, queryVO.getPlatform());
        }

        // 回传方式查询
        if (queryVO.getNodelay() != null) {
            wrapper.eq(ConfigHuichuan::getNodelay, queryVO.getNodelay());
        }

        // 关键字查询（支持渠道、来源、参数名的模糊查询）
        if (StrUtil.isNotBlank(queryVO.getKeyword())) {
            wrapper.and(w -> w
                    .like(ConfigHuichuan::getChannel, queryVO.getKeyword())
                    .or()
                    .like(ConfigHuichuan::getSource, queryVO.getKeyword())
                    .or()
                    .like(ConfigHuichuan::getParamName, queryVO.getKeyword())
            );
        }

        // 按创建时间倒序排列
        wrapper.orderByDesc(ConfigHuichuan::getCreateTime);

        // 分页查询
        Page<ConfigHuichuan> page = queryVO.buildPage();
        IPage<ConfigHuichuan> entityPage = this.page(page, wrapper);

        // 转换为VO
        List<ConfigHuichuanVO> voList = entityPage.getRecords().stream()
                .map(entity -> {
                    ConfigHuichuanVO vo = new ConfigHuichuanVO();
                    BeanUtils.copyProperties(entity, vo);
                    return vo;
                })
                .collect(Collectors.toList());

        // 构建返回结果
        Page<ConfigHuichuanVO> voPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        voPage.setRecords(voList);
        return voPage;
    }

}
