package com.xy.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName pro_info
 */
@TableName(value ="pro_info")
@Data
public class ProInfo implements Serializable {
    /**
     * 广告
     */
    @TableId
    private String pro;

    /**
     * 渠道号
     */
    private String channelCode;

    /**
     * 产品订购id
     */
    private String productId;

    /**
     * 管理员电话号
     */
    private String adminPhone;

    /**
     * 服务热线
     */
    private String hotline;

    /**
     * 所属广告主
     */
    private String advertiser;

    /**
     * uniqueAccId
     */
    private String uniqueAccId;

    /**
     * accPassword
     */
    private String accPassword;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ProInfo other = (ProInfo) that;
        return (this.getPro() == null ? other.getPro() == null : this.getPro().equals(other.getPro()))
            && (this.getChannelCode() == null ? other.getChannelCode() == null : this.getChannelCode().equals(other.getChannelCode()))
            && (this.getProductId() == null ? other.getProductId() == null : this.getProductId().equals(other.getProductId()))
            && (this.getAdminPhone() == null ? other.getAdminPhone() == null : this.getAdminPhone().equals(other.getAdminPhone()))
            && (this.getHotline() == null ? other.getHotline() == null : this.getHotline().equals(other.getHotline()))
            && (this.getAdvertiser() == null ? other.getAdvertiser() == null : this.getAdvertiser().equals(other.getAdvertiser()))
            && (this.getUniqueAccId() == null ? other.getUniqueAccId() == null : this.getUniqueAccId().equals(other.getUniqueAccId()))
            && (this.getAccPassword() == null ? other.getAccPassword() == null : this.getAccPassword().equals(other.getAccPassword()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getPro() == null) ? 0 : getPro().hashCode());
        result = prime * result + ((getChannelCode() == null) ? 0 : getChannelCode().hashCode());
        result = prime * result + ((getProductId() == null) ? 0 : getProductId().hashCode());
        result = prime * result + ((getAdminPhone() == null) ? 0 : getAdminPhone().hashCode());
        result = prime * result + ((getHotline() == null) ? 0 : getHotline().hashCode());
        result = prime * result + ((getAdvertiser() == null) ? 0 : getAdvertiser().hashCode());
        result = prime * result + ((getUniqueAccId() == null) ? 0 : getUniqueAccId().hashCode());
        result = prime * result + ((getAccPassword() == null) ? 0 : getAccPassword().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", pro=").append(pro);
        sb.append(", channelCode=").append(channelCode);
        sb.append(", productId=").append(productId);
        sb.append(", adminPhone=").append(adminPhone);
        sb.append(", hotline=").append(hotline);
        sb.append(", advertiser=").append(advertiser);
        sb.append(", uniqueAccId=").append(uniqueAccId);
        sb.append(", accPassword=").append(accPassword);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}