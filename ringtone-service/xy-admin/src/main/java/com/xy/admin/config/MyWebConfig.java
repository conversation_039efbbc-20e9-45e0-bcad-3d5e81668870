package com.xy.admin.config;

import com.xy.base.starter.config.DefaultWebConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;

/**
 * <AUTHOR>
 * @since 2019-11-22
 */
@Configuration
public class MyWebConfig extends DefaultWebConfig {

    @Value("${oss.local}")
    private String local;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/res/**").addResourceLocations("file:" + local);
    }

}
