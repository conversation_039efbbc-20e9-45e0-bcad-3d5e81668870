package com.xy.admin.dto.productAnalysis;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 产品分析统计DTO - 用于数据库查询结果映射
 * <AUTHOR>
 * @since 2025/08/04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductAnalysisStatsDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 日期（格式：yyyy-MM-dd）
     */
    private String date;

    /**
     * 总记录数
     */
    private Long totalCount;

    /**
     * 短信发送数（operation_type = 'SMS'）
     */
    private Long smsCount;

    /**
     * 订单提交数（operation_type = 'ORDER'）
     */
    private Long orderCount;

    /**
     * 订单提交成功数（通过pro_order表的reportStatus=1统计）
     */
    private Long orderSuccessCount;
}