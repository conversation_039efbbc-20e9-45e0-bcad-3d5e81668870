package com.xy.admin.config;

import cn.hutool.db.ds.DSFactory;
import cn.hutool.db.ds.GlobalDSFactory;
import cn.hutool.setting.Setting;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.EnumerablePropertySource;
import org.springframework.core.env.Environment;
import org.springframework.core.env.PropertySource;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
@Slf4j
public class HutoolDataSourceInit implements ApplicationRunner {

    @Value("${spring.datasource.dynamic.primary}")
    private String primaryDataSourceName;

    @Value("${spring.datasource.dynamic.datasource.primary.jdbc-url}")
    private String url;

    @Value("${spring.datasource.dynamic.datasource.primary.username}")
    private String username;

    @Value("${spring.datasource.dynamic.datasource.primary.password}")
    private String password;

    @Value("${spring.datasource.dynamic.datasource.primary.driver-class-name}")
    private String driverClassName;

    @Resource
    private Environment environment;

    @Override
    public void run(ApplicationArguments args) {
        Setting setting = new Setting();
        // 将Spring Boot的主数据源配置转换为Hutool的Setting格式
        setting.set("url", url);
        setting.set("user", username);
        setting.set("pass", password);
        setting.set("driver", driverClassName);

        // 设置全局数据源
        GlobalDSFactory.set(DSFactory.create(setting));

        log.info("=== 数据源配置信息 ===");
        log.info("主数据源: {}", primaryDataSourceName);

        // 打印所有数据源信息
        printAllDataSources();
    }

    /**
     * 打印所有配置的数据源信息
     */
    private void printAllDataSources() {
        Set<String> dataSourceNames = getAllDataSourceNames();

        log.info("发现 {} 个数据源配置:", dataSourceNames.size());

        for (String dsName : dataSourceNames) {
            String urlKey = "spring.datasource.dynamic.datasource." + dsName + ".jdbc-url";
            String url = environment.getProperty(urlKey);

            if (url != null && !url.isEmpty()) {
                String databaseName = extractDatabaseName(url);
                String username = environment
                        .getProperty("spring.datasource.dynamic.datasource." + dsName + ".username");
                String isPrimary = dsName.equals(primaryDataSourceName) ? " [主数据源]" : "";

                log.info("数据源: {}{} -> 数据库: {}", dsName, isPrimary, databaseName);
                log.info("  连接地址: {}", url);
                log.info("  用户名: {}", username);
                log.info("  --------");
            }
        }

        log.info("=== 数据源配置信息结束 ===");
    }

    /**
     * 动态获取所有配置的数据源名称
     */
    private Set<String> getAllDataSourceNames() {
        Set<String> dataSourceNames = new HashSet<>();

        if (environment instanceof ConfigurableEnvironment) {
            ConfigurableEnvironment configurableEnvironment = (ConfigurableEnvironment) environment;

            for (PropertySource<?> propertySource : configurableEnvironment.getPropertySources()) {
                if (propertySource instanceof EnumerablePropertySource) {
                    EnumerablePropertySource<?> enumerable = (EnumerablePropertySource<?>) propertySource;

                    for (String propertyName : enumerable.getPropertyNames()) {
                        // 查找匹配 spring.datasource.dynamic.datasource.{name}.jdbc-url 格式的属性
                        if (propertyName.startsWith("spring.datasource.dynamic.datasource.")
                                && propertyName.endsWith(".jdbc-url")) {

                            // 提取数据源名称
                            String prefix = "spring.datasource.dynamic.datasource.";
                            String suffix = ".jdbc-url";
                            String dsName = propertyName.substring(prefix.length(),
                                    propertyName.length() - suffix.length());
                            dataSourceNames.add(dsName);
                        }
                    }
                }
            }
        }

        return dataSourceNames;
    }

    /**
     * 从JDBC URL中提取数据库名
     */
    private String extractDatabaseName(String jdbcUrl) {
        try {
            // 找到最后一个 '/' 后面的内容
            int lastSlashIndex = jdbcUrl.lastIndexOf("/");
            if (lastSlashIndex != -1) {
                String dbPart = jdbcUrl.substring(lastSlashIndex + 1);
                // 如果有参数（?），则只取?前面的部分
                int questionMarkIndex = dbPart.indexOf("?");
                if (questionMarkIndex != -1) {
                    return dbPart.substring(0, questionMarkIndex);
                }
                return dbPart;
            }
            return "未知";
        } catch (Exception e) {
            log.warn("解析数据库名失败: {}", jdbcUrl, e);
            return "解析失败";
        }
    }
}
