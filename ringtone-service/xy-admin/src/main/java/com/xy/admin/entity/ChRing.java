package com.xy.admin.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2024-02-20
 */
@TableName(value = "ch_ring")
@Data
public class ChRing implements Serializable {

    /**
     * 渠道商代码
     */
    @NotNull
    private String channel;
    /**
     * 来源代码
     */
    @NotNull
    private String source;
    /**
     * 铃音ID
     */
    private String ringId;
    /**
     * 铃音名称
     */
    private String ringName;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
