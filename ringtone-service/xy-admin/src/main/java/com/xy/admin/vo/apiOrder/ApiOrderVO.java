package com.xy.admin.vo.apiOrder;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * API订单展示VO
 * <AUTHOR>
 * @since 2025/7/30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApiOrderVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 渠道编号
     */
    private String channelNo;
    
    /**
     * 产品编号
     */
    private String productNo;
    
    /**
     * 手机号码
     */
    private String mobileNo;
    
    /**
     * 订单ID
     */
    private String orderId;
    
    /**
     * 订单状态码
     */
    private String orderStatus;
    
    /**
     * 外部订单状态
     */
    private String outOrderStatus;
    
    /**
     * 外部订单ID
     */
    private String outOrderId;
    
    /**
     * 操作类型：SMS-短信发送，ORDER-订单提交
     */
    private String operationType;
    
    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 应用包名
     */
    private String appPackage;
    
    /**
     * 应用名称
     */
    private String appName;
    
    /**
     * 平台名称
     */
    private String platform;
    
    /**
     * 页面URL
     */
    private String pageUrl;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    /**
     * 服务器
     */
    private String server;
}
