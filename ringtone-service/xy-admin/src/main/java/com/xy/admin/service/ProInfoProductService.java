package com.xy.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xy.admin.entity.ProInfoProduct;
import com.xy.admin.vo.proInfoProduct.ProInfoProductQueryVO;
import com.xy.admin.vo.proInfoProduct.ProInfoProductVO;

/**
 * 产品推广信息服务接口
 * <AUTHOR>
 * @since 2025/7/17 16:00
 */
public interface ProInfoProductService extends IService<ProInfoProduct>{

    /**
     * 分页查询产品推广信息
     * @param queryVO 查询条件
     * @return 分页结果
     */
    IPage<ProInfoProductVO> queryPage(ProInfoProductQueryVO queryVO);

}
