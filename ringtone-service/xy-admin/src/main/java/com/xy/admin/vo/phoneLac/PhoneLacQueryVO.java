package com.xy.admin.vo.phoneLac;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xy.base.starter.dto.CommonPageQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 手机号段查询VO
 * <AUTHOR>
 * @since 2025/7/18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PhoneLacQueryVO extends CommonPageQuery {
    private static final long serialVersionUID = 1L;
    
    /**
     * 号段
     */
    private String seg;
    
    /**
     * 省份
     */
    private String province;
    
    /**
     * 城市
     */
    private String city;
    
    /**
     * 运营商
     */
    private String carrier;
    
    /**
     * 虚拟运营商
     */
    private String vcarrier;
    
    /**
     * 创建时间开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeStart;
    
    /**
     * 创建时间结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;
    
    /**
     * 更新时间开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTimeStart;
    
    /**
     * 更新时间结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTimeEnd;
    
    /**
     * 排序字段
     */
    private String orderField;
    
    /**
     * 排序方式：asc/desc
     */
    private String orderType;
}
