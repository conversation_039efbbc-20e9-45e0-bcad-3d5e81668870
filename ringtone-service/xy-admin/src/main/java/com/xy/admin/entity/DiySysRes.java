package com.xy.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 系统素材
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@TableName(value = "diy_sys_res")
@Data
public class DiySysRes implements Serializable {

    /**
     * 
	 * @NeverNull
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    private String name;
    /**
     * 图片访问地址
	 * @NeverNull
     */
    private String image;
    /**
     * 
     */
    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
