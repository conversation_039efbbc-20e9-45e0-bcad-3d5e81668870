package com.xy.admin.vo.phoneLac;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 手机号段编辑VO
 * <AUTHOR>
 * @since 2025/7/18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PhoneLacEditVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 号段
     */
    @Size(max = 7, message = "号段最大长度要小于 7")
    private String seg;
    
    /**
     * 省份
     */
    @Size(max = 25, message = "省份最大长度要小于 25")
    private String province;
    
    /**
     * 城市
     */
    @Size(max = 55, message = "城市最大长度要小于 55")
    private String city;
    
    /**
     * 运营商
     */
    @Size(max = 25, message = "运营商最大长度要小于 25")
    private String carrier;
    
    /**
     * 虚拟运营商
     */
    @Size(max = 25, message = "虚拟运营商最大长度要小于 25")
    private String vcarrier;
}
