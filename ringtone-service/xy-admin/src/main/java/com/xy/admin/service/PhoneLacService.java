package com.xy.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xy.admin.entity.PhoneLac;
import com.xy.admin.vo.phoneLac.PhoneLacQueryVO;
import com.xy.admin.vo.phoneLac.PhoneLacVO;

/**
 * 手机号段服务接口
 * <AUTHOR>
 * @description 针对表【phone_lac】的数据库操作Service
 * @createDate 2024-07-17 10:57:17
 */
public interface PhoneLacService extends IService<PhoneLac> {

    /**
     * 分页查询手机号段信息
     * @param queryVO 查询条件
     * @return 分页结果
     */
    IPage<PhoneLacVO> queryPage(PhoneLacQueryVO queryVO);

}
