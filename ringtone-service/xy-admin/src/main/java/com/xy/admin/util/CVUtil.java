package com.xy.admin.util;

import cn.hutool.core.util.StrUtil;
import com.xy.base.core.util.IdUtils;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.ffmpeg.global.avcodec;
import org.bytedeco.ffmpeg.global.avutil;
import org.bytedeco.javacv.*;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/11
 */
@Slf4j
public class CVUtil {

    public static String compose(String voicePath, List<String> pics, int seconds, String targetFolder) throws Exception {

        String mp4SavePath = StrUtil.format("{}/{}.mp4", targetFolder, IdUtils.nextIdWithPrefix("v"));
        int width = 540;
        int height = 960;
        createMp4(mp4SavePath, seconds, pics, width, height);

        String finalPath = StrUtil.format("{}/{}.mp4", targetFolder, IdUtils.nextIdWithPrefix("diy"));


        mergeAudioAndVideo(mp4SavePath, voicePath, finalPath);


        return finalPath;
    }

    private static void createMp4(String mp4SavePath, int seconds, List<String> imgs, int width, int height) throws FrameRecorder.Exception {
        try (FFmpegFrameRecorder recorder = new FFmpegFrameRecorder(mp4SavePath, width, height)) {
            //设置视频编码层模式
            recorder.setVideoCodec(avcodec.AV_CODEC_ID_H264);
            //设置视频为25帧每秒
            recorder.setFrameRate(30);
            //设置视频图像数据格式
            recorder.setPixelFormat(avutil.AV_PIX_FMT_YUV420P);
            recorder.setFormat("mp4");

            try {
                recorder.start();
                Java2DFrameConverter converter = new Java2DFrameConverter();

                //录制一个seconds秒的视频
                // 计算每张图需要暂时的时长（每秒30张）
                int frame = seconds * 30 / imgs.size();
                for (String img : imgs) {
                    BufferedImage read = ImageIO.read(new File(img));
                    for (int j = 0; j <= frame; j++) {
                        recorder.record(converter.getFrame(read));
                    }
                }

            } catch (Exception e) {
                log.error("创建视频错误", e);
            }
        }
    }

    public static void mergeAudioAndVideo(String videoPath, String audioPath, String outPut) throws Exception {

        FrameRecorder recorder = null;
        FrameGrabber grabber1 = null;
        FrameGrabber grabber2 = null;
        try {
            //抓取视频帧
            grabber1 = new FFmpegFrameGrabber(videoPath);
            //抓取音频帧
            grabber2 = new FFmpegFrameGrabber(audioPath);
            grabber1.start();
            grabber2.start();
            //创建录制
            recorder = new FFmpegFrameRecorder(outPut, grabber1.getImageWidth(), grabber1.getImageHeight(), grabber2.getAudioChannels());

            recorder.setVideoCodec(avcodec.AV_CODEC_ID_H264);
            recorder.setAudioCodec(avcodec.AV_CODEC_ID_AAC);
            recorder.setFormat("mp4");
            recorder.setFrameRate(30);
            recorder.setSampleRate(grabber2.getSampleRate());

            recorder.start();

            Frame frame1;
            Frame frame2;
            //先录入视频
            while ((frame1 = grabber1.grabFrame()) != null) {
                recorder.record(frame1);
            }
            //然后录入音频
            while ((frame2 = grabber2.grabFrame()) != null) {
                recorder.record(frame2);
            }
            grabber1.stop();
            grabber2.stop();
            recorder.stop();
        } catch (Exception e) {
            log.error("合成音频错误", e);
        } finally {
            try {
                if (recorder != null) {
                    recorder.close();
                }
                if (grabber1 != null) {
                    grabber1.close();
                }
                if (grabber2 != null) {
                    grabber2.close();
                }
            } catch (FrameRecorder.Exception e) {
                log.error("合成音频错误", e);
            }
        }

    }
}
