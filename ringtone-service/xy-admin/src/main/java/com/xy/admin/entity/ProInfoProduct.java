package com.xy.admin.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * 产品推广信息表
 * <AUTHOR>
 * @since 2025/7/17 16:00
 */
@Data
@Accessors(chain = true)
@SuperBuilder
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "pro_info_product")
public class ProInfoProduct implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @NotNull(message = "ID不能为null")
    private Integer id;

    /**
     * 公司
     */
    @TableField(value = "company")
    @Size(max = 50,message = "公司最大长度要小于 50")
    private String company;

    /**
     * 代理商：zsgl, sqs
     */
    @TableField(value = "agent")
    @Size(max = 50,message = "代理商：zsgl, sqs最大长度要小于 50")
    private String agent;

    /**
     * 产品ID
     */
    @TableField(value = "product")
    @Size(max = 100,message = "产品ID最大长度要小于 100")
    private String product;

    /**
     * pro编号
     */
    @TableField(value = "pro")
    @Size(max = 100,message = "pro编号最大长度要小于 100")
    private String pro;

    /**
     * 运营商
     */
    @TableField(value = "carrier")
    @Size(max = 64,message = "运营商最大长度要小于 64")
    private String carrier;

    /**
     * 产品编号
     */
    @TableField(value = "product_no")
    @Size(max = 50,message = "产品编号最大长度要小于 50")
    private String productNo;

    /**
     * 产品名称
     */
    @TableField(value = "product_name")
    @Size(max = 255,message = "产品名称最大长度要小于 255")
    private String productName;

    /**
     * 产品状态: 1-待接入 2-已暂停 3-已下线 4-可推广
     */
    @TableField(value = "product_status")
    private Byte productStatus;

    /**
     * 产品价格
     */
    @TableField(value = "product_price")
    @Size(max = 256,message = "产品价格最大长度要小于 256")
    private String productPrice;

    /**
     * 推广省份
     */
    @TableField(value = "promotion_province")
    @Size(max = 256,message = "推广省份最大长度要小于 256")
    private String promotionProvince;

    /**
     * 业务要求
     */
    @TableField(value = "business_requirements")
    private String businessRequirements;

    /**
     * 推广价格
     */
    @TableField(value = "promotion_price")
    @Size(max = 256,message = "推广价格最大长度要小于 256")
    private String promotionPrice;

    /**
     * 请求参数
     */
    @TableField(value = "params")
    @Size(max = 256,message = "请求参数最大长度要小于 256")
    private String params;

    /**
     * 开发对接状态：1-排期中 2-计划中 3-开发中 4-测试中 5-等页面 6-审核中 7-联调中 8-等修复 9-待测试 10-已上线
     */
    @TableField(value = "development_status")
    private Byte developmentStatus;

    /**
     * 参考图片URL
     */
    @TableField(value = "reference_image")
    @Size(max = 512,message = "参考图片URL最大长度要小于 512")
    private String referenceImage;

    /**
     * 参考链接URL
     */
    @TableField(value = "reference_link")
    @Size(max = 512,message = "参考链接URL最大长度要小于 512")
    private String referenceLink;

    /**
     * 备注信息
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}