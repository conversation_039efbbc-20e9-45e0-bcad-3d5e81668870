package com.xy.admin.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据源切换注解
 * 可以用在类或方法上
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Target({ ElementType.TYPE, ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
public @interface DataSource {
  /**
   * 数据源名称
   * primary: 主数据源
   * secondary: 次要数据源
   * tertiary: 第三数据源
   * fourth: 第四数据源
   */
  String value() default "primary";
}