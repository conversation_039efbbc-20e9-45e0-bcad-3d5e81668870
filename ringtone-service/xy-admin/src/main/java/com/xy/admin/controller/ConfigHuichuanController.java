package com.xy.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xy.admin.annotation.DataSource;
import com.xy.admin.entity.ConfigHuichuan;
import com.xy.admin.service.ConfigHuichuanService;
import com.xy.admin.vo.configHuichuan.ConfigHuichuanAddVO;
import com.xy.admin.vo.configHuichuan.ConfigHuichuanEditVO;
import com.xy.admin.vo.configHuichuan.ConfigHuichuanQueryVO;
import com.xy.admin.vo.configHuichuan.ConfigHuichuanVO;
import com.xy.base.core.response.Result;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 回传配置控制层
 *
 * <AUTHOR>
 * @since 2025/7/28
 */
@RestController
@RequestMapping("/config_huichuan")
public class ConfigHuichuanController {

    @Autowired
    private ConfigHuichuanService configHuichuanService;

    /**
     * 回传配置列表查询接口
     * 支持多字段查询、分页和排序
     */
    @PostMapping("/list")
    @DataSource("tertiary")
    public IPage<ConfigHuichuanVO> list(@Valid @RequestBody ConfigHuichuanQueryVO queryVO) {
        return configHuichuanService.queryPage(queryVO);
    }

    /**
     * 单个回传配置查询接口（通过ID）
     */
    @GetMapping("/{id}")
    @DataSource("tertiary")
    public Result<ConfigHuichuanVO> getById(@PathVariable Long id) {
        ConfigHuichuan entity = configHuichuanService.getById(id);
        if (entity == null) {
            return Result.error(404, "回传配置不存在");
        }

        ConfigHuichuanVO vo = new ConfigHuichuanVO();
        BeanUtils.copyProperties(entity, vo);
        return Result.success(vo);
    }

    /**
     * 回传配置新增接口
     */
    @PostMapping
    @DataSource("tertiary")
    public Result<String> add(@Valid @RequestBody ConfigHuichuanAddVO addVO) {
        ConfigHuichuan entity = new ConfigHuichuan();
        BeanUtils.copyProperties(addVO, entity);

        boolean success = configHuichuanService.save(entity);
        if (success) {
            return Result.success("新增成功");
        } else {
            return Result.error(500, "新增失败");
        }
    }

    /**
     * 回传配置修改接口
     */
    @PutMapping("/{id}")
    @DataSource("tertiary")
    public Result<String> update(@PathVariable Long id, @Valid @RequestBody ConfigHuichuanEditVO editVO) {
        // 确保ID一致
        editVO.setId(id);

        ConfigHuichuan entity = new ConfigHuichuan();
        BeanUtils.copyProperties(editVO, entity);

        boolean success = configHuichuanService.updateById(entity);
        if (success) {
            return Result.success("修改成功");
        } else {
            return Result.error(500, "修改失败");
        }
    }

    /**
     * 回传配置删除接口
     */
    @DeleteMapping("/{id}")
    @DataSource("tertiary")
    public Result<String> delete(@PathVariable Long id) {
        boolean success = configHuichuanService.removeById(id);
        if (success) {
            return Result.success("删除成功");
        } else {
            return Result.error(500, "删除失败");
        }
    }
}
