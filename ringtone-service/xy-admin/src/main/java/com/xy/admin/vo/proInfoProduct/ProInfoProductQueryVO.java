package com.xy.admin.vo.proInfoProduct;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xy.base.starter.dto.CommonPageQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 产品推广信息查询VO
 * <AUTHOR>
 * @since 2025/7/17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ProInfoProductQueryVO extends CommonPageQuery{
    
    /**
     * 公司
     */
    private String company;
    
    /**
     * 运营商
     */
    private String carrier;
    
    /**
     * 产品编号
     */
    private String productNo;
    
    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品状态: 1-待接入 2-已暂停 3-已下线 4-可推广
     */
    private Byte productStatus;

    /**
     * 产品价格
     */
    private String productPrice;
    
    /**
     * 推广省份
     */
    private String promotionProvince;
    
    /**
     * 业务要求
     */
    private String businessRequirements;
    
    /**
     * 推广价格
     */
    private String promotionPrice;

    /**
     * 开发对接状态：1-排期中 2-计划中 3-开发中 4-测试中 5-等页面 6-审核中 7-联调中 8-等修复 9-待测试 10-已上线
     */
    private Byte developmentStatus;

    /**
     * 参考图片URL
     */
    private String referenceImage;
    
    /**
     * 参考链接URL
     */
    private String referenceLink;
    
    /**
     * 备注信息
     */
    private String remark;
    
    /**
     * 创建时间开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeStart;
    
    /**
     * 创建时间结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;
    
    /**
     * 更新时间开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTimeStart;
    
    /**
     * 更新时间结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTimeEnd;
    
    /**
     * 排序字段
     */
    private String orderField;
    
    /**
     * 排序方式：asc/desc
     */
    private String orderType;
}
