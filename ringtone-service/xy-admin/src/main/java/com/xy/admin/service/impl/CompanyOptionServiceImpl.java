package com.xy.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xy.admin.entity.ProductCompanyInfo;
import com.xy.admin.entity.ProductPromotionCompany;
import com.xy.admin.service.CompanyOptionService;
import com.xy.admin.service.ProductCompanyInfoService;
import com.xy.admin.service.ProductPromotionCompanyService;
import com.xy.admin.vo.common.CompanyOptionVO;
import com.xy.base.starter.redis.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 公司选项服务实现类
 * 
 * <AUTHOR>
 * @since 2025/7/18
 */
@Slf4j
@Service
public class CompanyOptionServiceImpl implements CompanyOptionService {
    
    /**
     * Redis缓存键前缀
     */
    private static final String CACHE_KEY_COMPANY_OPTIONS = "company:options:all";
    
    /**
     * 缓存过期时间（秒）- 1小时
     */
    private static final long CACHE_EXPIRE_TIME = 3600L;
    
    @Autowired
    private ProductCompanyInfoService productCompanyInfoService;
    
    @Autowired
    private ProductPromotionCompanyService productPromotionCompanyService;
    
    @Override
    public List<CompanyOptionVO> getAllCompanyOptions() {
        // 先尝试从Redis缓存获取
        List<CompanyOptionVO> cachedOptions = RedisUtils.getForEntity(
            CACHE_KEY_COMPANY_OPTIONS, 
            new TypeReference<List<CompanyOptionVO>>() {}
        );
        
        if (CollUtil.isNotEmpty(cachedOptions)) {
            log.debug("从Redis缓存获取公司选项，数量: {}", cachedOptions.size());
            return cachedOptions;
        }
        
        // 缓存不存在，从数据库查询
        List<CompanyOptionVO> options = queryCompanyOptionsFromDatabase();
        
        // 缓存到Redis
        if (CollUtil.isNotEmpty(options)) {
            RedisUtils.set(CACHE_KEY_COMPANY_OPTIONS, options, CACHE_EXPIRE_TIME);
            log.debug("公司选项已缓存到Redis，数量: {}", options.size());
        }
        
        return options;
    }
    
    @Override
    public List<CompanyOptionVO> refreshCompanyOptions() {
        log.debug("刷新公司选项缓存");
        
        // 清除缓存
        clearCompanyOptionsCache();
        
        // 重新查询并缓存
        return getAllCompanyOptions();
    }
    
    @Override
    public void clearCompanyOptionsCache() {
        RedisUtils.del(CACHE_KEY_COMPANY_OPTIONS);
        log.debug("已清除公司选项缓存");
    }
    
    /**
     * 从数据库查询公司选项
     * 
     * @return 公司选项列表
     */
    private List<CompanyOptionVO> queryCompanyOptionsFromDatabase() {
        List<CompanyOptionVO> options = new ArrayList<>();
        
        try {
            // 查询ProductCompanyInfo表
            List<ProductCompanyInfo> companyInfoList = productCompanyInfoService.list(
                new LambdaQueryWrapper<ProductCompanyInfo>()
                    .isNotNull(ProductCompanyInfo::getCompanyId)
                    .isNotNull(ProductCompanyInfo::getCompanyName)
                    .orderByAsc(ProductCompanyInfo::getCompanyId)
            );
            
            if (CollUtil.isNotEmpty(companyInfoList)) {
                List<CompanyOptionVO> companyInfoOptions = companyInfoList.stream()
                    .map(company -> CompanyOptionVO.builder()
                        .companyId(company.getCompanyId())
                        .companyName(company.getCompanyName())
                        .build())
                    .collect(Collectors.toList());
                options.addAll(companyInfoOptions);
                log.debug("从ProductCompanyInfo表查询到公司数量: {}", companyInfoOptions.size());
            }
            
            // 查询ProductPromotionCompany表
            List<ProductPromotionCompany> promotionCompanyList = productPromotionCompanyService.list(
                new LambdaQueryWrapper<ProductPromotionCompany>()
                    .isNotNull(ProductPromotionCompany::getCompanyId)
                    .isNotNull(ProductPromotionCompany::getCompanyName)
                    .orderByAsc(ProductPromotionCompany::getCompanyId)
            );
            
            if (CollUtil.isNotEmpty(promotionCompanyList)) {
                List<CompanyOptionVO> promotionCompanyOptions = promotionCompanyList.stream()
                    .map(company -> CompanyOptionVO.builder()
                        .companyId(company.getCompanyId())
                        .companyName(company.getCompanyName())
                        .build())
                    .collect(Collectors.toList());
                options.addAll(promotionCompanyOptions);
                log.debug("从ProductPromotionCompany表查询到公司数量: {}", promotionCompanyOptions.size());
            }
            
            // 去重处理（基于companyId）
            options = options.stream()
                .collect(Collectors.toMap(
                    CompanyOptionVO::getCompanyId,
                    option -> option,
                    (existing, replacement) -> existing // 保留第一个
                ))
                .values()
                .stream()
                .sorted((o1, o2) -> o1.getCompanyId().compareTo(o2.getCompanyId()))
                .collect(Collectors.toList());
            
            log.debug("去重后的公司选项数量: {}", options.size());
            
        } catch (Exception e) {
            log.error("查询公司选项时发生异常", e);
        }
        
        return options;
    }
}
