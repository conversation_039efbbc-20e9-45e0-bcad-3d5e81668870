package com.xy.admin.vo.productAnalysis;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 产品分析结果VO
 * <AUTHOR>
 * @since 2025/08/04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductAnalysisResultVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 概览统计
     */
    private OverviewStats overviewStats;

    /**
     * 订单类型环比数据
     */
    private List<OrderTypeChartData> orderTypeChart;

    /**
     * 成功率环比数据
     */
    private List<SuccessRateChartData> successRateChart;

    /**
     * 概览统计数据
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OverviewStats implements Serializable {
        /**
         * 总数
         */
        private Long totalCount;

        /**
         * 短信发送数
         */
        private Long smsCount;

        /**
         * 订单提交数
         */
        private Long orderCount;

        /**
         * 订单提交成功数（办理成功）
         */
        private Long orderSuccessCount;

        /**
         * 订单提交成功率
         */
        private BigDecimal orderSuccessRate;

        /**
         * 产品转化率（订单提交数/短信发送数）
         */
        private BigDecimal productConversionRate;
    }

    /**
     * 订单类型图表数据
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OrderTypeChartData implements Serializable {
        /**
         * 日期
         */
        private String date;

        /**
         * 短信发送数
         */
        private Long smsCount;

        /**
         * 订单提交数
         */
        private Long orderCount;
    }

    /**
     * 成功率图表数据
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SuccessRateChartData implements Serializable {
        /**
         * 日期
         */
        private String date;

        /**
         * 订单提交成功率
         */
        private BigDecimal orderSuccessRate;

        /**
         * 产品转化率
         */
        private BigDecimal productConversionRate;
    }
}