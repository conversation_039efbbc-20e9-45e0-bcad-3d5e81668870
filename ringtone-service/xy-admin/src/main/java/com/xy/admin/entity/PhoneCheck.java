package com.xy.admin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
@TableName(value = "phone_check")
@Data
public class PhoneCheck implements Serializable {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 手机号
     */
    private String billnum;
    /**
     * 1添加成员，2删除成员
     */
    private Integer type;
    /**
     * 处理结果码，000000是成功，000001是失败
     */
    private String state;
    /**
     * 处理结果描述/失败原因描述
     */
    private String description;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
