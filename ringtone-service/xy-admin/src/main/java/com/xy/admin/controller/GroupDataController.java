package com.xy.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xy.admin.entity.*;
import com.xy.admin.service.*;
import com.xy.admin.vo.MemberManageVO;
import com.xy.base.starter.dto.CommonPageQuery;
import com.xy.lib.migu.online.MemberOperation;
import com.xy.lib.migu.vo.DepartmentObject;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 集团数据相关
 *
 * <AUTHOR>
 * @since 2023/12/3
 */
@RestController
@RequestMapping("/groupData")
@RequiredArgsConstructor
public class GroupDataController {


    private final RingDepartmentService departmentService;
    private final RingUserService userService;

    private final MemberManageService memberManageService;

    private final ChChannelService channelService;
    private final ChSourceService sourceService;


    /**
     * 集团部门列表
     */
    @PostMapping("/deptList")
    public IPage<RingDepartment> deptList(@RequestBody CommonPageQuery query) {

        Page<RingDepartment> page = query.buildPage();
        return departmentService.lambdaQuery()
                .like(StringUtils.hasText(query.getKeyword()), RingDepartment::getDepartmentName, query.getKeyword())
                .eq(query.getType() != null, RingDepartment::getType, query.getType())
                .ge(query.getStartTime() != null, RingDepartment::getCreateTime, query.getStartTime())
                .le(query.getEndTime() != null, RingDepartment::getCreateTime, query.getEndTime())
                .orderByDesc(RingDepartment::getCreateTime)
                .page(page);
    }


    /**
     * 企业成员列表
     */
    @PostMapping("/userList")
    public IPage<RingUser> userList(@RequestBody CommonPageQuery query) {

        Page<RingUser> page = query.buildPage();
        return userService.lambdaQuery()
                .like(StringUtils.hasText(query.getKeyword()), RingUser::getBillNum, query.getKeyword())
                .eq(query.getType() != null, RingUser::getType, query.getType())
                .le(query.getEndTime() != null, RingUser::getCreateTime, query.getEndTime())
                .ge(query.getStartTime() != null, RingUser::getCreateTime, query.getStartTime())
                .orderByDesc(RingUser::getCreateTime)
                .page(page);
    }


    /**
     * 企业成员列表
     */
    @PostMapping("/memberList")
    public IPage<MemberManageVO> memberList(@RequestBody CommonPageQuery query) {

        Page<MemberManage> page = query.buildPage();
        memberManageService.lambdaQuery()
                .eq(StringUtils.hasText(query.getKeyword()), MemberManage::getBillNum, query.getKeyword())
                .orderByDesc(MemberManage::getCreateTime)
                .page(page);

        HashMap<String, String> channelDict = new HashMap<>();
        HashMap<String, String> sourceDict = new HashMap<>();

        if (!page.getRecords().isEmpty()) {
            Set<String> channels = new HashSet<>();
            Set<String> sources = new HashSet<>();

            page.getRecords().forEach(m -> {
                channels.add(m.getChannel());
                sources.add(m.getSource());
            });

            if (!channels.isEmpty()) {
                channelService.lambdaQuery().in(ChChannel::getChannel, channels).list().forEach(c -> channelDict.put(c.getChannel(), c.getName()));
            }

            if (!sources.isEmpty()) {
                sourceService.lambdaQuery().in(ChSource::getSource, sources).list().forEach(s -> sourceDict.put(s.getChannel() + "-" + s.getSource(), s.getName()));
            }

        }

        return page.convert(m -> {
            MemberManageVO vo = new MemberManageVO();
            BeanUtils.copyProperties(m, vo);

            if (StringUtils.hasText(m.getChannel()) && channelDict.get(m.getChannel()) != null) {
                vo.setChannelName(channelDict.get(m.getChannel()));
            } else {
                vo.setChannelName(m.getChannel());
            }

            if (StringUtils.hasText(m.getSource()) && sourceDict.get(m.getChannel() + "-" + m.getSource()) != null) {
                vo.setSourceName(sourceDict.get(m.getChannel() + "-" + m.getSource()));
            } else {
                vo.setSourceName(m.getSource());
            }
            return vo;
        });

    }

    /**
     * 成员退订
     */
    @GetMapping("/memberDel/{phone}")
    public void memberDel(@PathVariable("phone") String phone) {
        MemberOperation.deleteMember(phone);
    }

    /**
     * 成员部门信息
     */
    @GetMapping("/memberGroup/{phone}")
    public List<DepartmentObject> memberGroup(@PathVariable("phone") String phone) {
        return MemberOperation.queryDepartment(phone);
    }

}
