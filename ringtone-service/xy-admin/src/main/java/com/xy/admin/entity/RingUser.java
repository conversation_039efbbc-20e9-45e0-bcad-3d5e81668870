package com.xy.admin.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2023-12-03
 */
@TableName(value = "ring_user")
@Data
public class RingUser implements Serializable {

    /**
     * 成员号码
     */
    private String billNum;
    /**
     * 
     */
    private Integer departmentId;
    /**
     * 成员添加来源，0前端。1后台
     */
    private Integer type;
    /**
     * 订单编码
     */
    private String orderId;
    /**
     * 
     */
    private String departmentName;
    /**
     * 
     */
    private LocalDateTime createTime;
    /**
     * 成员归属省份Id
     */
    private Integer provinceId;
    /**
     * 省份名称
     */
    private String provinceName;
    /**
     * 成员归属地市Id
     */
    private Integer locationId;
    /**
     * 地市名称
     */
    private String locationName;
    /**
     * 成员状态：
99 订购待确认
00 添加处理中
01 添加待归档
02 添加归档成功
10 删除处理中
11 删除待归档
12 删除归档失败
13 删除确认中
     */
    private String userStatus;
    /**
     * 企业名称
     */
    private String ecName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
