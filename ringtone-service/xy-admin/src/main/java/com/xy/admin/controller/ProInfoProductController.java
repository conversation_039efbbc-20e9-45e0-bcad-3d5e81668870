package com.xy.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xy.admin.annotation.DataSource;
import com.xy.admin.entity.ProInfoProduct;
import com.xy.admin.service.ProInfoProductService;
import com.xy.admin.service.CompanyOptionService;
import com.xy.admin.utils.ProInfoProductStatusUtils;
import com.xy.admin.vo.common.CompanyOptionVO;
import com.xy.admin.vo.proInfoProduct.ProInfoProductAddVO;
import com.xy.admin.vo.proInfoProduct.ProInfoProductEditVO;
import com.xy.admin.vo.proInfoProduct.ProInfoProductQueryVO;
import com.xy.admin.vo.proInfoProduct.ProInfoProductVO;
import com.xy.base.core.enums.ExceptionResultEnum;
import com.xy.base.core.exception.AppException;
import com.xy.base.core.response.Result;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 产品推广信息控制层
 *
 * <AUTHOR>
 * @since 2025/7/17
 */
@RestController
@RequestMapping("/proInfoProduct")
public class ProInfoProductController {

	@Autowired
	private ProInfoProductService proInfoProductService;

	@Autowired
	private CompanyOptionService companyOptionService;

	/**
	 * 产品列表查询接口
	 * 支持多字段查询、分页和排序
	 */
	@PostMapping("/list")
	@DataSource("tertiary")
	public IPage<ProInfoProductVO> list(@Valid @RequestBody ProInfoProductQueryVO queryVO) {
		return proInfoProductService.queryPage(queryVO);
	}

	/**
	 * 单个产品查询接口（通过ID）
	 */
	@GetMapping("/{id}")
	@DataSource("tertiary")
	public Result<ProInfoProductVO> getById(@PathVariable Integer id) {
		ProInfoProduct entity = proInfoProductService.getById(id);
		if (entity == null) {
			return Result.error(404, "产品不存在");
		}

		ProInfoProductVO vo = new ProInfoProductVO();
		BeanUtils.copyProperties(entity, vo);
		return Result.success(vo);
	}

	/**
	 * 产品新增接口
	 */
	@PostMapping
	@DataSource("tertiary")
	public Result<String> add(@Valid @RequestBody ProInfoProductAddVO addVO) {
		ProInfoProduct entity = new ProInfoProduct();
		BeanUtils.copyProperties(addVO, entity);

		boolean success = proInfoProductService.save(entity);
		if (success) {
			return Result.success("新增成功");
		} else {
			return Result.error(500, "新增失败");
		}
	}

	/**
	 * 产品修改接口
	 */
	@PutMapping("/{id}")
	@DataSource("tertiary")
	public Result<String> update(@PathVariable Integer id, @Valid @RequestBody ProInfoProductEditVO editVO) {
		// 确保ID一致
		editVO.setId(id);

		ProInfoProduct entity = new ProInfoProduct();
		BeanUtils.copyProperties(editVO, entity);

		boolean success = proInfoProductService.updateById(entity);
		AppException.tnt(!success, ExceptionResultEnum.DATA_ACCESS_ERROR);
		return Result.success("修改成功");
	}

	/**
	 * 产品删除接口
	 */
	@DeleteMapping("/{id}")
	@DataSource("tertiary")
	public Result<String> delete(@PathVariable Integer id) {
		boolean success = proInfoProductService.removeById(id);
		if (success) {
			return Result.success("删除成功");
		} else {
			return Result.error(500, "删除失败");
		}
	}

	/**
	 * 获取所有选项接口（包括状态选项和公司选项）
	 * 用于前端一次性获取所有下拉框选项
	 */
	@GetMapping("/options")
	@DataSource("tertiary")
	public Result<Map<String, Object>> getAllOptions() {
		Map<String, Object> allOptions = new HashMap<>();

		// 产品状态选项
		allOptions.put("productStatus", ProInfoProductStatusUtils.getProductStatusOptions());
		// 开发选项
		allOptions.put("developmentStatus", ProInfoProductStatusUtils.getDevelopmentStatusOptions());

		// 公司选项
		allOptions.put("companyOptions", companyOptionService.getAllCompanyOptions());

		return Result.success(allOptions);
	}

	/**
	 * 获取公司选项接口
	 * 用于前端下拉框显示
	 */
	@GetMapping("/companyOptions")
	@DataSource("tertiary")
	public Result<List<CompanyOptionVO>> getCompanyOptions() {
		List<CompanyOptionVO> companyOptions = companyOptionService.getAllCompanyOptions();
		return Result.success(companyOptions);
	}

	/**
	 * 刷新公司选项缓存接口
	 * 用于手动刷新缓存
	 */
	@GetMapping("/refreshCompanyOptions")
	@DataSource("tertiary")
	public Result<List<CompanyOptionVO>> refreshCompanyOptions() {
		List<CompanyOptionVO> companyOptions = companyOptionService.refreshCompanyOptions();
		return Result.success(companyOptions);
	}
}
