package com.xy.admin.vo.logApi;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xy.base.starter.dto.CommonPageQuery;
import lombok.*;

import java.time.LocalDateTime;

/**
 * API日志查询VO
 * <AUTHOR>
 * @since 2025/7/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class LogApiQueryVO extends CommonPageQuery {
    private static final long serialVersionUID = 1L;
    
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 日志类型，0主调，1被调
     */
    private Integer type;
    
    /**
     * URL
     */
    private String url;
    
    /**
     * 请求IP
     */
    private String ip;
    
    /**
     * 方法类型、POST、GET
     */
    private String method;
    
    /**
     * 请求参数
     */
    private String request;
    
    /**
     * 响应内容
     */
    private String response;
    
    /**
     * 花费时间范围查询 - 最小值（毫秒）
     */
    private Integer timingMin;
    
    /**
     * 花费时间范围查询 - 最大值（毫秒）
     */
    private Integer timingMax;
    
    /**
     * 创建时间范围查询 - 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTimeStart;
    
    /**
     * 创建时间范围查询 - 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTimeEnd;
    
    /**
     * 排序字段
     */
    private String sortField;
    
    /**
     * 排序方向：asc/desc
     */
    private String sortOrder;
}
