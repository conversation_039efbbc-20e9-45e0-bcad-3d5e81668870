<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.admin.mapper.ApiOrderMapper">
  <resultMap id="BaseResultMap" type="com.xy.admin.entity.ApiOrder">
    <!--@mbg.generated-->
    <!--@Table api_order-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="channel_no" jdbcType="VARCHAR" property="channelNo" />
    <result column="product_no" jdbcType="VARCHAR" property="productNo" />
    <result column="mobile_no" jdbcType="VARCHAR" property="mobileNo" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="order_status" jdbcType="VARCHAR" property="orderStatus" />
    <result column="out_order_status" jdbcType="VARCHAR" property="outOrderStatus" />
    <result column="out_order_id" jdbcType="VARCHAR" property="outOrderId" />
    <result column="operation_type" jdbcType="VARCHAR" property="operationType" />
    <result column="client_ip" jdbcType="VARCHAR" property="clientIp" />
    <result column="user_agent" jdbcType="VARCHAR" property="userAgent" />
    <result column="app_package" jdbcType="VARCHAR" property="appPackage" />
    <result column="app_name" jdbcType="VARCHAR" property="appName" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="page_url" jdbcType="VARCHAR" property="pageUrl" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="server" jdbcType="VARCHAR" property="server" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, app_id, channel_no, product_no, mobile_no, order_id, order_status, out_order_status, 
    out_order_id, operation_type, client_ip, user_agent, app_package, app_name, platform, 
    page_url, remark, create_time, update_time, server
  </sql>
  <update id="updateByPrimaryKey" parameterType="com.xy.admin.entity.ApiOrder">
    <!--@mbg.generated-->
    update api_order
    set app_id = #{appId,jdbcType=VARCHAR},
      channel_no = #{channelNo,jdbcType=VARCHAR},
      product_no = #{productNo,jdbcType=VARCHAR},
      mobile_no = #{mobileNo,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=VARCHAR},
      out_order_status = #{outOrderStatus,jdbcType=VARCHAR},
      out_order_id = #{outOrderId,jdbcType=VARCHAR},
      operation_type = #{operationType,jdbcType=VARCHAR},
      client_ip = #{clientIp,jdbcType=VARCHAR},
      user_agent = #{userAgent,jdbcType=VARCHAR},
      app_package = #{appPackage,jdbcType=VARCHAR},
      app_name = #{appName,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=VARCHAR},
      page_url = #{pageUrl,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      server = #{server,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>


</mapper>