<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.admin.mapper.ProInfoProductMapper">
  <resultMap id="BaseResultMap" type="com.xy.admin.entity.ProInfoProduct">
    <!--@mbg.generated-->
    <!--@Table pro_info_product-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company" jdbcType="VARCHAR" property="company" />
    <result column="agent" jdbcType="VARCHAR" property="agent" />
    <result column="product" jdbcType="VARCHAR" property="product" />
    <result column="pro" jdbcType="VARCHAR" property="pro" />
    <result column="carrier" jdbcType="VARCHAR" property="carrier" />
    <result column="product_no" jdbcType="VARCHAR" property="productNo" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="product_status" jdbcType="TINYINT" property="productStatus" />
    <result column="product_price" jdbcType="VARCHAR" property="productPrice" />
    <result column="promotion_province" jdbcType="VARCHAR" property="promotionProvince" />
    <result column="business_requirements" jdbcType="LONGVARCHAR" property="businessRequirements" />
    <result column="promotion_price" jdbcType="VARCHAR" property="promotionPrice" />
    <result column="params" jdbcType="VARCHAR" property="params" />
    <result column="development_status" jdbcType="TINYINT" property="developmentStatus" />
    <result column="reference_image" jdbcType="VARCHAR" property="referenceImage" />
    <result column="reference_link" jdbcType="VARCHAR" property="referenceLink" />
    <result column="remark" jdbcType="LONGVARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, company, agent, product, pro, carrier, product_no, product_name, product_status, 
    product_price, promotion_province, business_requirements, promotion_price, params, 
    development_status, reference_image, reference_link, remark, create_time, update_time
  </sql>
</mapper>