<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.admin.mapper.UrlBeianMapper">

    <resultMap id="BaseResultMap" type="com.xy.admin.entity.UrlBeian">
            <id property="url" column="url" jdbcType="VARCHAR"/>
            <result property="pro" column="pro" jdbcType="VARCHAR"/>
            <result property="entity" column="entity" jdbcType="VARCHAR"/>
            <result property="enable" column="enable" jdbcType="SMALLINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        url,pro,entity,
        enable,create_time,update_time
    </sql>
</mapper>
