<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.admin.mapper.CallbackReportMapper">

    <resultMap id="BaseResultMap" type="com.xy.admin.entity.CallbackReport">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="SMALLINT"/>
            <result property="reportStatus" column="report_status" jdbcType="SMALLINT"/>
            <result property="clickid" column="clickid" jdbcType="VARCHAR"/>
            <result property="response" column="response" jdbcType="VARCHAR"/>
            <result property="platform" column="platform" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,phone,type,
        report_status,clickid,response,
        platform,create_time,update_time
    </sql>
</mapper>
