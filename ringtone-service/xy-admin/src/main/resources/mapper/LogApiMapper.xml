<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.admin.mapper.LogApiMapper">
  <resultMap id="BaseResultMap" type="com.xy.admin.entity.LogApi">
    <!--@mbg.generated-->
    <!--@Table log_api-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="method" jdbcType="VARCHAR" property="method" />
    <result column="position" jdbcType="VARCHAR" property="position" />
    <result column="request" jdbcType="LONGVARCHAR" property="request" />
    <result column="response" jdbcType="LONGVARCHAR" property="response" />
    <result column="timing" jdbcType="INTEGER" property="timing" />
    <result column="exception" jdbcType="BIT" property="exception" />
    <result column="exception_name" jdbcType="VARCHAR" property="exceptionName" />
    <result column="exception_trace" jdbcType="LONGVARCHAR" property="exceptionTrace" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, phone, `type`, url, ip, `method`, `position`, request, response, timing, `exception`, 
    exception_name, exception_trace, create_time
  </sql>
  <update id="updateByPrimaryKey" parameterType="com.xy.admin.entity.LogApi">
    <!--@mbg.generated-->
    update log_api
    set phone = #{phone,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      url = #{url,jdbcType=VARCHAR},
      ip = #{ip,jdbcType=VARCHAR},
      `method` = #{method,jdbcType=VARCHAR},
      `position` = #{position,jdbcType=VARCHAR},
      request = #{request,jdbcType=LONGVARCHAR},
      response = #{response,jdbcType=LONGVARCHAR},
      timing = #{timing,jdbcType=INTEGER},
      `exception` = #{exception,jdbcType=BIT},
      exception_name = #{exceptionName,jdbcType=VARCHAR},
      exception_trace = #{exceptionTrace,jdbcType=LONGVARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>


</mapper>