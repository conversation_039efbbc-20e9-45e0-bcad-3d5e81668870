<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.admin.mapper.ConfigHuichuanMapper">
  <resultMap id="BaseResultMap" type="com.xy.admin.entity.ConfigHuichuan">
    <!--@mbg.generated-->
    <!--@Table config_huichuan-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="param_name" jdbcType="VARCHAR" property="paramName" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="nodelay" jdbcType="SMALLINT" property="nodelay" />
    <result column="huichuan_pct" jdbcType="INTEGER" property="huichuanPct" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, channel, `source`, param_name, platform, nodelay, huichuan_pct, create_time, 
    update_time
  </sql>
  <update id="updateByPrimaryKey" parameterType="com.xy.admin.entity.ConfigHuichuan">
    <!--@mbg.generated-->
    update config_huichuan
    set channel = #{channel,jdbcType=VARCHAR},
      `source` = #{source,jdbcType=VARCHAR},
      param_name = #{paramName,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=VARCHAR},
      nodelay = #{nodelay,jdbcType=SMALLINT},
      huichuan_pct = #{huichuanPct,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>