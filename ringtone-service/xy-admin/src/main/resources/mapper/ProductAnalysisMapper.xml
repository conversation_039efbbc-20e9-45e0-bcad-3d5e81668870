<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.admin.mapper.ProductAnalysisMapper">

    <!-- 概览统计查询 -->
    <select id="getOverviewStats" resultType="com.xy.admin.dto.productAnalysis.ProductAnalysisStatsDTO">
        SELECT
            COUNT(*) as totalCount,
            SUM(CASE WHEN a.operation_type = 'SMS' THEN 1 ELSE 0 END) as smsCount,
            SUM(CASE WHEN a.operation_type = 'ORDER' THEN 1 ELSE 0 END) as orderCount,
            COUNT(p.id) as orderSuccessCount
        FROM api_order a
        LEFT JOIN pro_order p ON a.order_id = p.order_id AND p.report_status = 1
        <where>
            a.create_time >= #{query.startTime}
            AND a.create_time &lt;= #{query.endTime}
            <if test="query.channelNo != null and query.channelNo != ''">
                AND a.channel_no = #{query.channelNo}
            </if>
            <if test="query.productNo != null and query.productNo != ''">
                AND a.product_no = #{query.productNo}
            </if>
            <if test="query.server != null and query.server != ''">
                AND a.server = #{query.server}
            </if>
        </where>
    </select>

    <!-- 按日期分组的统计查询 -->
    <select id="getDailyStats" resultType="com.xy.admin.dto.productAnalysis.ProductAnalysisStatsDTO">
        SELECT
            DATE_FORMAT(a.create_time, '%Y-%m-%d') as date,
            COUNT(*) as totalCount,
            SUM(CASE WHEN a.operation_type = 'SMS' THEN 1 ELSE 0 END) as smsCount,
            SUM(CASE WHEN a.operation_type = 'ORDER' THEN 1 ELSE 0 END) as orderCount,
            COUNT(p.id) as orderSuccessCount
        FROM api_order a
        LEFT JOIN pro_order p ON a.order_id = p.order_id AND p.report_status = 1
        <where>
            a.create_time >= #{query.startTime}
            AND a.create_time &lt;= #{query.endTime}
            <if test="query.channelNo != null and query.channelNo != ''">
                AND a.channel_no = #{query.channelNo}
            </if>
            <if test="query.productNo != null and query.productNo != ''">
                AND a.product_no = #{query.productNo}
            </if>
            <if test="query.server != null and query.server != ''">
                AND a.server = #{query.server}
            </if>
        </where>
        GROUP BY DATE_FORMAT(a.create_time, '%Y-%m-%d')
        ORDER BY date ASC
    </select>

</mapper>