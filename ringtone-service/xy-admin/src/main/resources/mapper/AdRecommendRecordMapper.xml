<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.admin.mapper.AdRecommendRecordMapper">

    <resultMap id="BaseResultMap" type="com.xy.admin.entity.AdRecommendRecord">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="sspTenantId" column="ssp_tenant_id" jdbcType="VARCHAR"/>
        <result property="sspAdId" column="ssp_ad_id" jdbcType="BIGINT"/>
        <result property="sspScene" column="ssp_scene" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="province" column="province" jdbcType="VARCHAR"/>
        <result property="city" column="city" jdbcType="VARCHAR"/>
        <result property="adId" column="ad_id" jdbcType="BIGINT"/>
        <result property="projectId" column="project_id" jdbcType="BIGINT"/>
        <result property="bid" column="bid" jdbcType="DECIMAL"/>
        <result property="link" column="link" jdbcType="VARCHAR"/>
        <result property="active" column="active" jdbcType="BIT"/>
        <result property="ua" column="ua" jdbcType="LONGVARCHAR"/>
        <result property="rawUrl" column="raw_url" jdbcType="LONGVARCHAR"/>
        <result property="activeTime" column="active_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="zssource" column="zssource" jdbcType="VARCHAR"/>
        <result property="zslevel" column="zslevel" jdbcType="VARCHAR"/>
        <result property="clickid" column="clickid" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,ssp_tenant_id,ssp_ad_id,ssp_scene,phone,province,city,ad_id,project_id,
        bid,link,active,ua,raw_url,active_time,create_time,zssource,zslevel,clickid
    </sql>

</mapper>