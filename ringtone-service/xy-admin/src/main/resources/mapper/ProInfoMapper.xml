<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.admin.mapper.ProInfoMapper">

    <resultMap id="BaseResultMap" type="com.xy.admin.entity.ProInfo">
            <id property="pro" column="pro" jdbcType="VARCHAR"/>
            <result property="channelCode" column="channel_code" jdbcType="VARCHAR"/>
            <result property="productId" column="product_id" jdbcType="VARCHAR"/>
            <result property="adminPhone" column="admin_phone" jdbcType="VARCHAR"/>
            <result property="hotline" column="hotline" jdbcType="VARCHAR"/>
            <result property="advertiser" column="advertiser" jdbcType="VARCHAR"/>
            <result property="uniqueAccId" column="unique_acc_id" jdbcType="VARCHAR"/>
            <result property="accPassword" column="acc_password" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        pro,channel_code,product_id,
        admin_phone,hotline,advertiser,
        unique_acc_id,acc_password,create_time,
        update_time
    </sql>
</mapper>
