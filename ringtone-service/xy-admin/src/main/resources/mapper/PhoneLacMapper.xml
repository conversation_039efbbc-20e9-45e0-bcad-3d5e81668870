<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.admin.mapper.PhoneLacMapper">
  <resultMap id="BaseResultMap" type="com.xy.admin.entity.PhoneLac">
    <!--@Table phone_lac-->
    <id column="seg" jdbcType="VARCHAR" property="seg" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="carrier" jdbcType="VARCHAR" property="carrier" />
    <result column="vcarrier" jdbcType="VARCHAR" property="vcarrier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    seg, province, city, carrier, vcarrier, create_time, update_time
  </sql>
</mapper>