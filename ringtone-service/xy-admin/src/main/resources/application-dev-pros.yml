spring:
  cloud:
    nacos:
      discovery:
        enabled: false
        server-addr: 192.168.1.158:8848
  datasource:
    dynamic:
      primary: primary # 设置默认的数据源
      strict: false # 严格匹配数据源，默认false. true未匹配到指定数据源时抛异常，false使用默认数据源
      datasource:
        primary:
          jdbc-url: ***********************************************************************************************************************************************************************************
          username: couser1
          password: USero01@ARd21@Liy2504
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            connection-timeout: 3000
            idle-timeout: 5000
            max-lifetime: 5500
            maximum-pool-size: 20
            minimum-idle: 5
        secondary:
          jdbc-url: ********************************************************************************************************************************************************************************
          username: couser1
          password: USero01@ARd21@Liy2504
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            connection-timeout: 3000
            idle-timeout: 5000
            max-lifetime: 5500
            maximum-pool-size: 20
            minimum-idle: 5
        tertiary:
          jdbc-url: *******************************************************************************************************************************************************************************
          username: couser1
          password: USero01@ARd21@Liy2504
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            connection-timeout: 3000
            idle-timeout: 5000
            max-lifetime: 5500
            maximum-pool-size: 20
            minimum-idle: 5
        fourth:
          jdbc-url: *******************************************************************************************************************************************************************************
          username: couser1
          password: USero01@ARd21@Liy2504
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            connection-timeout: 3000
            idle-timeout: 5000
            max-lifetime: 5500
            maximum-pool-size: 20
            minimum-idle: 5

  redis:
    database: 0
    host: 127.0.0.1
    password:
    port: 6379

logging:
  config: classpath:logback-spring-dev.xml
