spring:
  cloud:
    nacos:
      discovery:
        enabled: false
        server-addr: 192.168.1.158:8848
  datasource:
    dynamic:
      primary: primary # 设置默认的数据源
      strict: false # 严格匹配数据源，默认false. true未匹配到指定数据源时抛异常，false使用默认数据源
      datasource:
        primary:
          jdbc-url: ********************************************************************************************************************************************************************************
          username: couser1
          password: USero01@ARd21@Liy2504
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            connection-timeout: 3000
            idle-timeout: 5000
            max-lifetime: 5500
            maximum-pool-size: 20
            minimum-idle: 5
        secondary:
          jdbc-url: ***********************************************************************************************************************************************************************************
          username: couser1
          password: USero01@ARd21@Liy2504
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            connection-timeout: 3000
            idle-timeout: 5000
            max-lifetime: 5500
            maximum-pool-size: 20
            minimum-idle: 5
        tertiary:
          jdbc-url: *******************************************************************************************************************************************************************************
          username: couser1
          password: USero01@ARd21@Liy2504
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            connection-timeout: 3000
            idle-timeout: 5000
            max-lifetime: 5500
            maximum-pool-size: 20
            minimum-idle: 5
        fourth:
          jdbc-url: *******************************************************************************************************************************************************************************
          username: couser1
          password: USero01@ARd21@Liy2504
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            connection-timeout: 3000
            idle-timeout: 5000
            max-lifetime: 5500
            maximum-pool-size: 20
            minimum-idle: 5

  redis:
    database: 0
    host: **********
    password: Sqs@redis
    port: 6379

logging:
  config: classpath:logback-spring-test.xml

oss:
  # 上传到本地的配置，必须/结尾
  local: oss/
  ak: LTAI5tSSdbUVJzJc7vjDo2Fc
  voiceKey: EOiEyFRYbr6YbV1h
  secret: ******************************
  endPoint: oss-cn-chengdu.aliyuncs.com
  bucket: videofiles
  access: https://oss.widelink.net.cn
  accessLocal: http://127.0.0.1:8910/admin/res/