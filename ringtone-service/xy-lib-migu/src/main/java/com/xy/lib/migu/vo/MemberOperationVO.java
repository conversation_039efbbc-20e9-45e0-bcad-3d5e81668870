package com.xy.lib.migu.vo;


import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * 接口标题行
 *
 * @Author: David
 * @Date: 2023/11/15 21:31
 */
@Data
@ToString
public class MemberOperationVO {
    /**
     * 订单编码
     */
    private String orderId;

    /**
     * 部门ID（删除、修改时必填）
     */
    private String departmentId;

    /**
     * 用户手机号码 （限制50个号码量）
     */
    private List<String> billNums;

    /**
     * 用于查询用户信息的手机号码
     */
    private String billNum;

    /**
     * 用于查询用户信息的手机号码
     */
    private String phone;

    public void addCellphone(String cellphone){
        if(null == billNums){
            billNums = new ArrayList<>();
        }
        billNums.add(cellphone);
    }
}
