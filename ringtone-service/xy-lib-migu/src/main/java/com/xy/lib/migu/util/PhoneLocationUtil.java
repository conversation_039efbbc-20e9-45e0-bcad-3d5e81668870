package com.xy.lib.migu.util;

import com.xy.lib.migu.vo.Location;
import com.xy.lib.migu.vo.PhoneLocationType;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

public class PhoneLocationUtil {
    public static void main(String[] args) {
        String phoneNumber = "13512214639";
        long startTime = System.currentTimeMillis(); // 获取开始时间
        PhoneLocationType phoneLocationType = getLocationAndType(phoneNumber);
        long endTime = System.currentTimeMillis(); // 获取结束时间
        long elapsedTime = endTime - startTime; // 计算总耗时
        if (phoneLocationType != null) {
            System.out.println("归属地：" + phoneLocationType.getLocation().getProvince() + " " + phoneLocationType.getLocation().getCity());
            System.out.println("卡类型：" + phoneLocationType.getCarrier());
            System.out.println("耗时：" + elapsedTime + " 毫秒");
        }
    }

    public static PhoneLocationType getLocationAndType(String phoneNumber) {
        String url = "https://shouji.bmcx.com/" + phoneNumber + "__shouji/";
        try {
            URL obj = new URL(url);
            HttpURLConnection con = (HttpURLConnection) obj.openConnection();

            // 设置超时时间为5秒
            con.setConnectTimeout(5000);

            // 设置请求方法
            con.setRequestMethod("GET");

            BufferedReader in = new BufferedReader(
                    new InputStreamReader(con.getInputStream(), "UTF-8"));
            String inputLine;
            StringBuilder response = new StringBuilder();

            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }

            in.close();

            // 解析 HTML
            Document doc = Jsoup.parse(response.toString());
            Elements tables = doc.select("table");
            if (tables.size() > 0) {
                Element table = tables.get(0);
                Elements tds = table.select("td");
                if (tds.size() >= 6) {
                    String locationStr = tds.get(4).text(); // 归属地
                    String type = tds.get(6).text(); // 卡类型
                    Location location = parseLocation(locationStr);
                    return new PhoneLocationType(location, type);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    private static Location parseLocation(String locationStr) {
        String[] parts = locationStr.split(" ");
        if (parts.length == 2) {
            return new Location(parts[0], parts[1]);
        } else if (parts.length == 1) {
            return new Location(parts[0], "");
        } else {
            return new Location("", "");
        }
    }
}