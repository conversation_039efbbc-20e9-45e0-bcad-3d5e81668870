package com.xy.lib.migu.util;

import cn.hutool.db.Entity;
import com.xy.base.core.util.DbUtil;
import lombok.extern.slf4j.Slf4j;

import java.net.MalformedURLException;
import java.net.URL;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Slf4j
public class UrlUtil {

    // 从URL中提取域名
    public static String extractDomain(String input) {
        String output = input;
        try {
            URL url = new URL(input);
            output = url.getHost();
            log.info("get domain {} by url {}", output, input);
        } catch (MalformedURLException e) {
            // 如果不是URL，则可能是域名
        }
        return output;
    }

    public static void testExtractDomain() {
        String[] strings = {
                "widelink.net.cn",
                "yz0705.widelink.net.cn",
                "https://yz0705.widelink.net.cn/gluttony/page/?adsluid=wdprx34sg3qt2yn",
                "cdzs2584.zhonglianhuashu.com",
                "https://cdzs2584.zhonglianhuashu.com/app/info03.html?channel=zsgl&source=csj06&union_site=2055652382&projectid=7402878439093305363&promotionid=7402884232756740159&creativetype=3&clickid=ELnRponU-JoDGK3AsojXASDC76OODDAMOI-zzMkDQilkMzE3NDJlOC04YzllLTQ2MmYtODc3Ny00M2VlYjA0ZWQzYTl1MzU5NkiA0pOtA5ABAA&ad_id=1807344783815860&_toutiao_params=%7B%22cid%22%3A1807344806242489%2C%22device_id%22%3A3251173314%2C%22log_extra%22%3A%22%7B%5C%22ad_price%5C%22%3A%5C%22Zrl-_QAMhSxmuX79AAyFLFaBtO_zDpB0koFoag%5C%22%2C%5C%22city_id%5C%22%3Anull%2C%5C%22convert_id%5C%22%3A0%2C%5C%22country_id%5C%22%3Anull%2C%5C%22dma_id%5C%22%3Anull%2C%5C%22orit%5C%22%3A900000000%2C%5C%22province_id%5C%22%3Anull%2C%5C%22req_id%5C%22%3A%5C%22d31742e8-8c9e-462f-8777-43eeb04ed3a9u3596%5C%22%2C%5C%22rit%5C%22%3A959650191%7D%22%2C%22orit%22%3A900000000%2C%22req_id%22%3A%22d31742e8-8c9e-462f-8777-43eeb04ed3a9u3596%22%2C%22rit%22%3A959650191%2C%22sign%22%3A%22D41D8CD98F00B204E9800998ECF8427E%22%2C%22uid%22%3A57731227693%2C%22ut%22%3A12%7D"
        };

        for (String str : strings) {
            String domain = extractDomain(str);
            System.out.println("输入字符串: " + str);
            System.out.println("提取的域名: " + domain);
            System.out.println();
        }
    }

    public static void main(String[] args) {
        testExtractDomain();
    }
}
