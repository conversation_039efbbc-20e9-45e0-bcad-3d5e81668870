package com.xy.lib.migu.vo;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.IOException;
import java.util.List;

@Data
public class DepartmentInfo {
    @JsonProperty("departmentId")
    private String departmentId;

    @JsonProperty("departmentName")
    private String departmentName;

    @JsonProperty("adminMsisdn")
    private String adminMsisdn;

    @JsonProperty("createTime")
    private String createTime;

    public static List<DepartmentInfo> parseDepartments(String json) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        DepartmentInfo.Result result = mapper.readValue(json, DepartmentInfo.Result.class);
        return result.getData();
    }

    @Data
    public static class Result {
        @JsonProperty("code")
        private String code;

        @JsonProperty("info")
        private String info;

        @JsonProperty("data")
        private List<DepartmentInfo> data;
    }

    public static void main(String[] args) {
        String json = "{\"code\":\"000000\",\"info\":\"操作成功\",\"data\":[{\"departmentId\":\"3099995139547\",\"departmentName\":\"DPT13880718836\",\"adminMsisdn\":null,\"createTime\":\"20240206153427\"}]}";

        try {
            List<DepartmentInfo> departmentInfos = DepartmentInfo.parseDepartments(json);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}