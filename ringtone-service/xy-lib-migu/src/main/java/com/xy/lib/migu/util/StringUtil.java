package com.xy.lib.migu.util;

import com.google.gson.Gson;
import com.google.gson.JsonObject;

import java.net.URI;
import java.net.URISyntaxException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class StringUtil {

    public static String decrypt(String pro) {
        char[] decryptedChars = new char[pro.length()];

        for (int i = 0; i < pro.length(); i++) {
            char originalChar = pro.charAt(i);
            // originalChar 是否为数字
            if (originalChar >= '0' && originalChar <= '9') {
                decryptedChars[i] = originalChar;
                continue;
            }
            // 计算逆向位移量
            int shift = i + 1;
            // 对当前字符进行逆向位移解密
            char decryptedChar = shiftBack(originalChar, shift);
            decryptedChars[i] = decryptedChar;
        }

        return new String(decryptedChars);
    }

    // 向前位移字符
    private static char shiftBack(char c, int shift) {
        // 'a' 的 Unicode 码为 97, 'z' 的 Unicode 码为 122
        int originalPosition = c - 'a';
        int newPosition = (originalPosition - shift + 26) % 26; // 使用 +26 是为了处理负数情况
        return (char) ('a' + newPosition);
    }

    public static String md5(String input, String option) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(input.getBytes());
            byte[] digest = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b & 0xff));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return option;
    }

    public static String extractDomain(String urlString) {
        try {
            URI uri = new URI(urlString);
            String domain = uri.getHost();
            if (null == domain) {
                return urlString;
            } else {
                return domain;
            }
        } catch (URISyntaxException e) {
            e.printStackTrace();
        }
        return null;
    }


    public static String extractPackageName(String ua) {
        if (ua == null) {
            return null;
        }
        int index = ua.lastIndexOf('/');
        if (index != -1) {
            String name = ua.substring(index + 1);
            if (name.contains(".")) {
                return name;
            }
        }
        String[] sources = "WeChat,MicroMessenger".split(",");
        for (String src : sources) {
            if (ua.contains(src)) {
                return src;
            }
        }
        return null;
    }

    public static String getHuichuanJsonStrring(String clickid, String convert_type) {
        try {
            // Create JSON object for the request body
            JsonObject requestBody = new JsonObject();
            requestBody.addProperty("clickid", clickid);
            requestBody.addProperty("convert_type", convert_type);

            // Convert JSON object to string
            return new Gson().toJson(requestBody);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String getHuichuanJsonStrring(int code, String clickid, String convert_type, String message) {
        try {
            // Create JSON object for the request body
            JsonObject requestBody = new JsonObject();
            requestBody.addProperty("code", code);
            if (code == 200) {
                requestBody.addProperty("clickid", clickid);
                requestBody.addProperty("convert_type", convert_type);
            }
            requestBody.addProperty("message", message);

            // Convert JSON object to string
            return new Gson().toJson(requestBody);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public static void testExtractDomain() {
        String url1 = "https://card.hzjiuhuang.cn/f/kysx?pid=12572&zllink=Zr2ame";
        String url2 = "http://localhost:8081/h/11065/#/";
        String url3 = "https://www.baidu.com/";
        String url4 = "https://llggdsz.victorycd.cn/f/kysx?pid=14405&zllink=EVnUFv";
        String url5 = "zsgl05.diantads.com";

        System.out.println("Domain of " + url1 + ": " + extractDomain(url1));
        System.out.println("Domain of " + url2 + ": " + extractDomain(url2));
        System.out.println("Domain of " + url3 + ": " + extractDomain(url3));
        System.out.println("Domain of " + url4 + ": " + extractDomain(url4));
        System.out.println("Domain of " + url5 + ": " + extractDomain(url5));
    }

    public static void testUaExtract() {
        String ua1 = "Mozilla/5.0 (Linux; Android 14; PHF110 Build/UKQ1.230924.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Mobile Safari/537.36 open_news open_news_u_s/6104/com.whxk.wifi.qnysjsb";
        String ua2 = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x18003136) NetType/4G Language/zh_CN";
        String ua3 = "Mozilla/5.0 (Linux; Android 10; V1962A Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/122.0.6261.120 Mobile Safari/537.36 XWEB/1220067 MMWEBSDK/20230805 MMWEBID/7702 MicroMessenger/8.0.42.2460(0x28002A58) WeChat/arm64 Weixin NetType/5G Language/zh_CN ABI/arm64";
        String ua4 = "Mozilla/5.0 (Linux; Android 13; RMO-AN00 Build/HONORRMO-TN00; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/99.0.4844.88 Mobile Safari/537.36 open_news open_news_u_s/5928/red.ruyiluck.sardines";
        String ua5 = "Mozilla/5.0 (Linux; Android 13; PJS110 Build/TP1A.220905.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/103.0.5060.129 Mobile Safari/537.36 Mobads";

        System.out.println("Package name from UA1: " + extractPackageName(ua1));
        System.out.println("Package name from UA2: " + extractPackageName(ua2));
        System.out.println("Package name from UA3: " + extractPackageName(ua3));
        System.out.println("Package name from UA4: " + extractPackageName(ua4));
        System.out.println("Package name from UA5: " + extractPackageName(ua5));
    }

    public static void main(String[] args) {
        String pro = "oofl";
        String decrypted = decrypt(pro);
        System.out.println("解密后的结果: " + decrypted); // 输出 nmch
    }
}
