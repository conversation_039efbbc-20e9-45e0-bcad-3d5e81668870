package com.xy.lib.migu.util;

import com.xy.lib.migu.config.ConfigData;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.util.MultiValueMap;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Map;
import javax.net.ssl.*;
/**
 * 接口标题行
 *
 * @Author: David
 * @Date: 2024/1/13 19:45
 */

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLSession;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;

public class Https3 {
    private static final String BOUNDARY = "------------------------";
    private static final String LINE_END = "\r\n";

    public static void main(String[] args) {
        String url = "https://***************/evrms/v3/orderContent/foreign/submitRing";
        String orderId = ConfigData.orderId;
        String departmentId = "3099988961569";
        String ringName = "video1";
        String ringtoneFilePath = "D:\\zhongshi\\dev\\data\\video1.mp4";
        String copyrightFilePath = "D:\\zhongshi\\dev\\data\\中国移动企业视频彩铃内容上线著作权许可使用证明承诺书.pdf";
        String ringType = "1";

        try {
            URL requestUrl = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) requestUrl.openConnection();

            // 忽略SSL证书校验
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setHostnameVerifier(new HostnameVerifier() {
                    @Override
                    public boolean verify(String hostname, SSLSession session) {
                        return true;
                    }
                });
                trustAllHosts();
            }

            // 设置请求头
            Map<String, String> headers = HttpsUtil.getHeaders();
            headers.put("Content-Type", MediaType.MULTIPART_FORM_DATA.toString());

            for (Map.Entry<String, String> entry : headers.entrySet()) {
                connection.setRequestProperty(entry.getKey(), entry.getValue());
            }

            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setDoInput(true);

            DataOutputStream dos = new DataOutputStream(connection.getOutputStream());

            dos.writeBytes("--" + BOUNDARY + LINE_END);
            dos.writeBytes("Content-Disposition: form-data; name=\"orderId\"" + LINE_END + LINE_END);
            dos.writeBytes(orderId + LINE_END);

            dos.writeBytes("--" + BOUNDARY + LINE_END);
            dos.writeBytes("Content-Disposition: form-data; name=\"departmentId\"" + LINE_END + LINE_END);
            dos.writeBytes(departmentId + LINE_END);

            dos.writeBytes("--" + BOUNDARY + LINE_END);
            dos.writeBytes("Content-Disposition: form-data; name=\"ringName\"" + LINE_END + LINE_END);
            dos.writeBytes(ringName + LINE_END);

            dos.writeBytes("--" + BOUNDARY + LINE_END);
            dos.writeBytes("Content-Disposition: form-data; name=\"ringType\"" + LINE_END + LINE_END);
            dos.writeBytes(ringType + LINE_END);

            dos.writeBytes("--" + BOUNDARY + LINE_END);
            dos.writeBytes("Content-Disposition: form-data; name=\"ringFile\";filename=\"" + ringtoneFilePath + "\"" + LINE_END);
            dos.writeBytes("Content-Type: application/octet-stream" + LINE_END + LINE_END);
            FileInputStream fis = new FileInputStream(new File(ringtoneFilePath));
            byte[] buffer = new byte[1024];
            int len;
            while ((len = fis.read(buffer)) != -1) {
                dos.write(buffer, 0, len);
            }
            fis.close();
            dos.writeBytes(LINE_END);

            dos.writeBytes("--" + BOUNDARY + LINE_END);
            dos.writeBytes("Content-Disposition: form-data; name=\"copyrightFile\";filename=\"" + copyrightFilePath + "\"" + LINE_END);
            dos.writeBytes("Content-Type: application/octet-stream" + LINE_END + LINE_END);
            fis = new FileInputStream(new File(copyrightFilePath));
            while ((len = fis.read(buffer)) != -1) {
                dos.write(buffer, 0, len);
            }
            fis.close();
            dos.writeBytes(LINE_END);

            dos.writeBytes("--" + BOUNDARY + "--" + LINE_END);
            dos.flush();
            dos.close();

            // 获取响应结果
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpsURLConnection.HTTP_OK) {
                // 读取响应
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // 打印响应结果
                System.out.println("铃音上传成功：" + response.toString());
            } else {
                System.out.println("铃音上传失败，状态码：" + responseCode);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static void trustAllHosts() {
        // 这个方法用于忽略所有SSL证书验证，慎用！
        HttpsURLConnection.setDefaultHostnameVerifier(new HostnameVerifier() {
            public boolean verify(String hostname, SSLSession session) {
                return true;
            }
        });
    }
}