package com.xy.lib.migu.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.Data;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.List;

@Data
public class DepartmentObject {
    /**
     * 部门ID
     */
    @JsonProperty("departmentId")
    private String departmentId;

    /**
     * 部门名称
     */
    @JsonProperty("departmentName")
    private String departmentName;

    /**
     * 订单编码
     */
    @JsonProperty("orderId")
    private String orderId;

    /**
     * 成员号码
     */
    @JsonProperty("billNum")
    private String billNum;

    /**
     * 成员添加时间
     */
    @JsonProperty("createTime")
    private String createTime;

    /**
     * 成员归属省份Id
     */
    @JsonProperty("provinceId")
    private String provinceId;

    /**
     * 省份名称
     */
    @JsonProperty("provinceName")
    private String provinceName;

    /**
     * 成员归属地市Id
     */
    @JsonProperty("locationId")
    private String locationId;

    /**
     * 地市名称
     */
    @JsonProperty("locationName")
    private String locationName;

    /**
     * 成员状态
     */
    @JsonProperty("userStatus")
    private String userStatus;

    /**
     * 企业名称
     */
    @JsonProperty("ecName")
    private String ecName;

    public static List<DepartmentObject> parseDepartments(String json) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        Result result = mapper.readValue(json, Result.class);
        return result.getData();
    }

    @Data
    public static class Result {
        @JsonProperty("code")
        private String code;

        @JsonProperty("info")
        private String info;

        @JsonProperty("data")
        private List<DepartmentObject> data;
    }
}