package com.xy.lib.migu.online;

import cn.hutool.core.util.IdUtil;
import com.xy.base.core.exception.AppException;
import com.xy.lib.migu.config.ConfigData;
import com.xy.lib.migu.util.NetUtil;
import com.xy.lib.migu.vo.DepartmentInfo;
import com.xy.lib.migu.vo.DepartmentObject;
import com.xy.lib.migu.vo.EnterpriseDeptOperationVO;
import com.xy.lib.migu.vo.EnterpriseDeptQueryVO;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONUtil;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

import static com.xy.lib.migu.config.ConfigData.adminMsisdn;
import static com.xy.lib.migu.config.ConfigData.orderId;

/**
 * 接口标题行
 *
 * @Author: David
 * @Date: 2023/11/16 17:36
 */
@Slf4j
public class DepartmentOperation {

    /**
     * 部门操作，注意：部门不为空时，部门删除不掉
     * @param deptOperationVO
     */
    public static void operateDept(EnterpriseDeptOperationVO deptOperationVO) {
        // 接口 URL
        String url = NetUtil.getUrl("/evrms/v3/orderContent/foreign/ecOperation");

        // 补充请求参数
        deptOperationVO.setOrderId(orderId);
        deptOperationVO.setAdminMsisdn(adminMsisdn);

        ResponseEntity<String> response = NetUtil.getResponse(url, deptOperationVO);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    /**
     * 该方法通过自行组织部门名称对部门ID进行查询
     *
     * @param cellphone
     * @return
     */
    @Deprecated
    private static String tryQueryDeptId(String cellphone) {
        String deptString = queryDept(ConfigData.orderId, "DPT" + cellphone);
        try {
            List<DepartmentInfo> departmentInfos = DepartmentInfo.parseDepartments(deptString);
            if(null == departmentInfos || departmentInfos.size() == 0){
                return null;
            }
            return departmentInfos.get(0).getDepartmentId();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 检查手机号对应的部门是否为空部门，空部门时对其进行删除
     * 正常方式目前做不到：1. 确认是否为空部门需要根据部门id查询部门成员；2.用户已被删除是，已经查不出其原先对应的部门id
     * 可借助自行组装部门名称查询部门id
     * @param cellphone
     * @return
     */
    public static boolean tryRemoveIfEmpty(String cellphone) {
        try {
            String departmentId = null;
            try {
                departmentId = MemberOperation.queryDeptId(cellphone);
            } catch (Exception e1) {
                e1.printStackTrace();
            }
            // String departmentId = "3100000077677";
            if (null == departmentId) {
                departmentId = tryQueryDeptId(cellphone);
            }
            if(null == departmentId){
                return false;
            }
            List<DepartmentObject> departmentObjects = MemberOperation.queryDeptMember(departmentId);
            if (null != departmentObjects && departmentObjects.size() == 0) {
                // 清理空部门
                EnterpriseDeptOperationVO vo = new EnterpriseDeptOperationVO();
                vo.setDepartmentId(departmentId);
                vo.setOpType("1");
                operateDept(vo);
                log.info("成功为 {} 删除空部门，该空部门id={}", cellphone, departmentId);
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public static String queryDept(String orderId) {
        // 构造请求参数
        EnterpriseDeptQueryVO deptQueryVO = new EnterpriseDeptQueryVO();
        deptQueryVO.setOrderId(orderId);

        return queryDept(deptQueryVO);
    }

    public static String queryDept(String orderId, String departmentName) {
        // 构造请求参数
        EnterpriseDeptQueryVO deptQueryVO = new EnterpriseDeptQueryVO();
        deptQueryVO.setOrderId(orderId);
        deptQueryVO.setDepartmentName(departmentName);

        return queryDept(deptQueryVO);
    }

    public static String queryDept(EnterpriseDeptQueryVO deptQueryVO) {
        // 接口 URL
        String url = NetUtil.getUrl("/evrms/v3/orderContent/foreign/searchEcDepartments");

        ResponseEntity<String> response = NetUtil.getResponse(url, deptQueryVO);
        String responseBody = null;

        if (response.getStatusCode().is2xxSuccessful()) {
            responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
        return responseBody;
    }

    public static void main(String[] args) {
//        queryDeptId("19709741289");13551861290
//        tryRemoveIfEmpty("13880899948");
        tryRemoveIfEmpty("13551861290");
//        tryRemoveIfEmpty("13880718836");
    }
}
