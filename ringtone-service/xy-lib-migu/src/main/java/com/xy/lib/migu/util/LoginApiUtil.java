package com.xy.lib.migu.util;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.gson.Gson;
import com.xy.base.core.enums.ExceptionResultEnum;
import com.xy.base.core.exception.AppException;
import com.xy.base.core.util.DbUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.utils.URIBuilder;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;

import com.xy.base.starter.redis.RedisUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.xy.lib.migu.online.MemberOperation;

import static com.xy.lib.migu.util.StringUtil.md5;

@Slf4j
public class LoginApiUtil {
    /**
     * 调用示例：http://m.12530.com/order/rest/crbt/centrality/secret/url.do?
     * data={"timestamp":"20231207113029","signature":"13d395a13cb9fc0a59f3603f9c039baf","channelCode":"014X001","msisdn":"1848226","key":"4908ce67e584dc03"}
     */

    private static LoginConfig getLoginConfig(String pro) {
        String BASE_URL = "http://m.12530.com/order/rest/crbt/centrality/secret/url.do";
        String SECRET_KEY = "";
        String channelCode = "";
        String key = "";

        if ("zsgl".equals(pro) || "zs02".equals(pro)) {
            SECRET_KEY = "4efad85a7c694ef2a39e2c4fc3cd56ae";
            channelCode = "014X0FP";
            key = "46ab42eb45904d5590a50f7f3f14b4e3";
        } else if ("nmch".equals(pro)) {
            SECRET_KEY = "129d3f76758243518c4d7395bb2c200c";
            channelCode = "014X0GP";
            key = "414b4a30b90c4036acdc8621c3fbbdf2";
        } else if ("jxzw".equals(pro)) {
            SECRET_KEY = "3cc72160bfea462f9fce3bb9cad22184";
            channelCode = "014X0EN";
            key = "713a1c3ef79e4000ba71ff08e639939d";
        } else if ("gdsz".equals(pro)) {
            SECRET_KEY = "da7aebfa7f00474bb4ff464af730bb31";
            channelCode = "014X0FR";
            key = "1cc19adb84dc4d9c8c02f00e55e1f2c7";
        } else if ("jxlz".equals(pro)) {
            SECRET_KEY = "0bce332af86648329cc1c8f9bceb48f9";
            channelCode = "014X0G5";
            key = "5b8a58bf3f94472791e436cc3ef73611";
        } else if ("gdys".equals(pro)) {
            SECRET_KEY = "87fcb6e46c014979a6294fe28feb1db3";
            channelCode = "014X0FS";
            key = "df45f4dfb5dc475ca4acf5530be2de1e";
        }
        AppException.tnt(SECRET_KEY == "", "错误的PRO参数:" + pro);

        return new LoginConfig(BASE_URL, SECRET_KEY, channelCode, key);
    }

    private static String formatJson(String jsonString) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.enable(SerializationFeature.INDENT_OUTPUT);

            Object json = objectMapper.readValue(jsonString, Object.class);
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(json);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private static String currentTime() {
        // 创建 SimpleDateFormat 实例，指定日期时间格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss.SSS");
        // 格式化当前时间
        return dateFormat.format(new Date());
    }

    // 补充公司名，以及pro配置信息
    private static String putInfo(JSONObject jsonObject, String cellphone, String pro) {
        try {
            // 补充公司名/部门名
            String dept = CompanyUtil.getCompanyName(cellphone);
            jsonObject.put("dept", dept);
            jsonObject.put("pro", pro);
            // 补充pro_info信息
            Entity entity = Db.use().queryOne("select * from pro_info where pro = ?", pro);
            jsonObject.put("channelCode", entity.getStr("channel_code"));
            jsonObject.put("productId", entity.getStr("product_id"));
            jsonObject.put("adminPhone", entity.getStr("admin_phone"));
            jsonObject.put("hotline", entity.getStr("hotline"));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("捕获到异常: ", e);
        }
        return jsonObject.toString();
    }

    // 内蒙辰华登录
    private static String loginNmch(String cellphone) {

        String apiUrl = "http://www.chwx.top/ssjy/loginToken/token/getTokenThree";

        // 构建请求
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(apiUrl)
                .queryParam("phone", cellphone);

        URI uri = builder.build(true).toUri();

        String response = NetUtil.sendHttpRequest(uri, createHttpEntity(), HttpMethod.POST);

        try {
            log.info("response:" + response);
            // 解析 JSON
            JSONObject jsonObject = new JSONObject(response);
            JSONObject jsonObject2 = new JSONObject();
            int code = jsonObject.getInt("code");
            // 仅当token获取成功时 才cache手机号和 token
            if (code == 200) {
                String token = jsonObject.getJSONObject("data").getString("token");
                RedisUtils.set(cellphone, token, 3600);
                log.info("set Redis for cellphone: {}, token: {}", cellphone, token);
                jsonObject2.put("token", token);
            }
            return putInfo(jsonObject2, cellphone, "nmch");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return null;
    }

    // 西安摩威登录
    private static String loginXamw(String cellphone) {
        String apiUrl = "https://migu.xianmowei.com/api/order/sendsinfo";

        // 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 创建请求参数
        Map<String, String> requestParams = new HashMap<>();
        requestParams.put("mobile", cellphone);
        requestParams.put("distributor_id", "202503091420108444051");

        // 将请求参数转换为 JSON 字符串
        Gson gson = new Gson();
        String jsonBody = gson.toJson(requestParams);

        // 构建 HttpEntity
        HttpEntity<String> requestEntity = new HttpEntity<>(jsonBody, headers);


        try {
            String response = NetUtil.sendHttpRequest(apiUrl, requestEntity, HttpMethod.POST);
            log.info("xamw token response:" + response);
            // 解析 JSON
            JSONObject jsonObject = new JSONObject(response);
            JSONObject jsonObject2 = new JSONObject();
            int code = jsonObject.getInt("code");
            // 仅当token获取成功时 才cache手机号和 token
            if (code == 1) {
                String token = jsonObject.getJSONObject("data").getString("token");
                RedisUtils.set(cellphone, token, 3600);
                log.info("set Redis for cellphone: {}, token: {}", cellphone, token);
                jsonObject2.put("token", token);
            }
            return putInfo(jsonObject2, cellphone, "xamw");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return null;
    }


    private static String doLogin(String cellphone, String pro) {
        if ("xamw".equals(pro) || null == pro) {
            return loginXamw(cellphone);
        }
        if ("nmch".equals(pro)) {
            return loginNmch(cellphone);
        }
        if ("zsgl".equals(pro) || "zs02".equals(pro)) {
            log.info("starting member validate for phone {}", cellphone);
            // 去咪咕检查用户是否可以订购
            boolean canSubscription = MemberOperation.canSubscription(cellphone);
            log.info("phone={} member validate result={}", cellphone, canSubscription);
            AppException.tnt(!canSubscription, ExceptionResultEnum.OTHER_FAILED_PHONE_LIMIT);
        }

        LoginConfig config = getLoginConfig(pro);

        String BASE_URL = config.getBASE_URL();
        String SECRET_KEY = config.getSECRET_KEY();
        String channelCode = config.getChannelCode();
        String key = config.getKey();

        log.info("开始获取企业视频彩铃token，使用参数：{channelCode:{}, key:{}, msisdn:{}}", channelCode, key, cellphone);

        String timestamp = getCurrentTimestamp();
        String signature = generateSignature(timestamp, channelCode, SECRET_KEY);

        // 构建请求参数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("signature", signature);
        paramMap.put("msisdn", cellphone);
        paramMap.put("timestamp", timestamp);
        paramMap.put("channelCode", channelCode);
        paramMap.put("key", key);
        String jsonParams = generateJsonParams(paramMap);

        // 构建请求
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(BASE_URL)
                .queryParam("data", urlEncode(jsonParams));

        URI uri = builder.build(true).toUri();

        Map<String, String> loginAction = new HashMap<>();
        loginAction.put("action", "一键登录");
        loginAction.put("user", cellphone);
        try {
            loginAction.put("url", URLDecoder.decode(uri.toString(), "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        String loginActionString = generateJsonParams(loginAction);
        log.info("\n{} [main] INFO LoginApiUtil - \n{}", currentTime(), formatJson(loginActionString));

        String response = NetUtil.sendHttpRequest(uri, createHttpEntity(), HttpMethod.GET);
        JSONObject jsonObject = cache(cellphone, response);

        Map<String, Object> responseAction = new HashMap<>();
        responseAction.put("code", "200");
        responseAction.put("message", "登录成功");
        responseAction.put("info", jsonObject);
        log.info("\n{} [main] INFO LoginApiUtil - \n{}", currentTime(), formatMap(responseAction));

        return putInfo(jsonObject, cellphone, pro);
    }

    private static String loginNew(String cellphone, String pro) {
        // 手机号合法性验证
        boolean validatePass = PhoneUtil.validate(cellphone, pro);
        AppException.tnt(!validatePass, ExceptionResultEnum.CELLPHONE_VALIDATE_FAILED);

        return doLogin(cellphone, pro);
    }

    private static String loginNew(String cellphone, String pro, String url, String ua) {
        // 手机号合法性验证
        boolean validatePass = PhoneUtil.validate(cellphone, pro, url, ua);
        AppException.tnt(!validatePass, ExceptionResultEnum.CELLPHONE_VALIDATE_FAILED);

        return doLogin(cellphone, pro);
    }

    private static String loginNew(String cellphone, String pro, String url, String ua, String unionSite) {
        // 手机号合法性验证
        boolean validatePass = PhoneUtil.validate(cellphone, pro, url, ua, unionSite);
        AppException.tnt(!validatePass, ExceptionResultEnum.CELLPHONE_VALIDATE_FAILED);

        return doLogin(cellphone, pro);
    }

    private static String loginNew(String cellphone, String pro, String url, String ua, String unionSite, String ft) {
        // 手机号合法性验证
        boolean validatePass = PhoneUtil.validate(cellphone, pro, url, ua, unionSite, ft);
        AppException.tnt(!validatePass, ExceptionResultEnum.CELLPHONE_VALIDATE_FAILED);

        return doLogin(cellphone, pro);
    }

    // 格式化输出 Map
    private static String formatMap(Map<String, Object> map) {
        StringBuilder builder = new StringBuilder();
        builder.append("{\n");
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            builder.append("  \"").append(entry.getKey()).append("\" : ");
            if (entry.getValue() instanceof JSONObject) {
                try {
                    builder.append(((JSONObject) entry.getValue()).toString(6));
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            } else {
                builder.append("\"").append(entry.getValue()).append("\"");
            }
            builder.append(",\n");
        }
        builder.delete(builder.length() - 2, builder.length()); // 删除最后一个逗号和换行
        builder.append("\n}");
        return builder.toString();
    }

    public static String login(String cellphone, String channel, String source, String pro) {
        pro = loginPreCheck(cellphone, channel, source, pro, null, null, null, null);

        smsSend(cellphone);
        // 一旦推广暴增，此处频繁调用数据库进行写入可能不妥【带宽、数据库连接】
        // saveFrontUser(cellphone, channel, source);
        return loginNew(cellphone, pro);
    }

    public static String login(String cellphone, String channel, String source, String pro, String url, String ua) {
        pro = loginPreCheck(cellphone, channel, source, pro, url, ua, null, null);

        smsSend(cellphone);
        // 一旦推广暴增，此处频繁调用数据库进行写入可能不妥【带宽、数据库连接】
        // saveFrontUser(cellphone, channel, source);
        return loginNew(cellphone, pro, url, ua);
    }

    public static String login(String cellphone, String channel, String source, String pro, String url, String ua, String unionSite) {
        pro = loginPreCheck(cellphone, channel, source, pro, url, ua, unionSite, null);

        smsSend(cellphone);
        // 一旦推广暴增，此处频繁调用数据库进行写入可能不妥【带宽、数据库连接】
        // saveFrontUser(cellphone, channel, source);
        return loginNew(cellphone, pro, url, ua, unionSite);
    }

    // 根据参数决定如何进行过滤
    public static String login(String cellphone, String channel, String source, String pro, String url, String ua, String unionSite, String ft) {
        pro = loginPreCheck(cellphone, channel, source, pro, url, ua, unionSite, ft);

        smsSend(cellphone);
        // 一旦推广暴增，此处频繁调用数据库进行写入可能不妥【带宽、数据库连接】
        // saveFrontUser(cellphone, channel, source);
        return loginNew(cellphone, pro, url, ua, unionSite, ft);
    }

    public static String loginAPI(String cellphone, String channel, String source, String pro, String url, String ua) {
        pro = loginPreCheck(cellphone, channel, source, pro, url, ua, null, null);

        smsSend(cellphone);
        // 一旦推广暴增，此处频繁调用数据库进行写入可能不妥【带宽、数据库连接】
        // saveFrontUser(cellphone, channel, source);
        return loginNew(cellphone, pro, url, ua, null, null);
    }

    public static String loginAPI(String cellphone, String channel, String source, String url, String ua) {
        String pro = loginPreCheck(cellphone, channel, source, null, url, ua, null, null);

        smsSend(cellphone);
        // 暂不对来自欣网API和易尊API的请求进行UA过滤
        if (("xwhl".equals(channel) || "gdmbe".equals(channel)) && "api01".equals(source)) {
            return loginNew(cellphone, pro);
        } else {
            return loginNew(cellphone, pro, url, ua);
        }
    }

    /*
     * 登录前验证
     * */
    private static String loginPreCheck(String cellphone, String channel, String source, String pro, String url, String ua, String unionSite, String ft) {
        // 提取域名，预防部分情况下，url传递的并非域名
        url = UrlUtil.extractDomain(url);
        pro = ProUtil.usePro(pro, url);

        log.info("login using phone={} with channel={} , source={}, pro={}, url={} , ua={}, us={}, ft={}", cellphone, channel, source, pro, url, ua, unionSite, ft);

        AppException.tnt((null == cellphone || null == channel || null == source), ExceptionResultEnum.PARAMS_ERROR);

        // 此时为url错误
        AppException.tnt(null == pro, ExceptionResultEnum.ACCESS_DENIED);

        ProUtil.validateProSwitch(cellphone, pro, channel);

        // 强行将南京欣网的请求全部转发给 西安摩威 Todo 用完后记得注释掉
        // 但由于网页SDK参数的原因，nmch后续提交订购会失败，仅有zsgl会成功
        // 实际情况：xwhl极有可能把参数写死了，导致西安摩威无转化（接口通过自己的页面测试成功，且xwhl无法求证其代码是否正确）
//        if ("xwhl".equals(channel)) {
//            log.info("强制转发南京欣网请求到西安摩威，pro 由 {} 变为 {}", pro, "xamw");
//            pro = "xamw";
//        }

        return pro;
    }

    // 登录短信下发
    private static void smsSend(String cellphone) {
        String validateCode = RandomUtil.randomNumbers(6);
        log.info("登录短信下发，手机号：{}，下发验证码：{}，code=OK, message=OK，下发成功！", cellphone, validateCode);
        log.info("登录成功，手机号：{}，验证码：{}，\"code\":200, \"status\":success，验证成功！", cellphone, validateCode);
    }

    private static void saveFrontUser(String cellphone, String channel, String source) {
        Entity entity = Entity.create("log_front_user")
                .set("id", IdUtil.getSnowflake().nextId())
                .set("channel", channel)
                .set("source", source)
                .set("phone", cellphone);
        try {
            log.info(entity.toString());
            Db.use().insert(entity);
        } catch (SQLException e) {
            e.printStackTrace();
            log.error("捕获到异常: ", e);
        }
    }

    private static JSONObject cache(String cellphone, String response) {
        try {
            log.info("response:" + response);
            // 解析 JSON
            JSONObject jsonObject = new JSONObject(response);
            String resCode = jsonObject.getString("resCode");
            // 仅当token获取成功时 才cache手机号和 token
            if (resCode.equals("000000")) {
                String token = jsonObject.getString("token");
                RedisUtils.set(cellphone, token, 3600);
                log.info("set Redis for cellphone: {}, token: {}", cellphone, token);
            }
            return jsonObject;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return null;
    }

    private static HttpEntity<String> createHttpEntity() {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        return new HttpEntity<>(null, headers);
    }

    private static String generateJsonParams(Map<String, String> params) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(params);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            return "";
        }
    }

    private static String urlEncode(String value) {
        try {
            return URLEncoder.encode(value, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            return value;
        }
    }

    private static String getCurrentTimestamp() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        return now.format(formatter);
    }

    private static String generateSignature(String timestamp, String channelCode, String SECRET_KEY) {
        String text = channelCode + timestamp + SECRET_KEY;
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            byte[] digest = messageDigest.digest(text.getBytes());

            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        }
    }

    private static void fortest() {
        // 创建一个新的 Map 来保存 responseAction
        Map<String, Object> responseAction = new HashMap<>();
        responseAction.put("code", "200");
        responseAction.put("message", "登录成功");

        // response 字符串
        String response = "{\"token\":\"639ecc129bca44c78f01fe99720ee2da\",\"msisdn\":\"135*****638\",\"resCode\":\"000000\",\"resMsg\":\"【OPEN】操作成功\"}";

        try {
            // 将 response 字符串解析为 JSONObject
            JSONObject responseObject = new JSONObject(response);

            // 将 responseObject 放入 responseAction 的 info 字段
            responseAction.put("info", responseObject);

            // 打印出格式化后的 responseAction
            System.out.println(formatMap(responseAction));
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
//        String response = LoginApiUtil.loginNew("13880899950", "nmch");
//        String response = LoginApiUtil.login("13880899950", "zsgl", "csj02", "nmch");
//        System.out.println("response:" + response);
        // fortest();
        DbUtil.setActive("dev-zsgl");
//        String loginPreCheck = LoginApiUtil.loginPreCheck("13088032952", "zsgl", "csj02", "nmch", "https://ch2410.widelink.net.cn/app/view/chrhb/index.html?channel=zsgl&source=csj02", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3", "unionSite", "ft");
//        System.out.println("loginPreCheck:" + loginPreCheck);
//        String response = LoginApiUtil.loginXamw("13688032952");
        String loginPreCheck = LoginApiUtil.login("13088032952", "xwhl", "csj02", "nmch", "https://ch2410.widelink.net.cn/app/view/chrhb/index.html?channel=zsgl&source=csj02", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3", "unionSite", "ft");
//
    }
}
