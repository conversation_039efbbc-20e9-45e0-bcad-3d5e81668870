package com.xy.lib.migu.util;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.xy.base.core.enums.ExceptionResultEnum;
import com.xy.base.core.exception.AppException;
import com.xy.base.core.util.DbUtil;
import com.xy.base.starter.redis.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.security.core.parameters.P;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class ProUtil {
    private static final Map<String, String> convertPros = new HashMap<>();

    // 内蒙辰华登录
    public static void nmchSaveUser(String cellphone) {
        log.info("in nmchSaveUser");
        try {
            String apiUrl = "http://www.chwx.top/ssjy/loginToken/saveUser";
            // 构建请求
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(apiUrl)
                    .queryParam("phone", cellphone).queryParam("ecoperationId", "1783698831680049153");

            URI uri = builder.build(true).toUri();

            String response = NetUtil.sendHttpRequest(uri, createHttpEntity(), HttpMethod.POST);

            log.info("response:" + response);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("捕获到异常: ", e);
        }
    }

    // 检查当前pro在某个channel下是否已暂停发展
    public static boolean validateProSwitch(String cellphone, String pro, String channel) {
        Db db = Db.use();
        try {
            Entity entity = db.queryOne("SELECT * FROM pro_switch WHERE pro = ? and channel = ? limit 1", pro, channel);
            if (null == entity) {
                return true;
            } else {
                int enable = entity.getInt("enable");
                if (enable == 0) {
                    log.info("cellphone {}  validateProSwitch result: false", cellphone);
                    AppException.tnt(ExceptionResultEnum.CHANNEL_PAUSED);
                }
            }
        } catch (SQLException e) {
            log.error("验证validateProSwitch过程中发生SQL异常", e);
        }
        return true;
    }

    // 获取有效的pro
    public static String usePro(String pro, String url) {
        if (null == pro) {
            pro = ProUtil.getProByUrl(url);
        } else {
            pro = ProUtil.tryTranslatePro(pro);
        }
        return pro;
    }

    public static String tryConvertSource(String pro, String channel, String source, String phone) {
        String src = source;
        if(phone != null) {
            log.info("received pro:{}, channel:{}, source:{}, phone:{}", pro, channel, source, phone);
            // 获取缓存的channel和source信息
            String channelSource = RedisUtils.get("channel_src_ori:" + phone);

            if (channelSource != null && !channelSource.isEmpty()) {
                String[] channelParts = channelSource.split(":");
                if (channelParts.length >= 3) {
                    source = channelParts[1];
                    pro = channelParts[2];
                }
            }else{
                Entity entity = null;
                try {
                    entity = Db.use().queryOne("SELECT * FROM access_token WHERE channel = ?  and phone = ? limit 1", channel, phone);
                    pro = entity.getStr("pro");
                    src = entity.getStr("source");
                    log.info("convert source from {} to {} with pro {} and channel {}", source, src, pro, channel);
                    return src;
                } catch (SQLException e) {
                    log.error("捕获到异常: ", e);
                }
            }
            log.info("original pro:{}, channel:{}, source:{}, phone:{}", pro, channel, source, phone);
        }
        pro = doTranslatePro(pro);
        if ("xmllg".equals(channel) && "api01".equals(source)) {
            if ("jxzw".equals(pro)) {
                src = "api02";
            }
            /*else if ("nmch".equals(pro)) {
                src = "api03";
            }*/
        }
        log.info("convert source from {} to {} with pro {} and channel {}", source, src, pro, channel);
        return src;
    }

    private static void initConvertPros() {
        if (!convertPros.isEmpty()) {
            return;
        }
        Db db = Db.use();
        try {
            List<Entity> list = db.query("SELECT * FROM pro_convert");
            if (null == list || list.isEmpty()) {
                return;
            }
            for (Entity entity : list) {
                String prof = entity.getStr("prof");
                String prot = entity.getStr("prot");
                convertPros.put(prof, prot);
            }
        } catch (SQLException e) {
            log.error("捕获到异常: ", e);
        }
    }


    private static String tryConvertPro(String pro) {
        initConvertPros();
        return convertPros.get(pro);
    }

    // 对pro进行解密
    public static String tryTranslatePro(String pro) {
        if (null == pro) {
            return null;
        }
        String convertPro = tryConvertPro(pro);
        if (null != convertPro) {
            log.info("convert pro from {} to {}", pro, convertPro);
            return convertPro;
        }
        return doTranslatePro(pro);
    }

    // 对pro进行解密
    private static String doTranslatePro(String pro) {
        if (null == pro) {
            return null;
        }
        if ("nmch".equals(pro) || "jxzw".equals(pro) || "xamw".equals(pro) || "gdsz".equals(pro) || "jxlz".equals(pro) || "gdys".equals(pro) || "zsgl".equals(pro) || "zs02".equals(pro)) {
            return pro;
        }
        try {
            String prod = StringUtil.decrypt(pro);
            log.info("translate pro from {} to {}", pro, prod);
            return prod;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("捕获到异常: ", e);
        }
        return pro;
    }


    public static String getProByUrl(String url) {
        try {
            Entity entity = Db.use().queryOne("select * from url_beian where url = ?", url);
            String pro = entity.getStr("pro");
            log.info("get pro {} by url {}", pro, url);
            return pro;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("捕获到异常: ", e);
        }
        return null;
    }

    private static HttpEntity<String> createHttpEntity() {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        return new HttpEntity<>(null, headers);
    }

    public static void main(String[] args) {
        // ProUtil.nmchSaveUser("13880899000");
        // String pro = tryTranslatePro("au02");
        DbUtil.setActive("dev-zsgl");
        ProUtil.tryTranslatePro("kzca");
    }
}
