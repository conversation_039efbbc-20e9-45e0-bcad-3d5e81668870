package com.xy.lib.migu.util;

import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.xy.base.core.enums.ExceptionResultEnum;
import com.xy.base.core.exception.AppException;
import com.xy.base.core.util.DbUtil;
import com.xy.base.starter.redis.RedisUtils;
import com.xy.lib.migu.vo.Location;
import com.xy.lib.migu.vo.PhoneLocationType;
import lombok.extern.slf4j.Slf4j;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
public class PhoneUtil {

    // 过滤：已订阅用户、定向省份/运营商，用于 miguToken2，目前仅xwhl老接口跑nmch
    public static boolean validate(String cellphone, String pro) {
        // 过滤已订阅用户
        boolean result = validateSubscribered(cellphone);
        log.info("cellphone {} phone_check validate result: {}", cellphone, result);
        AppException.tnt(!result, ExceptionResultEnum.OTHER_FAILED);
        // 过滤定向省份/运营商
        PhoneLocationType locationAndType = PhoneInfoUtil.phoneInfo(cellphone);
        result = validateLocationCarrier(locationAndType, cellphone, pro);
        log.info("cellphone {} location and carrier validate result: {}", cellphone, result);
        if (!result) {
            return false;
        }
        // 过滤到量
        result = validateDaoliang(locationAndType, cellphone, pro);
        log.info("cellphone {} daoliang validate result: {}", cellphone, result);
        return result;
    }

    // 过滤：已订阅用户、定向省份/运营商、包名，用于 miguTokenAPI
    public static boolean validate(String cellphone, String pro, String url, String ua) {
        boolean result = validate(cellphone, pro);
        if (!result) {
            return false;
        }
        // 过滤包名
        result = validatePackageBase(cellphone, url, ua, pro);
        log.info("cellphone {} package validate result: {}", cellphone, result);
        return result;
    }

    // 过滤：已订阅用户、定向省份/运营商、包名、点位
    public static boolean validate(String cellphone, String pro, String url, String ua, String unionSite) {
        boolean result = validate(cellphone, pro, url, ua);
        if (!result) {
            return false;
        }
        if (null == unionSite) {
            return true;
        }
        // 过滤点位
        result = validateUnionSite(cellphone, unionSite, pro);
        AppException.tnt(!result, ExceptionResultEnum.OTHER_FAILED_POINT_LIMIT);
        log.info("cellphone {} union_site validate result: {}", cellphone, result);
        return result;
    }

    // 过滤：已订阅用户、定向省份/运营商、按条件过滤包名点位，用于 miguToken7
    public static boolean validate(String cellphone, String pro, String url, String ua, String unionSite, String ft) {
        if ("pk".equals(ft)) {
            // 仅过滤package
            return validate(cellphone, pro, url, ua);
        } else if ("no".equals(ft)) {
            // 不做过滤
            return validate(cellphone, pro);
        } else if (null == ft || "".equals(ft)) {
            // 仅过滤基础package
            boolean result = validate(cellphone, pro);
            if (!result) {
                return false;
            }
            result = validatePackageBase(cellphone, url, ua, pro);
            AppException.tnt(!result, ExceptionResultEnum.OTHER_FAILED_APP_LIMIT);
            log.info("cellphone {} package base validate result: {}", cellphone, result);
            return result;
        }
        // 按点位过滤
        boolean result = validate(cellphone, pro);
        if (!result) {
            return false;
        }
        if (null == unionSite) {
            return true;
        }
        result = validateUnionSite(cellphone, unionSite, pro);
        log.info("cellphone {} union_site validate result: {}", cellphone, result);
        return result;
    }

    // 根据UA检查 package 是否已被屏蔽
    private static boolean validatePackageBase(String cellphone, String url, String ua, String pro) {
        Db db = Db.use();
        try {
            Entity entity = db.queryOne("SELECT * FROM pro_filter_strategy WHERE pro = ? limit 1", pro);
            if (null != entity && entity.getInt("pkg_enable") == 0) {
                log.info("package filter strategy for cellphone {} with pro {} is disabled!", cellphone, pro);
                return true;
            }
            String pkg = StringUtil.extractPackageName(ua);
            if (null == pkg) {
                return true;
            }
            Entity result = db.queryOne("SELECT * FROM package_filter_base WHERE pkg = ? limit 1", pkg);

            if (result != null) {
                log.info("cellphone {} package hit package_filter, package is {}", cellphone, pkg);
                return false;
            }
        } catch (SQLException e) {
            log.error("验证包名过程中发生SQL异常", e);
        }
        return true;
    }

    // 根据UA检查 package 是否已被屏蔽
    private static boolean validatePackage(String cellphone, String url, String ua, String pro) {
        Db db = Db.use();
        try {
            Entity entity = db.queryOne("SELECT * FROM pro_filter_strategy WHERE pro = ? limit 1", pro);
            if (null != entity && entity.getInt("pkg_enable") == 0) {
                log.info("package filter strategy for cellphone {} with pro {} is disabled!", cellphone, pro);
                return true;
            }
            String pkg = StringUtil.extractPackageName(ua);
            if (null == pkg) {
                return true;
            }
            int htag = TimeUtil.holidayTag();
            Entity result = null;
            if (htag == -1) {
                result = db.queryOne("SELECT * FROM package_filter_workday WHERE pkg = ? limit 1", pkg);
            } else {
                result = db.queryOne("SELECT * FROM package_filter_holiday WHERE pkg = ? limit 1", pkg);
            }
            if (result != null) {
                log.info("cellphone {} package hit package_filter, package is {}", cellphone, pkg);
                return false;
            }
        } catch (SQLException e) {
            log.error("验证包名过程中发生SQL异常", e);
        }
        return true;
    }

    // 检查 union_site 是否已被屏蔽
    private static boolean validateUnionSite(String cellphone, String unionSite, String pro) {
        Db db = Db.use();
        try {
            Entity entity = db.queryOne("SELECT * FROM pro_filter_strategy WHERE pro = ? limit 1", pro);
            if (null != entity && entity.getInt("usite_enable") == 0) {
                log.info("usite filter strategy for cellphone {} with pro {} is disabled!", cellphone, pro);
                return true;
            }
            int htag = TimeUtil.holidayTag();
            Entity result = null;
            if (htag == -1) {
                result = db.queryOne("SELECT * FROM usite_filter_workday WHERE union_site = ? limit 1", unionSite);
            } else {
                result = db.queryOne("SELECT * FROM usite_filter_holiday WHERE union_site = ? limit 1", unionSite);
            }
            if (result != null) {
                log.info("cellphone {} union_site hit usite_filter, union_site is {}", cellphone, unionSite);
                return false;
            }
        } catch (SQLException e) {
            log.error("验证点位过程中发生SQL异常", e);
        }
        return true;
    }

    // 检查是否已订阅
    private static boolean validateSubscribered(String cellphone) {
        Db db = Db.use();
        try {
            Entity entity = db.queryOne("SELECT * FROM phone_check WHERE type != 0 and billNum = ? limit 1", cellphone);
            if (null == entity) {
                return true;
            } else {
                String description = entity.getStr("description");
                log.info("cellphone {}  phone_check result: false, description: {}", cellphone, description);
                AppException.tnt(ExceptionResultEnum.OTHER_FAILED_PHONE_LIMIT);
                return false;
            }
        } catch (SQLException e) {
            log.error("验证订阅状态过程中发生SQL异常", e);
        }
        return true;
    }

    // 获取地域和运营商信息
    public static PhoneLocationType phoneInfo(String cellphone) {
        try {
            long startTime = System.currentTimeMillis(); // 获取开始时间
            PhoneLocationType locationAndType = PhoneInfoUtil.phoneInfo(cellphone);
            long endTime = System.currentTimeMillis(); // 获取结束时间
            long elapsedTime = endTime - startTime; // 计算总耗时
            log.info("cellphone {} location: {} cost {}ms", cellphone, locationAndType, elapsedTime);
            return locationAndType;
        } catch (Exception e) {
            log.error("获取手机号地域信息过程中发生异常", e);
        }
        return null;
    }


    // 获取一个时间，并将其转为字符串，格式示例：2025-03-21 17:37:16，该时间满足条件：如果当前时间在9:00之前，则该时间为昨天9:00，否则该时间为今天9:00
    // 为了容错（例如时钟不同步导致出错，时钟涉及到自有服务器，咪咕服务器，阿里云RDS时钟等），该时间调整为：如果当前时间在8:30之前，则该时间为昨天9:00，
    // 如果当前时间在9:30之后，则该时间为今天9:00，否则，该时间为null
    public static String getDaoliangTime() {
        LocalDateTime now = LocalDateTime.now();
        LocalTime currentTime = now.toLocalTime();

        // 定义时间分界线
        LocalTime morning830 = LocalTime.of(8, 30);
        LocalTime morning930 = LocalTime.of(9, 30);

        // 根据条件构造目标时间
        LocalDateTime targetDateTime = null;
        if (currentTime.isBefore(morning830)) {
            // 昨天9点
            targetDateTime = LocalDateTime.of(now.minusDays(1).toLocalDate(), LocalTime.of(9, 0));
        } else if (currentTime.isAfter(morning930)) {
            // 今天9点
            targetDateTime = LocalDateTime.of(now.toLocalDate(), LocalTime.of(9, 0));
        }
        // 格式化为字符串或返回null
        String result = null;
        if (targetDateTime != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            result = targetDateTime.format(formatter);
        }
        return result;
    }

    // 根据表 dliang_info 中的 pro, notice, enable 字段判断该pro到量情况
    public static boolean validateDaoliang(PhoneLocationType locationAndType, String phone, String pro) {
        try {
            // 获取从表 dliang_info 中取数据的起始时间
            String startTime = getDaoliangTime();
            if (null == startTime) {
                return true;
            }

            // 获取LoginApiUtil中缓存的channel和source信息
            String channelSource = RedisUtils.get("channel_source:" + phone);
            String channel = "";
            String source = "";

            if (channelSource != null && !channelSource.isEmpty()) {
                String[] channelParts = channelSource.split(":");
                if (channelParts.length >= 2) {
                    channel = channelParts[0];
                    source = channelParts[1];
                }
            }

            // notice 取 all，检查公司整体到量情况
            Entity entity = Db.use().queryOne("SELECT * FROM dliang_info WHERE create_time >= ?  and notice = ? and pro= ? order by create_time desc limit 1",
                    startTime, "all", pro);
            if (null != entity && entity.getInt("enable") == 1) {
                // 公司整体到量
                log.info("cellphone {} with pro {} 公司整体到量", phone, pro);

                saveDliangRecordAsync(channel, source, phone, "all", pro);

                AppException.tnt(ExceptionResultEnum.PRODUCT_LIMIT);
                return false;
            }
            if (null == locationAndType) {
                return true;
            }
            // notice 取单个省份，检查单个省份到量情况
            String province = locationAndType.getLocation().getProvince();
            if (null == province) {
                return true;
            }

            // notice 取单个省份，检查单个省份到量情况
            entity = Db.use().queryOne(
                    "SELECT * FROM dliang_info WHERE create_time >= ?  and notice = ? and pro= ? order by create_time desc limit 1",
                    startTime, province, pro);
            if (null != entity && entity.getInt("enable") == 1) {
                // 单个省份到量
                log.info("cellphone {} with pro {} in province {} 单个省份到量", phone, pro, province);

                // 异步保存到量记录到数据库
                saveDliangRecordAsync(channel, source, phone, province, pro);

                // 计算通知次数，即 dliang_record 中的数量，超过3次不再拦截
                Entity ent = Db.use().queryOne("SELECT COUNT(*) c FROM dliang_record where create_time >= ?  and notice = ? and pro= ?  and channel = ? and source= ?",
                        startTime, province, pro, channel, source);
                Integer c = ent.getInt("c")-1;
                log.info("省份到量 {} 次，cellphone {}, pro {}, channel {}, source {} in province {}", c, phone, pro, channel, source, province);
                if(c >= 3){
                    log.info("省份到量已通知 3 次，不再拦截，cellphone {}, pro {}, channel {}, source {} in province {}", phone, pro, channel, source, province);
                }else {
                    AppException.tnt(ExceptionResultEnum.PROVINCE_LIMIT);
                    return false;
                }
            }
        } catch (AppException e) {
            // 业务异常直接抛出，不捕获
            throw e;
        } catch (Exception e) {
            // 只捕获非业务异常
            log.error("验证到量过程中发生系统异常", e);
        }
        return true;
    }

    /**
     * 异步将Redis中的到量记录保存到数据库中
     */
    private static void saveDliangRecordAsync(String channel, String source, String phone, String province, String pro) {
        try {
            Entity entity = Entity.create("dliang_record")
                    .set("phone", phone)
                    .set("pro", pro)
                    .set("notice", province)
                    .set("channel", channel)
                    .set("source", source);

            // 执行插入操作
            Db.use().insert(entity);

            // 插入成功后删除Redis中的记录
            RedisUtils.del("channel_source:" + phone);

            log.info("保存到量记录到数据库，{}", entity.toString());

        } catch (Exception e) {
            log.error("保存到量记录到数据库失败", e);
        }
    }

    // 检查地域和运营商
    private static boolean validateLocationCarrier(PhoneLocationType locationAndType, String cellphone, String pro) {
        try {
            if (null == locationAndType) {
                return true;
            }
            String type = locationAndType.getCarrier();
            if (type != null) {
                if (type.startsWith("电信") || type.startsWith("联通")) {
                    AppException.tnt(ExceptionResultEnum.OTHER_FAILED_NOT_MOBILE);
                    return false;
                }
            }
            Location location = locationAndType.getLocation();
            String province = location.getProvince();
            if (null == province) {
                return true;
            }
            List<Entity> entities = Db.use().query("SELECT * FROM pro_region WHERE province = ? and pro= ?", province, pro);
            if (null == entities || entities.isEmpty()) {
                return true;
            }
            String city = location.getCity();
            for (Entity entity : entities) {
                String strategy = entity.getStr("strategy");
                String ct = entity.getStr("city");
                if (null == ct || ct.trim().isEmpty()) {
                    if (isAbnormal(strategy)) {
                        AppException.tnt(ExceptionResultEnum.PROVINCE_OFF_SHELF);
                        return false;
                    }
                } else if (null != city && city.startsWith(ct)) {
                    if (isAbnormal(strategy)) {
                        AppException.tnt(ExceptionResultEnum.OTHER_FAILED_CITY_LIMIT);
                        return false;
                    }
                }
            }
        } catch (AppException e) {
            // 业务异常直接抛出，不捕获
            throw e;
        } catch (SQLException e) {
            log.error("验证地域和运营商过程中发生SQL异常", e);
        } catch (Exception e) {
            log.error("验证地域和运营商过程中发生系统异常", e);
        }
        return true;
    }

    private static boolean isAbnormal(String strategy) {
        return !strategy.startsWith("正常") && !strategy.startsWith("不限量");
    }

    public static void main(String[] args) {
        DbUtil.setActive("dev-pros");
//        String cellphone = "***********"; // 内蒙古
//        String cellphone = "***********"; //广西
//        String cellphone = "***********"; //新疆
//        String cellphone = "***********"; //黑龙江
//        String cellphone = "***********"; //四川
//        boolean result = PhoneUtil.validateSubscribered(cellphone);
//        boolean validate = PhoneUtil.validate(cellphone, "xamw");
        boolean validate = PhoneUtil.validateDaoliang(new PhoneLocationType("黑龙江","哈尔滨","中国移动"), "***********", "zsgl");
        System.out.println("validate:" + validate);
    }
}
