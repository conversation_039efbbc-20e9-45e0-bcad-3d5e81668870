package com.xy.lib.migu.util;

import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.aliyun.dytnsapi20200217.Client;
import com.aliyun.dytnsapi20200217.models.DescribePhoneNumberOperatorAttributeRequest;
import com.aliyun.dytnsapi20200217.models.DescribePhoneNumberOperatorAttributeResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.xy.base.core.util.DbUtil;
import com.xy.base.starter.redis.RedisUtils;
import com.xy.lib.migu.vo.Location;
import com.xy.lib.migu.vo.PhoneLocationType;
import lombok.extern.slf4j.Slf4j;

import java.sql.SQLException;

/**
 * 查询手机号运营商和归属地
 *
 * <AUTHOR>
 * @since 2024/01/30 11:23
 */
@Slf4j
public class PhoneInfoUtil {

    private static final String ACCESS_KEY = "LTAI5tSSdbUVJzJc7vjDo2Fc";
    private static final String ACCESS_KEY_SECRET = "******************************";
    private static int total_times = 0;
    private static int hit_redis_times = 0;
    private static int hit_db_times = 0;
    private static int api_times = 0;

    /**
     * 使用AK&SK初始化账号Client
     *
     * @return Client
     */
    private static Client createClient() throws Exception {
        Config config = new Config()
                .setAccessKeyId(ACCESS_KEY)
                .setAccessKeySecret(ACCESS_KEY_SECRET);
        // Endpoint 请参考 https://api.aliyun.com/product/Dysmsapi
        config.endpoint = "dytnsapi.aliyuncs.com";
        return new Client(config);
    }


    /**
     * 查询手机号运营商和归属地
     *
     * @param phone 手机号
     */
    public static PhoneLocationType phoneInfo(String phone) {
        try {
            return queryPhoneInfo(phone);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            log.info("phoneInfo times:{}, redis times:{}, db_times:{}, api_times:{}, api_percent:{}", total_times, hit_redis_times, hit_db_times, api_times, (float)api_times / total_times);
        }
        return null;
    }

    /**
     * 查询手机号运营商和归属地
     *
     * @param phone 手机号
     */
    private static PhoneLocationType queryPhoneInfo(String phone) {
        total_times += 1;
        String seg = phone.substring(0, 7);

        //1. 取redis
        String info = RedisUtils.get(seg);
        if (null != info) {
            final String[] split = info.split(",");
            hit_redis_times += 1;
            log.info("phone {} hit redis cache in PhoneInfoUtil", phone);
            return new PhoneLocationType(split[0], split[1], split[2]);
        }

        //2. 取database
        try {
            Entity entity = Db.use().queryOne("select * from phone_lac where seg = ?", seg);
            if (null != entity) {
                String province = entity.getStr("province");
                String city = entity.getStr("city");
                String carrier = entity.getStr("carrier");
                RedisUtils.set(seg, province + "," + city + "," + carrier);
                hit_db_times += 1;
                log.info("phone {} hit database in PhoneInfoUtil", phone);
                return new PhoneLocationType(province, city, carrier);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        //3.取API
        PhoneLocationType phoneLac = phoneInfoFromAPI(phone);
        if (null != phoneLac) {
            String province = phoneLac.getLocation().getProvince();
            String city = phoneLac.getLocation().getCity();
            String carrier = phoneLac.getCarrier();

            RedisUtils.set(seg, province + "," + city + "," + carrier);
            Entity entity = Entity.create("phone_lac")
                    .set("seg", seg)
                    .set("province", province)
                    .set("city", city)
                    .set("carrier", carrier)
                    .set("vcarrier", phoneLac.getVcarrier());
            try {
                Db.use().insert(entity);
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        api_times += 1;
        log.info("phone {} invoke api in PhoneInfoUtil", phone);
        return phoneLac;
    }

    /**
     * 从阿里号码百科查询手机号运营商和归属地
     *
     * @param phone 手机号
     */
    private static PhoneLocationType phoneInfoFromAPI(String phone) {
        try {
            Client client = createClient();
            DescribePhoneNumberOperatorAttributeRequest describePhoneNumberOperatorAttributeRequest = new DescribePhoneNumberOperatorAttributeRequest()
                    .setAuthCode("O91KdHeplT")
                    .setInputNumber(phone)
                    .setMask("NORMAL");
            RuntimeOptions runtime = new RuntimeOptions();

            DescribePhoneNumberOperatorAttributeResponse resp = client.describePhoneNumberOperatorAttributeWithOptions(describePhoneNumberOperatorAttributeRequest, runtime);
            System.out.println(com.aliyun.teautil.Common.toJSONString(resp));

            Location location = new Location(resp.body.data.getProvince(), resp.body.data.getCity());
            PhoneLocationType locationAndType = new PhoneLocationType(location, resp.body.data.getBasicCarrier().replace("中国", ""));
            locationAndType.setVcarrier(resp.body.data.getCarrier());
            return locationAndType;
        } catch (TeaException error) {
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return null;
    }

    public static void main(String[] args_) throws Exception {
//        String cellphone = "16273950000"; //三五互联
        String cellphone = "13086001000"; //海南
//        phoneInfoFromAPI(cellphone);
        queryPhoneInfo("13086001000");
    }
}
