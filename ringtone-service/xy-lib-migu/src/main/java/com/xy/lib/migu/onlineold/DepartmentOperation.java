package com.xy.lib.migu.onlineold;

import com.xy.lib.migu.config.ConfigData;
import com.xy.lib.migu.vo.DepartmentObject;
import com.xy.lib.migu.vo.EnterpriseDeptOperationVO;
import com.xy.lib.migu.vo.EnterpriseDeptQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;

import java.util.List;

import static com.xy.lib.migu.config.ConfigData.adminMsisdn;
import static com.xy.lib.migu.config.ConfigData.orderId;
import static com.xy.lib.migu.util.HttpsUtil.getUrl;
import static com.xy.lib.migu.util.HttpsUtil.post;

/**
 * 接口标题行
 *
 * @Author: David
 * @Date: 2023/11/16 17:36
 */
@Slf4j
public class DepartmentOperation {

    public static void operateDept(EnterpriseDeptOperationVO deptOperationVO) {
        // 接口 URL
        String url = getUrl("/evrms/v3/orderContent/foreign/ecOperation");

        // 补充请求参数
        deptOperationVO.setOrderId(orderId);
        deptOperationVO.setAdminMsisdn(adminMsisdn);

        ResponseEntity<String> response = post(url, deptOperationVO);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    public static String queryDeptId(String cellphone) {
        String deptString = queryDept(ConfigData.orderId, "DPT" + cellphone);
        try {
            List<DepartmentObject> departmentObjects = DepartmentObject.parseDepartments(deptString);
            return departmentObjects.get(0).getDepartmentId();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String queryDept(String orderId) {
        // 构造请求参数
        EnterpriseDeptQueryVO deptQueryVO = new EnterpriseDeptQueryVO();
        deptQueryVO.setOrderId(orderId);

        return queryDept(deptQueryVO);
    }

    public static String queryDept(String orderId, String departmentName) {
        // 构造请求参数
        EnterpriseDeptQueryVO deptQueryVO = new EnterpriseDeptQueryVO();
        deptQueryVO.setOrderId(orderId);
        deptQueryVO.setDepartmentName(departmentName);

        return queryDept(deptQueryVO);
    }

    public static String queryDept(EnterpriseDeptQueryVO deptQueryVO) {
        // 接口 URL
        String url = getUrl("/evrms/v3/orderContent/foreign/searchEcDepartments");

        ResponseEntity<String> response = post(url, deptQueryVO);
        String responseBody = null;

        if (response.getStatusCode().is2xxSuccessful()) {
            responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
        return responseBody;
    }

    public static void main(String[] args) {
        // create
//        operateDept("0", null, null);
//        // update
//        operateDept("2", "3099954556372", "ZSGL测试部门3");
//        // delete
//        operateDept("1", "3099954556372", null);
        queryDept(orderId);
    }
}
