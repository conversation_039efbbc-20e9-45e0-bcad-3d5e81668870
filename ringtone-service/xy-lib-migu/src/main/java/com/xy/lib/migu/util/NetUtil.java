package com.xy.lib.migu.util;

import cn.hutool.core.util.IdUtil;
import com.xy.base.core.util.DbUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.DefaultProxyRoutePlanner;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;

import javax.net.ssl.*;
import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.cert.Certificate;
import java.security.cert.CertificateParsingException;
import java.security.cert.X509Certificate;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Random;

/**
 * 接口标题行
 *
 * @Author: David
 * @Date: 2023/11/18 15:48
 */
@Slf4j
public class NetUtil {
    private static final String protocol = "http";
    private static final String platformIP = "***************";
    private static final String platformPort = "80";
    private static final String uniqueAccId = "037600N";
    private static final String accPassword = "UtNJNVrSHg7%";

    public static String getUrl(String relativePath) {
        return String.format("%s://%s:%s%s", protocol, platformIP, platformPort, relativePath);
    }

    public static HttpHeaders getHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json; charset=UTF-8");

        String accSeq = generateAccSeq();
        String digest = generateDigest(uniqueAccId, accSeq, accPassword);

        // 添加请求头参数
        headers.set("uniqueAccId", uniqueAccId);
        headers.set("accSeq", accSeq);
        headers.set("digest", digest);

        return headers;
    }

    public static HttpHeaders getHeaders(String uniqueAccId, String accPassword) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json; charset=UTF-8");

        String accSeq = generateAccSeq();
        String digest = generateDigest(uniqueAccId, accSeq, accPassword);

        // 添加请求头参数
        headers.set("uniqueAccId", uniqueAccId);
        headers.set("accSeq", accSeq);
        headers.set("digest", digest);

        return headers;
    }

    private static String generateAccSeq() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = dateFormat.format(new Date());
        String randomNum = String.format("%06d", new Random().nextInt(999999));
        return timestamp + randomNum;
    }

    private static String generateDigest(String uniqueAccId, String accSeq, String accPassword) {
        String combinedString = uniqueAccId + accSeq + accPassword;
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(combinedString.getBytes());
            byte[] digestBytes = md.digest();
            return bytesToHex(digestBytes).toUpperCase();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        }
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    public static String getIP(String url) {
        try {
            URL u = new URL(url);
            String host = u.getHost();
            InetAddress address = InetAddress.getByName(host);
            return address.getHostAddress();
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    public static void main(String[] args) {
        String packageName = NetUtil.class.getPackage().getName() + "." + NetUtil.class.getName();
        System.out.println("Package name: " + packageName);
    }

    public static ResponseEntity<String> getResponse(String url, Object body) {
        HttpHeaders headers = getHeaders();
        return getResponseWithHeaders(url, body, headers);
    }

    public static ResponseEntity<String> getResponse(String url, Object body, String uniqueAccId, String accPassword) {
        HttpHeaders headers = getHeaders(uniqueAccId, accPassword);
        return getResponseWithHeaders(url, body, headers);
    }

    public static ResponseEntity<String> getResponseWithHeaders(String url, Object body, HttpHeaders headers) {
        RestTemplate restTemplate = new RestTemplate();

        HttpEntity<Object> entity = new HttpEntity<>(body, headers);
        HttpMethod method = HttpMethod.POST;

        long startTime = System.currentTimeMillis();
        ResponseEntity<String> response = null;
        Exception exception = null;

        try {
            // 发送POST请求
            response = restTemplate.exchange(
                    url, method, entity, String.class);
        } catch (Exception e) {
            e.printStackTrace();
            exception = e;
        } finally {
            long endTime = System.currentTimeMillis();

            Entity logEntity = Entity.create("log_api")
                    .set("type", 0)
                    .set("id", IdUtil.getSnowflake().nextId())
                    .set("url", url)
                    .set("ip", getIP(url))
                    .set("method", method.toString())
                    .set("position", NetUtil.class.getPackage().getName() + "." + NetUtil.class.getName())
                    .set("timing", endTime - startTime)
                    .set("create_time", LocalDateTime.now());

            if (entity.getBody() != null) {
                logEntity.set("request", entity.getBody().toString());
            }

            if (response != null) {
                logEntity.set("response", response.getBody());
            }

            if (exception != null) {
                StringWriter sw = new StringWriter();
                exception.printStackTrace(new PrintWriter(sw, true));
                String st = sw.toString();
                logEntity.set("exception", true)
                        .set("exception_name", exception.getClass().getName())
                        .set("exception_trace", st.length() > 1000 ? st.substring(0, 1000) : st);
            }
            try {
                log.info(logEntity.toString());
                Db.use().insert(logEntity);
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return response;
    }

    public static String sendHttpRequest(Object url, HttpEntity<String> httpEntity, HttpMethod method) {
        RestTemplate restTemplate = new RestTemplate();

        long startTime = System.currentTimeMillis();
        String response = null;
        Exception exception = null;
        String urlString = null;

        try {
            ResponseEntity<String> responseEntity = null;
            if (url instanceof String) {
                urlString = (String) url;
                responseEntity = restTemplate.exchange(urlString, method, httpEntity, String.class);
            } else if (url instanceof URI) {
                URI uri = (URI) url;
                urlString = URLDecoder.decode(uri.toString(), "UTF-8");
                responseEntity = restTemplate.exchange(uri, method, httpEntity, String.class);
            } else {
                log.error("url is wrong!");
                return null;
            }
            response = responseEntity.getBody();
            return response;
        } catch (Exception e) {
            log.error("捕获到异常: ", e);
            e.printStackTrace();
            exception = e;
        } finally {
            long endTime = System.currentTimeMillis();

            Entity logEntity = Entity.create("log_api")
                    .set("type", 0)
                    .set("id", IdUtil.getSnowflake().nextId())
                    .set("url", urlString)
                    .set("ip", getIP(url.toString()))
                    .set("method", method.name())
                    .set("request", httpEntity.getBody())
                    .set("response", response)
                    .set("position", NetUtil.class.getPackage().getName() + "." + NetUtil.class.getName())
                    .set("timing", endTime - startTime)
                    .set("create_time", LocalDateTime.now());

            if (exception != null) {
                StringWriter sw = new StringWriter();
                exception.printStackTrace(new PrintWriter(sw, true));
                String st = sw.toString();
                logEntity.set("exception", true)
                        .set("exception_name", exception.getClass().getName())
                        .set("exception_trace", StringUtils.hasLength(st) ? st.substring(0, Math.min(st.length(), 1000)) : null);
            }

            try {
                log.info(logEntity.toString());
                Db.use().insert(logEntity);
            } catch (SQLException e) {
                log.error("捕获到异常: ", e);
                e.printStackTrace();
            }
        }
        return response;
    }
}
