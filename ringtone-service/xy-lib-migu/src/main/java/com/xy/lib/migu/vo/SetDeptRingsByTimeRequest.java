package com.xy.lib.migu.vo;

import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * 接口标题行
 *
 * @Author: David
 * @Date: 2023/11/20 19:43
 */
/**
 * 集团铃音设置请求对象
 */
@Data
@ToString
public class SetDeptRingsByTimeRequest {
    /**
     * 订单编码
     */
    private String orderId;

    /**
     * 部门ID
     */
    private String departmentId;

    /**
     * 播放开始时间
     */
    private String startTime;

    /**
     * 播放结束时间
     */
    private String endTime;

    /**
     * 铃音类型：
     * 1 - 视频
     * 2 - 音频
     */
    private String ringType;

    /**
     * 设置的铃音ID集合
     */
    private List<String> ringIds;

    public SetDeptRingsByTimeRequest(String orderId, String departmentId, String startTime, String endTime, String ringType, List<String> ringIds) {
        this.orderId = orderId;
        this.departmentId = departmentId;
        this.startTime = startTime;
        this.endTime = endTime;
        this.ringType = ringType;
        this.ringIds = ringIds;
    }
}
