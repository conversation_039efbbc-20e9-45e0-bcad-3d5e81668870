package com.xy.lib.migu.util;

import cn.hutool.core.util.IdUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.xy.base.core.util.DbUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
public class AdTrackingUtil {
    private static final String delimiter = "#";

    private static String getAllParams(Map<String, String> params, List<String> ignoreList, boolean printParams) {
        List<String> list = new ArrayList<>();
        if (printParams) {
            log.info("params:");
        }
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (printParams) {
                log.info("{}: {}", entry.getKey(), entry.getValue());
            }
            if (null != ignoreList && ignoreList.contains(entry.getKey())) {
                continue;
            }
            if ("url".equals(entry.getKey())) {
                continue;
            }
            list.add(String.format("%s=%s", entry.getKey(), entry.getValue()));
        }
        return String.join("&", list);
    }

    private static String getParam(Map<String, String> params, String key) {
        if (params.containsKey(key)) {
            return params.get(key);
        }
        return null;
    }

    public static void report(String cellphone, String channel, String source, Map<String, String> params) {
        log.info("invoke AdTrackingUtil.report from channel {} with source {} and  phone: {}", channel, source, cellphone);

        String allParams = getAllParams(params, null, true);

        String paramValue = null;
        String platform = null;
        String ring = null;
        Integer nodelay = 0;
        int report_status = 0;
        String url = null;
        String ua = null;
        String pro = null;
        String projectid = null;
        String promotionid = null;
        try {
            Entity config = Db.use().queryOne("select * from config_huichuan where channel = ? and source = ?", channel, source);

            if (config != null) {
                String paramName = config.getStr("param_name");
                platform = config.getStr("platform");
                paramValue = getCallbackValue(channel, source, platform, paramName, params);
                nodelay = config.getInt("nodelay");

                if (paramValue != null) {
                    log.info("成功取到回调参数值: {}", paramValue);
                } else {
                    log.info("channel {} with source {} 未取到回调参数值", channel, source);
                }
            } else {
                log.info("channel {} with source {} 未查询到回传配置记录", channel, source);
            }
            if (params.containsKey("clickRingId")) {
                ring = params.get("clickRingId");
            } else if (params.containsKey("ring")) {
                ring = params.get("ring");
                if (null == ring || "".equals(ring.trim()) || "false".equals(ring.trim())) {
                    ring = null;
                }
            }
            log.info("got ring={}", ring);
            url = getParam(params, "url");
            pro = getParam(params, "pro");
            pro = ProUtil.tryTranslatePro(pro);
            projectid = getParam(params, "projectid");
            promotionid = getParam(params, "promotionid");

            if (nodelay == 1) {
                log.info("do nodelay report immediately, using params: {}, {}, {}, {}, {}, {}", paramValue, channel, source, platform, cellphone, allParams);
                report_status = 1;
                doSuccessReport(paramValue, channel, source, pro, platform, cellphone, allParams, url);
            }
            // 抖音平台不知何时在url后面追加了ad_params参数，该参数长度接近4000，会导致在channel_report保存失败，从而影响到延迟回传的订单
            // 因已修改巨量平台回传方式，allParams不再必要，且该值过大会增加网络和数据库压力
            /*
            if ("douyin".equals(platform)) {
                List<String> ignoreList = new LinkedList<>();
                ignoreList.add("ad_params");
                allParams = getAllParams(params, ignoreList, false);
            }
            */
            // 临时将ua参数作为allParams写入params
            ua = getParam(params, "ua");

            saveChannelReport(paramValue, cellphone, source, channel, platform, report_status, ring, ua, url, pro, projectid, promotionid);
        } catch (SQLException e) {
            e.printStackTrace();
            log.error("捕获到异常: ", e);
        }
    }

    private static String getCallbackValue(String channel, String source, String platform, String paramName, Map<String, String> params) {
        /*
        if ("shfy".equals(channel) && "dy01".equals(source)) {
            final String[] split = paramName.split(",");
            List<String> list = new ArrayList<>();
            for (String s : split) {
                list.add(params.get(s));
            }
            return String.join(delimiter, list);
        }
         */
        //clickid：对于推广Web落地页而言，在腾讯广告推广时，每次点击都会生成一个 click_id，在跳转到落地页时，会将 click_id 作为参数传入对应的 URL 中。对于腾讯广告非微信流量为URL中的参数qz_gdt的值，对于微信流量为URL中的参数gdt_vid的值。
        if ("gdt".equals(platform)) {
            if (params.containsKey("qz_gdt")) {
                return params.get("qz_gdt");
            } else if (params.containsKey("gdt_vid")) {
                return params.get("gdt_vid");
            }
        }
        return params.get(paramName);
    }

    private static void saveChannelReport(String callback, String cellphone, String source, String channel, String platform, int report_status, String ring, String params, String url, String pro, String projectid, String promotionid) {
        // 不再保存params
        // 允许的params最大长度为3300
        // if (params.length() > 3300) {
        //     params = params.substring(0, 3300);
        // }
        Entity entity = Entity.create("channel_report")
                .set("id", IdUtil.getSnowflake().nextId())
                .set("channel", channel)
                .set("report_status", report_status)
                .set("callback", callback)
                .set("phone", cellphone)
                .set("platform", platform)
                .set("source", source)
                .set("projectid", projectid)
                .set("promotionid", promotionid)
                .set("ring", ring)
                .set("url", url)
                .set("pro", pro)
                .set("params", params);
        try {
            log.info(entity.toString());
            Db.use().insert(entity);
        } catch (SQLException e) {
            e.printStackTrace();
            log.error("捕获到异常: ", e);
        }
    }

    // 即使同一平台下，因包装主体不同而有不同回传要求，因此，有时仅判断platform就不够了，需要同时判断channel和source
    public static boolean doSuccessReport(String callback, String channel, String source, String pro, String platform, String phone, String params, String url) {
        try {
            if ("ks".equals(platform)) {
                doKuaishouReport(callback);
                return true;
            } else if ("llg".equals(platform)) {
                // doLiuliangguoReport(callback); // 此处不再做回传动作，交由Python脚本：pros_huichuan
                return false;
            } else if ("llgapi".equals(platform)) {
                 // doLiuliangguoAPIReport(callback); // 此处不再做回传动作，交由Python脚本：pros_huichuan
                return false;
            } else if ("xwapi".equals(platform)) {
                // doXwAPIReport(callback);
                // doXwAPIReportV2(callback, pro, true, null); // 此处不再做回传动作，交由Python脚本：pros_huichuan
                return false;
            } else if ("gzbjapi".equals(platform)) {
                String apiUrl = "https://www.kaboss.cn/operator/commonPack/callback/zhongShiGuangLianPackSubmitOrder";
                doAPIReportV2(callback, pro, true, apiUrl, platform);
                return true;
            }
            // 需同时给厦门众联和抖音巨量引擎上报
            /*
            if ("shfy".equals(channel) && "dy01".equals(source)) {
                final int i = callback.indexOf(delimiter);
                String uuid = callback.substring(0, i);
                String clickid = callback.substring(i + 1);
                String fromUrl = "https://mtq9814.zhonglianhuashu.com/app/ring02.html?";
                doZhonglianDouyinReport(uuid, phone);
                doDouyinReport(clickid, phone, fromUrl, params);
                return;
            }
             */
            if ("douyin".equals(platform)) {
                // 根据文档：https://bytedance.larkoffice.com/docs/doccnie3Url88jhwSfrkViah1nq，该回传为老版本回传，
                // doDouyinReport2 为新版本回传，因此，仅保留新版本，去掉老版本回传
                // doDouyinReport2(callback); // 此处不再做回传动作，交由Python脚本：pros_huichuan
                return false;
            }
            if ("gdt".equals(platform)) {
                doGuangdiantongReport(callback, url);
                return true;
            }
            if ("gzqh".equals(platform)) {
                doGzqhAPIReport(callback, "subscribe");
                return true;
            }
            if ("zsgl".equals(platform)) {
                return false;
            }
            log.error("非法的回传平台：{} with callback {}", platform, callback);
        } catch (Exception e) {
            log.error("捕获到异常: ", e);
            e.printStackTrace();
        }
        return false;
    }

    public static void doFailReport(String callback, String channel, String source, String pro, String platform, String phone, String params, String url, String failDesc) {
        try {
            if ("xwapi".equals(platform)) {
                // doXwAPIReport(callback);
                doXwAPIReportV2(callback, pro, false, failDesc);
                return;
            } else if ("gzbjapi".equals(platform)) {
                String apiUrl = "https://www.kaboss.cn/operator/commonPack/callback/zhongShiGuangLianPackSubmitOrder";
                doAPIReportV2(callback, pro, false, apiUrl, platform);
                return;
            }
            log.error("非法的失败回传平台：{} with callback {}", platform, callback);
        } catch (Exception e) {
            log.error("捕获到异常: ", e);
            e.printStackTrace();
        }
    }

    // 处理退订回传
    public static boolean doWithdrawReport(String callback, String platform, String pro) {
        try {
            if ("gzqh".equals(platform)) {
                doGzqhAPIReport(callback, "withdraw");
                return true;
            }
            // log.error("非法的回传平台：{} with callback {}", platform, callback);
        } catch (Exception e) {
            log.error("捕获到异常: ", e);
            e.printStackTrace();
        }
        return false;
    }

    private static void doZhonglianDouyinReport(String uuid, String phone) {
        log.info("invoke doZhonglianDouyinReport using uuid={}, phone={}", uuid, phone);
        try {
            Entity order = Db.use().queryOne("select * from subscription_order where phone = ? order by create_time desc", phone);
            String orderId = order.getStr("order_number");
            String createTime = order.getStr("create_time");

            String apiUrl = "https://api.wi-fi.cn/open_api/oceanengine/micro-app/push-order";

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体
            String status = "已提交";
            String detailUrl = encodeValue("https://mtq9814.zhonglianhuashu.com/app/detail.html?orderId=" + orderId);
            String img = encodeValue("https://mtq9814.zhonglianhuashu.com/app/static/order_slt.jpg");
            String title = "视频彩铃";

            String jsonBody = "{\"uuid\":\"" + uuid + "\",\"order_id\":\"" + orderId + "\",\"create_time\":\"" + createTime + "\",\"status\":\"" + status + "\",\"detail_url\":\"" + detailUrl + "\",\"img\":\"" + img + "\",\"title\":\"" + title + "\"}";

            // 构建 HttpEntity
            HttpEntity<String> requestEntity = new HttpEntity<>(jsonBody, headers);

            NetUtil.sendHttpRequest(apiUrl, requestEntity, HttpMethod.POST);
        } catch (SQLException e) {
            e.printStackTrace();
        }

    }

    // 此回传根据文档 https://open.oceanengine.com/labels/7/docs/1696710647473167
    private static String doDouyinReport(String clickid, String phone, String fromUrl, String params) {
        try {
            log.info("invoke doDouyinReport using clickid={} and phone={} and fromUrl={} and params={}", clickid, phone, fromUrl, params);
            int eventType = 3; // 表单事件
            String trackingUrl = AdTrackingUtil.douyinTrackingUrl(eventType, clickid, fromUrl, params);
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json");
            HttpEntity<String> httpEntity = new HttpEntity<>(null, headers);
            return NetUtil.sendHttpRequest(trackingUrl, httpEntity, HttpMethod.GET);
        } catch (Exception e) {
            log.error("捕获到异常: ", e);
            e.printStackTrace();
        }
        return null;
    }

    // 此回传根据文档 https://event-manager.oceanengine.com/docs/8650/h5_api_docs
    public static String doDouyinReport2(String clickId) {
        try {
            log.info("invoke doDouyinReport2 using clickid={}", clickId); // 处log输出更改暂未更新

            String url = "https://analytics.oceanengine.com/api/v2/conversion";
            // 生成当前时间戳
            long currentTimeStamp = System.currentTimeMillis();
            String jsonBody = "{\"event_type\":\"form\",\"context\":{\"ad\":{\"callback\":\"" + clickId + "\"}},\"timestamp\":" + currentTimeStamp + "}";

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<String> httpEntity = new HttpEntity<>(jsonBody, headers);
            return NetUtil.sendHttpRequest(url, httpEntity, HttpMethod.POST);
        } catch (Exception e) {
            log.error("捕获到异常: ", e);
            e.printStackTrace();
        }
        return null;
    }

    private static String doLiuliangguoReport(String callback) {
        String apiUrl = "https://api.liuliangguo.com/openapi/v1/trace/convert";

        // 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 构建请求体参数
        String requestBody = "{\"click_id\": \"" + callback + "\", \"convert_type\": \"FORM\"}";
        HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);

        return NetUtil.sendHttpRequest(apiUrl, requestEntity, HttpMethod.POST);
    }

    private static String doLiuliangguoAPIReport(String callback) {
        try {
            String apiUrl = "https://notify.wi-fi.cn/callback/unify/AgentZSGL/sdkCallback";

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体参数
            String requestBody = "{\"clickid\": \"" + callback + "\", \"convert_type\": \"FORM\"}";
            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);

            return NetUtil.sendHttpRequest(apiUrl, requestEntity, HttpMethod.POST);
        } catch (Exception e) {
            log.error("捕获到异常: ", e);
            e.printStackTrace();
        }
        return null;
    }

    private static String doXwAPIReport(String callback) {
        try {
            log.info("in  doXwAPIReport, callback is: {}", callback);

            String apiUrl = "https://shop.xwtec.cn/tmall/busiApi/callback/377";

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体参数
            String requestBody = "{\"clickid\": \"" + callback + "\", \"convert_type\": \"FORM\"}";
            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);

            return NetUtil.sendHttpRequest(apiUrl, requestEntity, HttpMethod.POST);
        } catch (Exception e) {
            log.error("捕获到异常: ", e);
            e.printStackTrace();
        }
        return null;
    }

    private static String doXwAPIReportV2(String callback, String pro, boolean success, String failDesc) {
        try {
            log.info("in  doXwAPIReport, callback is: {}", callback);

            String apiUrl = "https://shop.xwtec.cn/tmall/busiApi/callback/377";

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            String dt = TimeUtil.currentDate();
//            dt = "2024-07-16";
//            DbUtil.setDbByPro(pro);
            Entity entity = Db.use().queryOne("select pro,platform,date,num,lmt from pro_num where pro = ?  and platform='xwapi' and date = ?", pro, dt);

            Integer num = 1;

            // 因公司整体到量、或某个省份到量失败时的回传
            if (!success) {
                log.info("{} pro: {}", failDesc, pro);
                String requestBody = StringUtil.getHuichuanJsonStrring(450, callback, "FORM", failDesc);
                log.info("request body: {}", requestBody);
                HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);
                return NetUtil.sendHttpRequest(apiUrl, requestEntity, HttpMethod.POST);
            } else if (entity != null) {
                num = entity.getInt("num");
                Integer lmt = entity.getInt("lmt");
                num += 1;

                entity.set("num", num);
                Db.use().update(entity, entity.clone().set("num", num - 1));

                if (num >= lmt) {
                    log.info("当天已到量 pro: {}, platform:xwapi, date:{}, num:{}, lmt:{}", pro, dt, num, lmt);
                    String requestBody = StringUtil.getHuichuanJsonStrring(450, callback, "FORM", "当日用户发展数量已达当日上限");
                    log.info("request body: {}", requestBody);
                    HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);
                    return NetUtil.sendHttpRequest(apiUrl, requestEntity, HttpMethod.POST);
                }
            } else {
                entity = Entity.create("pro_num")
                        .set("pro", pro)
                        .set("platform", "xwapi")
                        .set("date", dt)
                        .set("num", num);
                Db.use().insert(entity);
            }

            String requestBody = StringUtil.getHuichuanJsonStrring(200, callback, "FORM", "成功");
            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);
            return NetUtil.sendHttpRequest(apiUrl, requestEntity, HttpMethod.POST);
        } catch (Exception e) {
            log.error("捕获到异常: ", e);
            e.printStackTrace();
        }
        return null;
    }

    private static String doAPIReportV2(String callback, String pro, boolean success, String apiUrl, String platform) {
        try {
            log.info("in  doAPIReportV2, callback is: {}", callback);
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            String dt = TimeUtil.currentDate();
//            dt = "2024-07-16";
//            DbUtil.setDbByPro(pro);
            Entity entity = Db.use().queryOne("select pro,platform,date,num,lmt from pro_num where pro = ?  and platform=? and date = ?", pro, platform, dt);

            Integer num = 1;

            // 因公司整体到量而失败时的回传
            if (!success) {
                log.info("当天公司整体已到量 pro: {}", pro);
                String requestBody = StringUtil.getHuichuanJsonStrring(450, callback, "FORM", "当日已到量");
                log.info("request body: {}", requestBody);
                HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);
                return NetUtil.sendHttpRequest(apiUrl, requestEntity, HttpMethod.POST);
            } else if (entity != null) {
                num = entity.getInt("num");
                Integer lmt = entity.getInt("lmt");
                num += 1;

                entity.set("num", num);
                Db.use().update(entity, entity.clone().set("num", num - 1));

                if (num >= lmt) {
                    log.info("当天已到量 pro: {}, platform:{}, date:{}, num:{}, lmt:{}", pro, platform, dt, num, lmt);
                    String requestBody = StringUtil.getHuichuanJsonStrring(450, callback, "FORM", "当日已到量");
                    log.info("request body: {}", requestBody);
                    HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);
                    return NetUtil.sendHttpRequest(apiUrl, requestEntity, HttpMethod.POST);
                }
            } else {
                entity = Entity.create("pro_num")
                        .set("pro", pro)
                        .set("platform", platform)
                        .set("date", dt)
                        .set("num", num);
                Db.use().insert(entity);
            }

            String requestBody = StringUtil.getHuichuanJsonStrring(200, callback, "FORM", "成功");
            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);
            return NetUtil.sendHttpRequest(apiUrl, requestEntity, HttpMethod.POST);
        } catch (Exception e) {
            log.error("捕获到异常: ", e);
            e.printStackTrace();
        }
        return null;
    }

    private static String doGzqhAPIReport(String clickid, String eventType) {
        try {
            String apiUrl = "http://ywff.ysy138.com/api/callback/zhongshiguanglian/?t=0507";

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体参数
            String requestBody = "{\"clickid\":\"" + clickid + "\",\"event_type\":\"" + eventType + "\"}";

            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);

            String response = NetUtil.sendHttpRequest(apiUrl, requestEntity, HttpMethod.POST);
            log.info("gzqh api 新增回传调用返回结果：{}", response);

            return response;
        } catch (Exception e) {
            log.error("捕获到异常: ", e);
            e.printStackTrace();
        }
        return null;
    }

    private static String doKuaishouReport(String callback) {
        // 表单事件
        doKuaishouReport(9, callback, null);
        // 提交订单事件
        return doKuaishouReport(14, callback, "10");
    }

    private static String doKuaishouReport(int eventType, String callback, String purchase_amount) {
        String trackingUrl = AdTrackingUtil.kuaishouTrackingUrl(eventType, callback, purchase_amount);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        HttpEntity<String> httpEntity = new HttpEntity<>(null, headers);
        return NetUtil.sendHttpRequest(trackingUrl, httpEntity, HttpMethod.GET);
    }

    // 参考文档：https://developers.e.qq.com/docs/guide/conversion/new_version/Web_api
    public static String doGuangdiantongReport(String clickId, String link) {
        try {
            log.info("invoke doGuangdiantongReport using clickid={}", clickId); // 处log输出更改暂未更新
            String actionType = "RESERVATION"; // 表单预约
            long actionTime = System.currentTimeMillis() / 1000; // Converting current time to seconds
            String encodedLink = URLEncoder.encode(link, StandardCharsets.UTF_8.toString());
            String url = String.format("http://tracking.e.qq.com/conv/web?clickid=%s&action_time=%d&action_type=%s&link=%s",
                    clickId, actionTime, actionType, encodedLink);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<String> httpEntity = new HttpEntity<>(headers);
            return NetUtil.sendHttpRequest(url, httpEntity, HttpMethod.GET);
        } catch (Exception e) {
            log.error("捕获到异常: ", e);
            e.printStackTrace();
        }
        return null;
    }

    private static String douyinTrackingUrl(int eventType, String clickid, String fromUrl, String params) {
        String url = fromUrl + params;
        log.info("in douyinTrackingUrl origin url: {}", url);
        // 构建上报请求链接
        StringBuilder sb = new StringBuilder("https://ad.oceanengine.com/track/activate/?");
        sb.append("event_type=").append(eventType);
        sb.append("&callback=").append(clickid);
        sb.append("&link=").append(encodeValue(url));

        return sb.toString();
    }

    private static String kuaishouTrackingUrl(int eventType, String callback, String purchase_amount) {
        long eventTime = System.currentTimeMillis(); // 获取当前时间戳

        // 构建上报请求链接
        StringBuilder sb = new StringBuilder("http://ad.partner.gifshow.com/track/activate?");
        sb.append("event_type=").append(eventType);
        sb.append("&event_time=").append(eventTime);
        sb.append("&callback=").append(callback);
        if (purchase_amount != null) {
            sb.append("&purchase_amount=").append(purchase_amount);
        }

        return sb.toString();
    }

    private static String encodeValue(String value) {
        try {
            return URLEncoder.encode(value, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) {
        String callback = "ENmRxJnUhJgDGKbmw8yIhfgGIKbmw8yIhfgGMA446-LTpwNCKWViYWQ3YzA5LTE2ZDMtNDE0Ni04YmE1LTI4NzhmZjQ4MjRlNHU1MzE2SIDSk60DkAEA";
//        String response = doKuaishouReport(callback);
//        String response = doLiuliangguoReport(callback);
//        doReport(callback, "llg");
//        Map<String, String> params = new HashMap<>();
//        params.put("callback", callback);
//        params.put("unknown", "weizhi");
//        report("13800001234", "shfy", "ks04", params);
//        String response = doDouyinReport2(callback, null, null, null);
//        String clickid = "yq4GtbZz0TdMaZOXPFNaeMdugt4zu55r_o6oiLFtXK9T7wGwWhpFcR209CfvW8SOKOSQULU3UC5eMMlKLnwwGvnzaMnCTxuBFNklQ7nVREqK9MHPNI_M-T5ttJTK8gy7xu8tkLzrSr7kOjTZkGQLEnKRIttiwpSBffadjmDICNRqLlN1IwpVbywXAyozi7sTVAk8O9HQRy2Ra9UKiIa_UQ";
//        String response = doGuangdiantongReport(clickid, "card.kuaifuinfo.com");
//        System.out.println(response);
//        String clickid = "17a29ec6fa8f76c8d3b2e122ac0b90d1";
//        String response = doGzqhAPIReport(clickid, "subscribe");
//        System.out.println(response);
        doXwAPIReportV2(callback, "nmch", true, null);
        doXwAPIReportV2(callback, "nmch", false, "当日用户发展数量已达当日上限");
    }

}
