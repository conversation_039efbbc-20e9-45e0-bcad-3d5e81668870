package com.xy.lib.migu.util;

import com.xy.lib.migu.vo.Location;
import com.xy.lib.migu.vo.PhoneLocationType;

import java.util.Random;

public class CompanyUtil {

    // 公司命名：城市+名称+业态+公司+手机尾号4位
    public static String getCompanyName(String phone) {
        try {
            String city = "";
            PhoneLocationType locationAndType = PhoneInfoUtil.phoneInfo(phone);
            if (null != locationAndType) {
                Location location = locationAndType.getLocation();
                if (null != location && null != location.getCity()) {
                    city = location.getCity();
                }
            }
            return city +
                    name() +
                    yetai() +
                    "公司" +
                    phone.substring(7);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return phone;
    }

    private static String yetai() {
        String words = "文化、传媒、物流、科技、金融、能源、零售、教育、餐饮、旅游、服装、商务、互联网、建筑、农业、化工、游戏、体育、音乐、投资、矿业、物业管理、食品、广告、人力资源、公关、环保、安全、家具、软件、家电、咨询、网络安全、智能、家居、环保科技、物联网";

        String[] wordsArray = words.split("、");

        // 使用 Random 类来生成随机数
        Random random = new Random();
        int randomIndex = random.nextInt(wordsArray.length);

        return wordsArray[randomIndex];
    }

    private static String name() {
        String words = "亦、星、道、坚、辉、灿、尊、秋、途、翰、强、新、思、富、铭、浚、永、宏、隆、喜、维、森、桦、世、河、衡、耀、杰、俊、领、弘、龙、瑄、亿、欧、瀚、朗、顺、荣、畅、海、林、振、康、泽、秦、西、蓝、卓、滨、利、豪、春、敬、峻、玮、榕、兴、金、智、毅、承、枫、亮、盈、风、咏、宸、讯、联、祥、恒、先、禾、洁、汉、帝、立、宇、若、致、群、百、颂、楠、恩、庆、德、格、伟、光、尚、彦、勤、麟、曦、仁、聪、逸、青、鑫、同、昌、纳、凯、诚、嘉、腾、伦、贤";

        // 将汉字按照顿号分割
        String[] wordsArray = words.split("、");
        StringBuilder sb = new StringBuilder();
        int startIndex = (int) (Math.random() * wordsArray.length);
        int length = 2 + (int) (Math.random() * 2); // 长度为 2 或 3

        for (int j = 0; j < length; j++) {
            sb.append(wordsArray[(startIndex + j) % wordsArray.length]);
        }
        return sb.toString();
    }

    public static void main(String[] args) {
        // String company = name();
        String company = getCompanyName("13905382213");
        System.out.println(company);
    }
}
