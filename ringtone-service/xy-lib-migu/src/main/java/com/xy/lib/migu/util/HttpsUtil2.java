package com.xy.lib.migu.util;

import cn.hutool.core.util.IdUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.xy.base.core.util.DbUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import javax.net.ssl.*;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

import static com.xy.lib.migu.config.ConfigData.*;
import static com.xy.lib.migu.util.NetUtil.getIP;

/**
 * 忽略SSL证书的HTTPS请求
 */
@Slf4j
public class HttpsUtil2 {

    public static String getUrl(String relativePath) {
        return String.format("https://%s:%s%s", platformIP, platformPort, relativePath);
    }

    public static Map<String, String> getHeaders() {
        // 生成安全访问参数
        String accSeq = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date())
                + String.valueOf(new Random().nextInt(900000) + 100000);
        String digest = null;
        try {
            digest = md5(uniqueAccId + accSeq + accPassword).toUpperCase();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        // 构造请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("uniqueAccId", uniqueAccId);
        headers.put("accSeq", accSeq);
        headers.put("digest", digest);

        return headers;
    }

    public static void main(String[] args) {
        String url = getUrl("/evrms/v3/orderContent/foreign/queryContentMembers");
        Map<String, String> params = new HashMap<>();
        params.put("orderId", orderId);
        post(url, params);
    }

    public static ResponseEntity<String> post(String url, Object params) {
        // 构造请求头
        Map<String, String> headers = getHeaders();
        return post(url, params, headers);
    }

    public static ResponseEntity<String> post(String url, Object params, Map<String, String> headers) {
        ResponseEntity<String> response = null;
        try {
            if (!(params instanceof String)) {
                Gson gson = new Gson();
                params = gson.toJson(params);
            }
            // 发送POST请求
            logRequestInfo(url, params, headers); // 记录请求信息
            response = sendPostRequest(url, params.toString(), headers);
            logResponseInfo(response); // 记录响应信息
            System.out.println(response);
        } catch (Exception e) {
            System.out.println("请求异常: " + e.getMessage());
        }
        return response;
    }

    private static ResponseEntity<String> sendPostRequest(String url, Map<String, String> params, Map<String, String> headers, boolean jsonParams)
            throws Exception {
        if (jsonParams) {
            // 先将参数Map转换为JSON字符串
            String jsonData = generateJsonParams(params);
            return sendPostRequest(url, jsonData, headers);
        } else {
            StringBuilder postData = new StringBuilder();
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (postData.length() != 0) postData.append('&');
                postData.append(entry.getKey());
                postData.append('=');
                postData.append(entry.getValue());
            }
            return sendPostRequest(url, postData.toString(), headers);
        }
    }

    private static ResponseEntity<String> sendPostRequest(String url, String paramsStr, Map<String, String> headers){
        HttpMethod method = HttpMethod.POST;
        ResponseEntity<String> response = null;
        Exception exception = null;

        long startTime = System.currentTimeMillis();
        try {
            response = sendRequest(url, paramsStr, headers, HttpMethod.POST);
        } catch (Exception e) {
            e.printStackTrace();
            exception = e;
        }finally {
            long endTime = System.currentTimeMillis();

            Entity logEntity = Entity.create("log_api")
                    .set("type", 0)
                    .set("id", IdUtil.getSnowflake().nextId())
                    .set("url", url)
                    .set("request", paramsStr)
                    .set("ip", getIP(url))
                    .set("method", method.toString())
                    .set("position", NetUtil.class.getPackage().getName() + "." + NetUtil.class.getName())
                    .set("timing", endTime - startTime)
                    .set("create_time", LocalDateTime.now());
            if (response != null) {
                logEntity.set("response", response.getBody());
            }

            if (exception != null) {
                StringWriter sw = new StringWriter();
                exception.printStackTrace(new PrintWriter(sw, true));
                String st = sw.toString();
                logEntity.set("exception", true)
                        .set("exception_name", exception.getClass().getName())
                        .set("exception_trace", st.length() > 1000 ? st.substring(0, 1000) : st);
            }
            try {
                log.info(logEntity.toString());
                Db.use().insert(logEntity);
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return response;
    }

    private static ResponseEntity<String> sendRequest(String url, String paramsStr, Map<String, String> headers, HttpMethod method)
            throws Exception {
        URL requestUrl = new URL(url);
        HttpURLConnection connection = (HttpURLConnection) requestUrl.openConnection();

        // 忽略SSL证书校验
        if (connection instanceof HttpsURLConnection) {
            ((HttpsURLConnection) connection).setHostnameVerifier(new HostnameVerifier() {
                @Override
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            });
            trustAllHosts();
        }
        // 设置请求头
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            connection.setRequestProperty(entry.getKey(), entry.getValue());
        }
        connection.setRequestMethod(method.toString());
        connection.setDoOutput(true);
        connection.setDoInput(true);

        // 发送请求参数
        OutputStream outputStream = connection.getOutputStream();
        outputStream.write(paramsStr.getBytes("UTF-8"));

        // 写入文件数据
//        FileInputStream fileInputStream = new FileInputStream(file);
//        byte[] buffer = new byte[1024];
//        int bytesRead;
//        while ((bytesRead = fileInputStream.read(buffer)) != -1) {
//            outputStream.write(buffer, 0, bytesRead);
//        }

        outputStream.flush();
        outputStream.close();

        // 获取响应结果
        int responseCode = connection.getResponseCode();
        BufferedReader bufferedReader;
        if (responseCode == 200) {
            bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
        } else {
            bufferedReader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
        }

        StringBuilder response = new StringBuilder();
        String line;
        while ((line = bufferedReader.readLine()) != null) {
            response.append(line);
        }

        bufferedReader.close();
        connection.disconnect();

        // 使用 ResponseEntity 封装响应
        ResponseEntity<String> responseEntity;
        if (responseCode == 200) {
            responseEntity = new ResponseEntity<>(response.toString(), HttpStatus.OK);
        } else {
            responseEntity = new ResponseEntity<>(response.toString(), HttpStatus.valueOf(responseCode));
        }
        return responseEntity;
    }

    private static String generateJsonParams(Map<String, String> params) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(params);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            return "";
        }
    }

    private static String md5(String input) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        md.update(input.getBytes());
        byte[] digest = md.digest();
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b & 0xff));
        }
        return sb.toString();
    }

    private static void trustAllHosts() {
        try {
            TrustManager[] trustAllCerts = new TrustManager[]{new X509TrustManager() {
                public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                }

                public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                }

                public X509Certificate[] getAcceptedIssuers() {
                    return new X509Certificate[0];
                }
            }};

            SSLContext sc = SSLContext.getInstance("TLS");
            sc.init(null, trustAllCerts, new SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void logRequestInfo(String url, Object params, Map<String, String> headers) {
        log.info("Sending POST request to: " + url);
        log.info("Request Parameters: " + params);
        log.info("Request Headers: " + headers);
    }

    private static void logResponseInfo(Object response) {
        log.info("Response: " + response);
    }

}
