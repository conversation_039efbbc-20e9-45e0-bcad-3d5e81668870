package com.xy.lib.migu.online;

import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.json.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.xy.base.core.exception.AppException;
import com.xy.base.core.util.DbUtil;
import com.xy.base.starter.redis.RedisUtils;
import com.xy.lib.migu.util.NetUtil;
import com.xy.lib.migu.vo.DepartmentObject;
import com.xy.lib.migu.vo.MemberOperationVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.http.ResponseEntity;

import java.sql.SQLException;
import java.util.*;

import static com.xy.lib.migu.config.ConfigData.orderId;

/**
 * 接口标题行
 *
 * @Author: David
 * @Date: 2023/11/16 17:36
 */
@Slf4j
public class MemberOperation {

    private static final String REDIS_SUBSCRIPTION = "sbs:";
    private static final Set<String> failCodes = new HashSet<>(Arrays.asList("000001", "000002", "000003", "000007", "000008", "000009"));


    public static void createMember(String departmentId, List<String> cellphoneList) {
        // 接口 URL
        String url = NetUtil.getUrl("/evrms/v3/orderContent/foreign/addContentMembers");

        // 构造请求参数
        MemberOperationVO operationVO = new MemberOperationVO();
        operationVO.setOrderId(orderId);
        operationVO.setDepartmentId(departmentId);
        operationVO.setBillNums(cellphoneList);

        ResponseEntity<String> response = NetUtil.getResponse(url, operationVO);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    public static void deleteMember(String cellphone) {
        String departmentId = MemberOperation.queryDeptId(cellphone);

        // 接口 URL
        String url = NetUtil.getUrl("/evrms/v3/orderContent/foreign/deleteContentMembers");

        // 构造请求参数
        MemberOperationVO operationVO = new MemberOperationVO();
        operationVO.setOrderId(orderId);
        operationVO.setDepartmentId(departmentId);
        operationVO.addCellphone(cellphone);

        ResponseEntity<String> response = NetUtil.getResponse(url, operationVO);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    // 批量删除风险较大，弃用
    @Deprecated
    private static void deleteMembers(String departmentId, List<String> cellphoneList) {
        // 接口 URL
        String url = NetUtil.getUrl("/evrms/v3/orderContent/foreign/deleteContentMembers");

        // 构造请求参数
        MemberOperationVO operationVO = new MemberOperationVO();
        operationVO.setOrderId(orderId);
        operationVO.setDepartmentId(departmentId);
        operationVO.setBillNums(cellphoneList);

        ResponseEntity<String> response = NetUtil.getResponse(url, operationVO);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    /**
     * 根据部门id查询部门内的所有成员
     *
     * @param departmentId
     * @return
     */
    public static List<DepartmentObject> queryDeptMember(String departmentId) {
        // 接口 URL
        String url = NetUtil.getUrl("/evrms/v3/orderContent/foreign/queryContentMembers");

        // 构造请求参数
        MemberOperationVO operationVO = new MemberOperationVO();
        operationVO.setOrderId(orderId);
        operationVO.setDepartmentId(departmentId);

        try {
            ResponseEntity<String> response = NetUtil.getResponse(url, operationVO);

            if (response.getStatusCode().is2xxSuccessful()) {
                String responseBody = response.getBody();
                log.info("Response: " + responseBody);
                return DepartmentObject.parseDepartments(responseBody);
            } else {
                log.info("Error: " + response.getStatusCode());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String queryDeptId(String phone) {
        // 接口 URL
        String url = NetUtil.getUrl("/evrms/v3/orderContent/foreign/queryContentMembers");

        // 构造请求参数
        MemberOperationVO operationVO = new MemberOperationVO();
        operationVO.setOrderId(orderId);
        operationVO.setBillNum(phone);

        ResponseEntity<String> response = NetUtil.getResponse(url, operationVO);

        String departmentId = null;
        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            log.info("Response: " + responseBody);

            Gson gson = new Gson();
            JsonObject jsonObject = gson.fromJson(responseBody, JsonObject.class);
            JsonArray dataArray = jsonObject.getAsJsonArray("data");
            if (dataArray.size() == 0) {
                throw new AppException("请先办理订阅");
            }
            JsonElement firstDataElement = dataArray.get(0);
            departmentId = firstDataElement.getAsJsonObject().get("departmentId").getAsString();
        } else {
            log.info("Error: " + response.getStatusCode());
        }
        return departmentId;
    }

    /*
     * 检查用户是否可以订阅
     */
    public static boolean canSubscription(String phone) {

        try {
            // 1. redis
            String type = RedisUtils.get(REDIS_SUBSCRIPTION + phone);
            if (null != type) {
                log.info("phone {} member validation hit redis cache", phone);
                return type.equals("0");
            }

            // 2.db,
            try {
                Entity entity = Db.use().queryOne("select type from phone_check where billNum = ?", phone);
                if (null != entity) {
                    log.info("phone {} member validation hit database", phone);
                    type = entity.getStr("type");
                    RedisUtils.set(REDIS_SUBSCRIPTION + phone, type);
                    return type.equals("0");
                }
            } catch (SQLException e) {
                e.printStackTrace();
                log.error("数据库查询异常：", e);
            }

            Map<String, String> result = memberValidate(phone);
            // 3 api
            if (null != result) {
                String code = result.get("code");
                boolean isValidate = !failCodes.contains(code);
                type = isValidate ? "0" : "5";
                RedisUtils.set(REDIS_SUBSCRIPTION + phone, type);
                Entity entity = Entity.create("phone_check");
                entity.set("billNum", phone);
                entity.set("create_time", new Date());
                entity.set("description", result.get("description"));
                entity.set("state", code);
                entity.set("type", type);
                try {
                    Db.use().insert(entity);
                } catch (SQLException e) {
                    e.printStackTrace();
                    log.error("捕获到异常: ", e);
                }

                log.info("phone {} member validation invoke api", phone);
                return type.equals("0");
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("捕获到异常: ", e);
        }

        return true;
    }

    /*
     * 第三方接口验证 手机是否能够订阅
     * */
    public static Map<String, String> memberValidate(String phone) {
        // 第三方接口 URL
        String url = NetUtil.getUrl("/evrms/v3/orderContent/foreign/addMemberValidate");

        try {
            // 构造请求参数
            MemberOperationVO operationVO = new MemberOperationVO();
            operationVO.setOrderId(orderId);
            operationVO.setPhone(phone);

            // 调用接口
            ResponseEntity<String> responseEntity = NetUtil.getResponse(url, operationVO);

            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                String responseBody = responseEntity.getBody();
                log.info("Response: " + responseBody);

                JSONObject jsonObject = JSONUtil.parseObj(responseBody);
                String code = jsonObject.getJSONObject("data").getStr("code");
                String desc = jsonObject.getJSONObject("data").getStr("desc");

                Map<String, String> result = new HashMap<>();
                result.put("description", desc);
                result.put("code", code);
                return result;
            } else {
                log.info("Error: " + responseEntity.getStatusCode());
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("捕获到异常：", e);
        }

        return null;
    }

    /*
     * 检查用户是否可以订阅
     */
    public static boolean memberValidateOld(String phone) {

        // 第三方接口 URL
        String url = NetUtil.getUrl("/evrms/v3/orderContent/foreign/addMemberValidate");
        try {
            // 构造请求参数
            MemberOperationVO operationVO = new MemberOperationVO();
            operationVO.setOrderId(orderId);
            operationVO.setPhone(phone);

            // 调用接口
            ResponseEntity<String> responseEntity = NetUtil.getResponse(url, operationVO);

            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                String responseBody = responseEntity.getBody();
                log.info("Response: " + responseBody);

                JSONObject jsonObject = JSONUtil.parseObj(responseBody);
                String code = jsonObject.getJSONObject("data").getStr("code");

                List<String> failCodeList = Arrays.asList("000001", "000002", "000003", "000004", "000005", "000007", "000008", "000009");
                Set<String> failCodes = new HashSet<>(failCodeList);

                // 判断返回的 code 值
                if (failCodes.contains(code)) {
                    return false;
                } else {
                    return true;
                }
            } else {
                log.info("Error: " + responseEntity.getStatusCode());
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("捕获到异常：", e);
        }
        return true;
    }


    public static String queryDeptId(String phone, String uniqueAccId, String accPassword, String orderId) {
        // 接口 URL
        String url = NetUtil.getUrl("/evrms/v3/orderContent/foreign/queryContentMembers");

        // 构造请求参数
        MemberOperationVO operationVO = new MemberOperationVO();
        operationVO.setOrderId(orderId);
        operationVO.setBillNum(phone);

        ResponseEntity<String> response = NetUtil.getResponse(url, operationVO, uniqueAccId, accPassword);

        String departmentId = null;
        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            log.info("Response: " + responseBody);

            Gson gson = new Gson();
            JsonObject jsonObject = gson.fromJson(responseBody, JsonObject.class);
            JsonArray dataArray = jsonObject.getAsJsonArray("data");
            if (dataArray.size() == 0) {
                throw new AppException("请先办理订阅");
            }
            JsonElement firstDataElement = dataArray.get(0);
            departmentId = firstDataElement.getAsJsonObject().get("departmentId").getAsString();
        } else {
            log.info("Error: " + response.getStatusCode());
        }
        return departmentId;
    }

    public static List<DepartmentObject> queryDepartment(String phone) {
        // 接口 URL
        String url = NetUtil.getUrl("/evrms/v3/orderContent/foreign/queryContentMembers");

        // 构造请求参数
        MemberOperationVO operationVO = new MemberOperationVO();
        operationVO.setOrderId(orderId);
        operationVO.setBillNum(phone);

        try {
            ResponseEntity<String> response = NetUtil.getResponse(url, operationVO);

            String departmentId = null;
            if (response.getStatusCode().is2xxSuccessful()) {
                String responseBody = response.getBody();
                log.info("Response: " + responseBody);
                List<DepartmentObject> departmentObjects = null;
                departmentObjects = DepartmentObject.parseDepartments(responseBody);
                return departmentObjects;
            } else {
                log.info("Error: " + response.getStatusCode());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static List<DepartmentObject> queryDepartment(String phone, String orderId) {
        // 接口 URL
        String url = NetUtil.getUrl("/evrms/v3/orderContent/foreign/queryContentMembers");

        // 构造请求参数
        MemberOperationVO operationVO = new MemberOperationVO();
        operationVO.setOrderId(orderId);
        operationVO.setBillNum(phone);

        try {
            ResponseEntity<String> response = NetUtil.getResponse(url, operationVO);

            String departmentId = null;
            if (response.getStatusCode().is2xxSuccessful()) {
                String responseBody = response.getBody();
                log.info("Response: " + responseBody);
                List<DepartmentObject> departmentObjects = null;
                departmentObjects = DepartmentObject.parseDepartments(responseBody);
                return departmentObjects;
            } else {
                log.info("Error: " + response.getStatusCode());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) {
        DbUtil.setActive("dev-zsgl");
//        queryDeptMember("3099994436335");
//        queryDeptId("13533577707");
//        deleteMember("13880899948");
//        deleteMember("15121999164");
//        queryDepartment("18887926955");
//        queryDepartment("18887926953", "62500046209");
//        String departmentId = queryDeptId("13551861290");
//        List<DepartmentObject> departmentObjects = queryDeptMember(departmentId);

        memberValidate("13088032952");
//        hasPhoneNumSubscription("13533577707");
    }
}


