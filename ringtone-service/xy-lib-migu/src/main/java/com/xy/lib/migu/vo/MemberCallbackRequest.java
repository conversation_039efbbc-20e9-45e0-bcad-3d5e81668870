package com.xy.lib.migu.vo;


import lombok.Data;
import lombok.ToString;

/**
 * 接口标题行
 *
 * @Author: David
 * @Date: 2023/11/19 16:51
 */
@Data
@ToString
public class MemberCallbackRequest {
    /**
     * 类型：
     * 1 - 添加成员
     * 2 - 删除成员
     */
    private int type;

    /**
     * 订单编码
     */
    private String orderId;

    /**
     * 成员手机号
     */
    private String billNum;

    /**
     * 处理结果码，000000 是成功，000001 是失败
     */
    private String state;

    /**
     * 处理结果描述/失败原因描述
     */
    private String desc;

    /**
     * 操作发起方，取值: 开放平台、企业彩铃平台、渠道自身发起的渠道编码
     */
    private String operSystem;

    /**
     * 归档时间，格式: yyyyMMddHHmmss
     */
    private String finishedTime;


    /**
     * 部门Id
     */
    private String departmentId;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 产品Id
     */
    private String productId;

    /**
     * 一级渠道编码
     */
    private String channelCode;

    /**
     * 二级渠道手机号码
     */
    private String adminMsisdn;

    /**
     * 创建结果返回码
     */
    private String resultCode;

    /**
     * 创建结果描述
     */
    private String resultDesc;

    /**
     * 扩展字段1（最大128个字节）
     */
    private String firstExtendedField;

    /**
     * 扩展字段2（最大128个字节）
     */
    private String secondExtendedField;

    /**
     * 扩展字段3（最大128个字节）
     */
    private String thirdExtendedField;

    /**
     * 扩展字段4（最大128个字节）
     */
    private String fourthExtendField;

    /**
     * 扩展字段5（最大128个字节）
     */
    private String fifthExtendField;

    /**
     * 超长扩展字段（最大2048字节）
     */
    private String longestExtendField;
}
