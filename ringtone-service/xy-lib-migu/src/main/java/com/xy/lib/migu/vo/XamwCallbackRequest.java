package com.xy.lib.migu.vo;


import lombok.Data;
import lombok.ToString;

/**
 * 接口标题行
 *
 * @Author: David
 * @Date: 2023/11/19 16:51
 */
@Data
@ToString
public class XamwCallbackRequest {
    // 操作类型
    private String oprType;
    // 结果说明
    private String resultDesc;
    // 订购号码
    private String msisdn;
    // 订购时间
    private String timestamp;
    // 状态
    private String state;

    // 构造函数
    public XamwCallbackRequest(String oprType, String resultDesc, String msisdn, String timestamp, String state) {
        this.oprType = oprType;
        this.resultDesc = resultDesc;
        this.msisdn = msisdn;
        this.timestamp = timestamp;
        this.state = state;
    }
}
