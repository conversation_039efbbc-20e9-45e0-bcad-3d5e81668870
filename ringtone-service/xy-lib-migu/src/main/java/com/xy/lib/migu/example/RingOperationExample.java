package com.xy.lib.migu.example;

import com.xy.lib.migu.online.DepartmentOperation;
import com.xy.lib.migu.util.HttpsUtil;
import com.xy.lib.migu.vo.SetDeptRingsByTimeRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static com.xy.lib.migu.config.ConfigData.orderId;
import static com.xy.lib.migu.util.HttpsUtil.getUrl;
import static com.xy.lib.migu.util.HttpsUtil.post;
import static com.xy.lib.migu.util.NetUtil.*;

/**
 * 接口标题行
 *
 * @Author: David
 * @Date: 2023/11/16 17:36
 */
@Slf4j
public class RingOperationExample {

    private static String departmentId = "3099988961569";

    public static void upload() {
        // 接口 URL
        String url = getUrl("/evrms/v3/orderContent/foreign/submitRing");

        HttpHeaders headers = getHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        // 构建请求体
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("orderId", orderId);
        body.add("departmentId", departmentId);
        body.add("ringName", "video1");
        body.add("ringFile", new FileSystemResource(new File("D:\\zhongshi\\dev\\data\\video1.mp4")));
        body.add("copyrightFile", new FileSystemResource(new File("D:\\zhongshi\\dev\\data\\copyright.pdf")));
        body.add("ringType", "1");
        body.add("extractAudio", "1");

        ResponseEntity<String> response = getResponseWithHeaders(url, body, headers);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    public static void upload(String cellphone, String ringFilePath, String copyrightFilePath) {
        // 接口 URL
//        String url = getUrl("/evrms/v3/orderContent/foreign/submitRing");
        String url = "http://***************/evrms/v3/orderContent/foreign/submitRing";
        String deptId = DepartmentOperationExample.queryDeptId(cellphone);

        HttpHeaders headers = getHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        // 构建请求体
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("orderId", orderId);
        body.add("departmentId", deptId);
        body.add("ringName", "video1");
        body.add("ringFile", new FileSystemResource(new File("D:\\zhongshi\\dev\\data\\video1.mp4")));
        body.add("copyrightFile", new FileSystemResource(new File("D:\\zhongshi\\dev\\data\\copyright.pdf")));
        body.add("ringType", "1");
        body.add("extractAudio", "1");

        ResponseEntity<String> response = getResponseWithHeaders(url, body, headers);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    public static void upload_oss() {
        // 接口 URL
        String url = HttpsUtil.getUrl("/evrms/v3/orderContent/foreign/submitRing");

        Map<String, String> headers = HttpsUtil.getHeaders();
        headers.put("Content-Type", MediaType.MULTIPART_FORM_DATA.toString());

        // 构建请求体
        Map<String, Object> body = new HashMap<>();
        body.put("orderId", orderId);
        body.put("departmentId", departmentId);
        body.put("ringName", "video1");
        // 创建 URL 对象
        String filePath = "https://oss.victorycd.cn/templates/20240112/c33550dc018d31470a1aee798b3c1384.mp4";
        try {
            body.put("ringFile", new UrlResource(filePath));
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        body.put("copyrightFile", new FileSystemResource(new File("D:\\zhongshi\\dev\\data\\中国移动企业视频彩铃内容上线著作权许可使用证明承诺书.pdf")));
        body.put("ringType", "1");
        body.put("extractAudio", "1");

        ResponseEntity<String> response = post(url, body, headers);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    public static void upload(String contentProductId) {
        // 接口 URL
        String url = getUrl("/evrms/v3/orderContent/foreign/submitRing");

        HttpHeaders headers = getHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        // 构建请求体
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("orderId", orderId);
        body.add("departmentId", departmentId);
        body.add("ringName", "product1");
        body.add("copyrightFile", new FileSystemResource(new File("D:\\zhongshi\\dev\\data\\copyright.pdf")));
        body.add("ringType", "1");
        body.add("extractAudio", "1");
        body.add("fileUploadType", "2"); // 合成视频彩铃
        body.add("contentProductId", contentProductId);

        ResponseEntity<String> response = getResponseWithHeaders(url, body, headers);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    public static void setRing() {
        // 接口 URL
        String url = getUrl("/evrms/v3/orderContent/foreign/setDeptRingsByTime");

        // 构建请求体
        // ringIds需使用分发成功的铃音id
        SetDeptRingsByTimeRequest body = new SetDeptRingsByTimeRequest(
                orderId,
                departmentId,
                "00:00:00",
                "23:59:59",
                "1",
                Arrays.asList("600926100600298558", "600926100600298560")
        );

        ResponseEntity<String> response = getResponse(url, body);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    public static void query(String relativePath) {
        // 接口 URL
        String url = getUrl("/evrms/v3/orderContent/foreign/"+relativePath);

        // 构建请求体
        String body = "{\"orderId\":\"" + orderId + "\",\"departmentId\":\"" + departmentId + "\"}";

        ResponseEntity<String> response = getResponse(url, body);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    public static void queryData() {
        query("searchEcRing");
    }

    public static void querySetting() {
        query("searchEcRingSetting");
    }

    public static void main(String[] args) {
//        upload("a99fd4ab13cb64b3da025fe56ef3c934");
//        upload_oss();
//        queryData();
        upload("13880899948", "", "");
    }
}


