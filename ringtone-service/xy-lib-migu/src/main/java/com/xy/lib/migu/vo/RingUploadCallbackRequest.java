package com.xy.lib.migu.vo;

import lombok.Data;
import lombok.ToString;

/**
 * 接口标题行
 *
 * @Author: David
 * @Date: 2023/11/20 18:12
 */
@Data
@ToString
public class RingUploadCallbackRequest {
    /**
     * 铃音标识
     */
    private String streamNumber;

    /**
     * 铃音ID
     */
    private String ringId;

    /**
     * 铃音名称
     */
    private String ringName;

    /**
     * 铃音路径（可以预览）
     */
    private String ringFilePath;

    /**
     * 铃音创建时间
     */
    private String createTime;

    /**
     * 铃音类型：
     * 1 - 视频
     * 2 - 音频
     */
    private String ringType;

    /**
     * 铃音状态：
     * 00 - 已上传
     * 01 - 转码成功审核中
     * 02 - 审核未通过
     * 03 - 审核通过
     * 06 - 分发成功
     * 07 - 已过期
     * 08 - 转码失败
     */
    private String ringStatus;

    /**
     * 状态描述
     */
    private String desc;
}
