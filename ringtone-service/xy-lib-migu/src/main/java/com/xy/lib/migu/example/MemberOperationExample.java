package com.xy.lib.migu.example;

import com.xy.lib.migu.vo.MemberOperationVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;

import static com.xy.lib.migu.config.ConfigData.orderId;
import static com.xy.lib.migu.util.NetUtil.getResponse;
import static com.xy.lib.migu.util.NetUtil.getUrl;

/**
 * 接口标题行
 *
 * @Author: David
 * @Date: 2023/11/16 17:36
 */
@Slf4j
public class MemberOperationExample {

    private static String departmentId = null;

    public static void createMember() {
        // 接口 URL
        String url = getUrl("/evrms/v3/orderContent/foreign/addContentMembers");

        // 构造请求参数
        MemberOperationVO operationVO = new MemberOperationVO();
        operationVO.setOrderId(orderId);
        operationVO.setDepartmentId(departmentId);
        operationVO.setBillNums(Arrays.asList("13880899948", "13880899949", "13880899950"));

        ResponseEntity<String> response = getResponse(url, operationVO);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    public static void deleteMember() {
        // 接口 URL
        String url = getUrl("/evrms/v3/orderContent/foreign/deleteContentMembers");

        // 构造请求参数
        MemberOperationVO operationVO = new MemberOperationVO();
        operationVO.setOrderId(orderId);
        operationVO.setDepartmentId(departmentId);
        operationVO.setBillNums(Arrays.asList("13880899948", "13880899949"));

        ResponseEntity<String> response = getResponse(url, operationVO);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    public static void queryMember(String orderId) {
        // 接口 URL
        String url = getUrl("/evrms/v3/orderContent/foreign/queryContentMembers");

        // 构造请求参数
        MemberOperationVO operationVO = new MemberOperationVO();
        operationVO.setOrderId(orderId);
        operationVO.setDepartmentId("3099983898653");

//        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
//        body.add("orderId", orderId);
//
//        ResponseEntity<String> response = getResponse(url, body);
        ResponseEntity<String> response = getResponse(url, operationVO);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    public static void main(String[] args) {
        // create
//        createMember();
//        queryMember();
        // 调用 delete 之前先等待成员确认callback得到回应，亦可不断查询Member状态
//        成员状态：
//        99 订购待确认
//        00 添加处理中
//        01 添加待归档
//        02 添加归档成功
//        10 删除处理中
//        11 删除待归档
//        12 删除归档失败
//        13 删除确认中
//        queryMember("EVRBT20231207mL9JUN8W0");
        queryMember("62500188909");
        // delete  暂且先不调用
//        deleteMember();
//        queryMember();
    }
}


