package com.xy.lib.migu.onlineold;

import com.xy.lib.migu.vo.MemberOperationVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;

import java.util.List;

import static com.xy.lib.migu.config.ConfigData.orderId;
import static com.xy.lib.migu.util.HttpsUtil.getUrl;
import static com.xy.lib.migu.util.HttpsUtil.post;

/**
 * 接口标题行
 *
 * @Author: David
 * @Date: 2023/11/16 17:36
 */
@Slf4j
public class MemberOperation {

    public static void createMember(String departmentId, List<String> cellphoneList) {
        // 接口 URL
        String url = getUrl("/evrms/v3/orderContent/foreign/addContentMembers");

        // 构造请求参数
        MemberOperationVO operationVO = new MemberOperationVO();
        operationVO.setOrderId(orderId);
        operationVO.setDepartmentId(departmentId);
        operationVO.setBillNums(cellphoneList);

        ResponseEntity<String> response = post(url, operationVO);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    public static void deleteMember(String departmentId, List<String> cellphoneList) {
        // 接口 URL
        String url = getUrl("/evrms/v3/orderContent/foreign/deleteContentMembers");

        // 构造请求参数
        MemberOperationVO operationVO = new MemberOperationVO();
        operationVO.setOrderId(orderId);
        operationVO.setDepartmentId(departmentId);
        operationVO.setBillNums(cellphoneList);

        ResponseEntity<String> response = post(url, operationVO);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    public static void queryMember(String departmentId) {
        // 接口 URL
        String url = getUrl("/evrms/v3/orderContent/foreign/queryContentMembers");

        // 构造请求参数
        MemberOperationVO operationVO = new MemberOperationVO();
        operationVO.setOrderId(orderId);
        operationVO.setDepartmentId(departmentId);

        ResponseEntity<String> response = post(url, operationVO);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    public static void main(String[] args) {
//        成员状态：
//        99 订购待确认
//        00 添加处理中
//        01 添加待归档
//        02 添加归档成功
//        10 删除处理中
//        11 删除待归档
//        12 删除归档失败
//        13 删除确认中
        queryMember(null);
    }
}


