package com.xy.lib.migu.vo;


import lombok.Data;
import lombok.ToString;
import org.springframework.web.multipart.MultipartFile;

/**
 * 接口标题行
 *
 * @Author: David
 * @Date: 2023/11/20 19:16
 */
@Data
@ToString
public class RingUploadVO {
    /**
     * 订单id
     */
    private String orderId;

    /**
     * 部门id
     */
    private String departmentId;

    /**
     * 铃音名称
     */
    private String ringName;

    /**
     * 铃音文件
     */
    private MultipartFile ringFile;

    /**
     * 版权声明文件
     */
    private MultipartFile copyrightFile;

    /**
     * 铃音类型:
     * 1 - 视频
     * 2 - 音频
     */
    private String ringType;

    /**
     * 是否提取音频 (当铃音类型为视频时可填，默认为0不提取，1为提取)
     */
    private Integer extractAudio;
}
