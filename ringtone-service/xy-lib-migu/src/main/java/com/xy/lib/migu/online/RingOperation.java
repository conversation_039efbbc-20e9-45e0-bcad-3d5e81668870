package com.xy.lib.migu.online;

import cn.hutool.core.util.IdUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.xy.base.core.util.DbUtil;
import com.xy.lib.migu.util.NetUtil;
import com.xy.lib.migu.vo.SetDeptRingsByTimeRequest;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.io.File;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.xy.lib.migu.util.NetUtil.*;
import static com.xy.lib.migu.config.ConfigData.orderId;

/**
 * 接口标题行
 *
 * @Author: David
 * @Date: 2023/11/16 17:36
 */
@Slf4j
public class RingOperation {

    private static void uploadForCellphone(String cellphone, String ringName, String ringFilePath, String copyrightFilePath) {
        String deptId = MemberOperation.queryDeptId(cellphone);
        upload(deptId, ringName, ringFilePath, copyrightFilePath);
    }

    public static void uploadWithOrder4Phone(String orderNum, String cellphone, String ringName, String ringFilePath, String copyrightFilePath){
        String deptId = MemberOperation.queryDeptId(cellphone);
        uploadWithOrder(orderNum, deptId, ringName, ringFilePath, copyrightFilePath, cellphone);
    }

    // 检查是否已经上传过，若已经上传过，则直接设置
    private static boolean isUploaded(String orderNum, String departmentId) {
        try {
            Entity entity = Db.use().queryOne("select * from ring_upload where order_number = ? limit 1", orderNum);
            if (null != entity) {
                String ringId = entity.getStr("ring_id");
                if (null != ringId) {
                    log.info("该铃音已经在部门 {} 上传过，ringId={}，直接设置", departmentId, ringId);
                    setRing(departmentId, "00:00:00", "23:59:59", Collections.singletonList(ringId));
                } else {
                    log.info("该铃音已经在部门 {} 上传过，当前还在上传分发中，尚未获取到ringId", departmentId);
                }
                return true;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }

    private static void uploadWithOrder(String orderNum, String departmentId, String ringName, String ringFilePath, String copyrightFilePath, String cellphone) {
        log.info("uploadWithOrder params: {},  {},  {},  {},  {}", orderNum, departmentId, ringName, ringFilePath, copyrightFilePath);
        // 检查是否已经上传过
        boolean uploaded = isUploaded(orderNum, departmentId);
        if(uploaded){
            return;
        }

        ResponseEntity<String> response = doUpload(departmentId, ringName, ringFilePath, copyrightFilePath);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            log.info("Response: " + responseBody);
            saveData(ringName, responseBody, null, null, orderNum, cellphone);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    private static void upload(String departmentId, String ringName, String ringFilePath, String copyrightFilePath) {
        ResponseEntity<String> response = doUpload(departmentId, ringName, ringFilePath, copyrightFilePath);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            log.info("Response: " + responseBody);
            saveData(ringName, responseBody, null, null, null, null);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    private static ResponseEntity<String> doUpload(String departmentId, String ringName, String ringFilePath, String copyrightFilePath) {
        // 接口 URL
        String url = getUrl("/evrms/v3/orderContent/foreign/submitRing");

        HttpHeaders headers = getHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        // 构建请求体
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("orderId", orderId);
        body.add("departmentId", departmentId);
        body.add("ringName", ringName);
        body.add("ringFile", new FileSystemResource(new File(ringFilePath)));
        body.add("copyrightFile", new FileSystemResource(new File(copyrightFilePath)));
        body.add("ringType", "1");
        body.add("extractAudio", "1");

        return getResponseWithHeaders(url, body, headers);
    }

    private static void saveData(String ringName, String responseBody, String fileUploadType, String contentProductId, String orderNum, String phone) {
        try {
            // 提取 "streamNumber" 值
            JSONObject jsonObject = new JSONObject(responseBody);
            String streamNumber = jsonObject.getJSONObject("data").getString("streamNumber");

            Entity entity = Entity.create("ring_upload")
                    .set("stream_number", streamNumber)
                    .set("ring_name", ringName)
                    .set("ring_type", "1")
                    .set("phone", phone);
            if (null != fileUploadType) {
                entity.set("file_upload_type", fileUploadType);
            }
            entity.set("content_product_id", contentProductId);
            entity.set("order_number", orderNum);
            Db.use().insert(entity);

        } catch (JSONException | SQLException e) {
            e.printStackTrace();
        }

    }

    public static void uploadContentProduct4Phone(String orderNum, String phone, String ringName, String contentProductId, String copyrightFilePath){
        String deptId = MemberOperation.queryDeptId(phone);
        uploadContentProduct(orderNum, deptId, ringName, contentProductId, copyrightFilePath, phone);
    }
    private static void uploadContentProduct(String orderNum, String departmentId, String ringName, String contentProductId, String copyrightFilePath, String phone) {
        log.info("uploadContentProduct params: {},  {},  {},  {},  {}", orderNum, departmentId, ringName, contentProductId, copyrightFilePath);
        // 检查是否已经上传过
        boolean uploaded = isUploaded(orderNum, departmentId);
        if(uploaded){
            return;
        }
        // 接口 URL
        String url = getUrl("/evrms/v3/orderContent/foreign/submitRing");

        HttpHeaders headers = getHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        // 构建请求体
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("orderId", orderId);
        body.add("departmentId", departmentId);
        body.add("ringName", ringName);
        body.add("copyrightFile", new FileSystemResource(new File(copyrightFilePath)));
        body.add("ringType", "1");
        body.add("extractAudio", "1");
        body.add("fileUploadType", "2"); // 合成视频彩铃
        body.add("contentProductId", contentProductId);

        ResponseEntity<String> response = getResponseWithHeaders(url, body, headers);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
            saveData(ringName, responseBody, "2", contentProductId, orderNum, phone);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    public static void setRing(String departmentId, String startTime, String endTime, List<String> ringIds) {
        // 接口 URL
        String url = getUrl("/evrms/v3/orderContent/foreign/setDeptRingsByTime");

        // 构建请求体
        // ringIds需使用分发成功的铃音id
        SetDeptRingsByTimeRequest body = new SetDeptRingsByTimeRequest(
                orderId,
                departmentId,
                startTime,
                endTime,
                "1",
                ringIds
        );

        ResponseEntity<String> response = getResponse(url, body);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    public static void setRing(String departmentId, String startTime, String endTime, List<String> ringIds, String uniqueAccId, String accPassword, String orderId) {
        // 接口 URL
        String url = getUrl("/evrms/v3/orderContent/foreign/setDeptRingsByTime");

        // 构建请求体
        // ringIds需使用分发成功的铃音id
        SetDeptRingsByTimeRequest body = new SetDeptRingsByTimeRequest(
                orderId,
                departmentId,
                startTime,
                endTime,
                "1",
                ringIds
        );

        ResponseEntity<String> response = getResponse(url, body, uniqueAccId, accPassword);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    public static void query(String relativePath, String departmentId) {
        // 接口 URL
        String url = getUrl("/evrms/v3/orderContent/foreign/" + relativePath);

        // 构建请求体
        String body = "{\"orderId\":\"" + orderId + "\",\"departmentId\":\"" + departmentId + "\"}";

        ResponseEntity<String> response = getResponse(url, body);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    public static void queryData(String departmentId) {
        query("searchEcRing", departmentId);
    }

    public static void querySetting(String departmentId) {
        query("searchEcRingSetting", departmentId);
    }

    private static void testUpload() {
        String cellphone = "13880899948";
        String ringName = "video2";
        String ringFilePath = "D:\\zhongshi\\dev\\data\\video1.mp4";
        String copyRightPath = "D:\\zhongshi\\dev\\data\\copyright.pdf";
        uploadForCellphone(cellphone, ringName, ringFilePath, copyRightPath);
    }

    private static void testUploadContentProduct() {
        String orderNum = "1GJIHSVOK4S00";
        String cellphone = "13880899948";
        String ringName = "定制视频彩铃";
        String contentProductId = "d4ed03cfe57f8906c4ca41fff0564836";
        String copyRightPath = "D:\\copyright\\通用证书.pdf";
        uploadContentProduct4Phone(orderNum, cellphone, ringName, contentProductId, copyRightPath);
    }

    public static void main(String[] args) {
//        queryData("DPT15769927183");
//        querySetting("DPT15769927183");
//        String departmentId = DepartmentOperation.queryDeptId("13880718836");
//        System.out.println("departmentId: " + departmentId);
//        setRing(departmentId, "00:00:00", "23:59:59", Collections.singletonList("600926100601972696"));
//        String departmentId = DepartmentOperation.queryDeptId("13880899948");
//        System.out.println("departmentId: " + departmentId);
//        setRing(departmentId, "00:00:00", "23:59:59", Collections.singletonList("600926100601973117"));
//        testUpload();
        testUploadContentProduct();
    }
}


