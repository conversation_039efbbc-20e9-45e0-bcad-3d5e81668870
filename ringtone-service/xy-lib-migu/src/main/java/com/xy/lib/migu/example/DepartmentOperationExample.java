package com.xy.lib.migu.example;

import com.xy.lib.migu.config.ConfigData;
import com.xy.lib.migu.util.NetUtil;
import com.xy.lib.migu.vo.DepartmentObject;
import com.xy.lib.migu.vo.EnterpriseDeptOperationVO;
import com.xy.lib.migu.vo.EnterpriseDeptQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;

import java.util.List;

import static com.xy.lib.migu.util.NetUtil.getResponse;
import static com.xy.lib.migu.util.NetUtil.getUrl;

/**
 * 接口标题行
 *
 * @Author: David
 * @Date: 2023/11/16 17:36
 */
@Slf4j
public class DepartmentOperationExample {

    private static String departmentId = null;

    public static void operateDept(String opType, String deptID, String deptName) {
        // 接口 URL
        String url = getUrl("/evrms/v3/orderContent/foreign/ecOperation");

        // 构造请求参数
        EnterpriseDeptOperationVO deptOperationVO = new EnterpriseDeptOperationVO();
        deptOperationVO.setOrderId("62200518688");
        deptOperationVO.setOpType(opType);
        if("0".equals(opType)) {
            deptOperationVO.setAdminMsisdn("13533331313");
        }
        else{
            deptOperationVO.setDepartmentId(deptID);
        }
        if (null == deptName) {
            deptOperationVO.setDepartmentName("ZSGL测试部门1");
        }
        else {
            deptOperationVO.setDepartmentName(deptName);
        }

        ResponseEntity<String> response = getResponse(url, deptOperationVO);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    public static void queryDept() {
        // 接口 URL
        String url = getUrl("/evrms/v3/orderContent/foreign/searchEcDepartments");

        // 构造请求参数
        EnterpriseDeptQueryVO deptQueryVO = new EnterpriseDeptQueryVO();
//        deptQueryVO.setOrderId("62200518688");
//        deptQueryVO.setAdminMsisdn("13533331313");
        deptQueryVO.setOrderId("62500188909");
        deptQueryVO.setAdminMsisdn("13551861290");

        ResponseEntity<String> response = getResponse(url, deptQueryVO);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }


    public static String queryDeptId(String cellphone) {
        String deptString = queryDept(ConfigData.orderId, "DPT" + cellphone);
        try {
            List<DepartmentObject> departmentObjects = DepartmentObject.parseDepartments(deptString);
            return departmentObjects.get(0).getDepartmentId();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String queryDept(String orderId) {
        // 构造请求参数
        EnterpriseDeptQueryVO deptQueryVO = new EnterpriseDeptQueryVO();
        deptQueryVO.setOrderId(orderId);

        return queryDept(deptQueryVO);
    }

    public static String queryDept(String orderId, String departmentName) {
        // 构造请求参数
        EnterpriseDeptQueryVO deptQueryVO = new EnterpriseDeptQueryVO();
        deptQueryVO.setOrderId(orderId);
        deptQueryVO.setDepartmentName(departmentName);

        return queryDept(deptQueryVO);
    }

    public static String queryDept(EnterpriseDeptQueryVO deptQueryVO) {
        // 接口 URL
        String url = NetUtil.getUrl("/evrms/v3/orderContent/foreign/searchEcDepartments");

        ResponseEntity<String> response = getResponse(url, deptQueryVO);
        String responseBody = null;

        if (response.getStatusCode().is2xxSuccessful()) {
            responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
        return responseBody;
    }

    public static void main(String[] args) {
        // create
//        operateDept("0", null, null);
//        queryDept();
//        // update
//        operateDept("2", "3099954556372", "ZSGL测试部门3");
//        queryDept();
//        // delete
//        operateDept("1", "3099954556372", null);
//        queryDept();
        queryDeptId("13880899948");
    }
}
