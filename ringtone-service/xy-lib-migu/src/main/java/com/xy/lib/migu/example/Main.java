package com.xy.lib.migu.example;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

import javax.net.ssl.*;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.URL;
import java.net.HttpURLConnection;

@Slf4j
public class Main {
    public static void main(String[] args) throws NoSuchAlgorithmException {
        String url = "https://218.205.238.252:443/evrms/v3/orderContent/foreign/queryContentMembers";
        Map<String, String> params = new HashMap<>();
        params.put("orderId", "62500188909");

        // 生成安全访问参数
        String uniqueAccId = "037600N"; // 替换成你的唯一标识
        String accSeq = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date())
                + String.valueOf(new Random().nextInt(900000) + 100000);
        String accPassword = "UtNJNVrSHg7%"; // 替换成你的访问密码
        String digest = md5(uniqueAccId + accSeq + accPassword).toUpperCase();

        // 构造请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("uniqueAccId", uniqueAccId);
        headers.put("accSeq", accSeq);
        headers.put("digest", digest);

        // 发送POST请求
        try {
            logRequestInfo(url, params, headers); // 记录请求信息
            String response = sendPostRequest(url, params, headers);
            logResponseInfo(response); // 记录响应信息
            System.out.println(response);
        } catch (Exception e) {
            System.out.println("请求异常: " + e.getMessage());
        }
    }

    private static String sendPostRequest(String url, Map<String, String> params, Map<String, String> headers)
            throws Exception {
        URL requestUrl = new URL(url);
        HttpURLConnection connection = (HttpURLConnection) requestUrl.openConnection();

        // 忽略SSL证书校验
        if (connection instanceof HttpsURLConnection) {
            ((HttpsURLConnection) connection).setHostnameVerifier(new HostnameVerifier() {
                @Override
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            });
            trustAllHosts();
        }

        // 设置请求头
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            connection.setRequestProperty(entry.getKey(), entry.getValue());
        }

        connection.setRequestMethod("POST");
        connection.setDoOutput(true);
        connection.setDoInput(true);

        // 构造请求参数
//        StringBuilder postData = new StringBuilder();
//        for (Map.Entry<String, String> entry : params.entrySet()) {
//            if (postData.length() != 0) postData.append('&');
//            postData.append(entry.getKey());
//            postData.append('=');
//            postData.append(entry.getValue());
//        }

        // 先将参数Map转换为JSON字符串
        String jsonData = generateJsonParams(params);

        // 发送请求参数
        OutputStream outputStream = connection.getOutputStream();
//        outputStream.write(postData.toString().getBytes("UTF-8"));
        outputStream.write(jsonData.getBytes("UTF-8"));
        outputStream.flush();
        outputStream.close();

        // 获取响应结果
        int responseCode = connection.getResponseCode();
        BufferedReader bufferedReader;
        if (responseCode == 200) {
            bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
        } else {
            bufferedReader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
        }

        StringBuilder response = new StringBuilder();
        String line;
        while ((line = bufferedReader.readLine()) != null) {
            response.append(line);
        }

        bufferedReader.close();
        connection.disconnect();

        return response.toString();
    }

    private static String generateJsonParams(Map<String, String> params) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(params);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            return "";
        }
    }

    private static String md5(String input) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        md.update(input.getBytes());
        byte[] digest = md.digest();
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b & 0xff));
        }
        return sb.toString();
    }

    private static void trustAllHosts() {
        try {
            TrustManager[] trustAllCerts = new TrustManager[] { new X509TrustManager() {
                public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                }

                public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                }

                public X509Certificate[] getAcceptedIssuers() {
                    return new X509Certificate[0];
                }
            } };

            SSLContext sc = SSLContext.getInstance("TLS");
            sc.init(null, trustAllCerts, new SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void logRequestInfo(String url, Map<String, String> params, Map<String, String> headers) {
        log.debug("Sending POST request to: " + url);
        log.debug("Request Parameters: " + params);
        log.debug("Request Headers: " + headers);
    }

    private static void logResponseInfo(String response) {
        log.debug("Response: " + response);
    }

}
