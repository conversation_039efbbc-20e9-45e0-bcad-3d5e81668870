package com.xy.lib.migu.onlineold;

import com.xy.lib.migu.example.DepartmentOperationExample;
import com.xy.lib.migu.vo.SetDeptRingsByTimeRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.io.File;
import java.util.List;

import static com.xy.lib.migu.config.ConfigData.orderId;
import static com.xy.lib.migu.util.NetUtil.*;

/**
 * 接口标题行
 *
 * @Author: David
 * @Date: 2023/11/16 17:36
 */
@Slf4j
public class RingOperation {

    public static void uploadForCellphone(String cellphone, String ringName, String ringFilePath, String copyrightFilePath) {
        String deptId = DepartmentOperationExample.queryDeptId(cellphone);
        upload(deptId, ringName, ringFilePath, copyrightFilePath);
    }

    public static void upload(String departmentId, String ringName, String ringFilePath, String copyrightFilePath) {
        // 接口 URL
        String url = getUrl("/evrms/v3/orderContent/foreign/submitRing");

        HttpHeaders headers = getHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        // 构建请求体
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("orderId", orderId);
        body.add("departmentId", departmentId);
        body.add("ringName", ringName);
        body.add("ringFile", new FileSystemResource(new File(ringFilePath)));
        body.add("copyrightFile", new FileSystemResource(new File(copyrightFilePath)));
        body.add("ringType", "1");
        body.add("extractAudio", "1");

        ResponseEntity<String> response = getResponseWithHeaders(url, body, headers);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    public static void uploadContentProduct(String departmentId, String ringName, String contentProductId, String copyrightFilePath) {
        // 接口 URL
        String url = getUrl("/evrms/v3/orderContent/foreign/submitRing");

        HttpHeaders headers = getHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        // 构建请求体
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("orderId", orderId);
        body.add("departmentId", departmentId);
        body.add("ringName", ringName);
        body.add("copyrightFile", new FileSystemResource(new File(copyrightFilePath)));
        body.add("ringType", "1");
        body.add("extractAudio", "1");
        body.add("fileUploadType", "2"); // 合成视频彩铃
        body.add("contentProductId", contentProductId);

        ResponseEntity<String> response = getResponseWithHeaders(url, body, headers);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    public static void setRing(String departmentId, String startTime, String endTime, List<String> ringIds) {
        // 接口 URL
        String url = getUrl("/evrms/v3/orderContent/foreign/setDeptRingsByTime");

        // 构建请求体
        // ringIds需使用分发成功的铃音id
        SetDeptRingsByTimeRequest body = new SetDeptRingsByTimeRequest(
                orderId,
                departmentId,
                startTime,
                endTime,
                "1",
                ringIds
        );

        ResponseEntity<String> response = getResponse(url, body);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    public static void query(String relativePath, String departmentId) {
        // 接口 URL
        String url = getUrl("/evrms/v3/orderContent/foreign/" + relativePath);

        // 构建请求体
        String body = "{\"orderId\":\"" + orderId + "\",\"departmentId\":\"" + departmentId + "\"}";

        ResponseEntity<String> response = getResponse(url, body);

        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            // 处理响应数据...
            log.info("Response: " + responseBody);
        } else {
            log.info("Error: " + response.getStatusCode());
        }
    }

    public static void queryData(String departmentId) {
        query("searchEcRing", departmentId);
    }

    public static void querySetting(String departmentId) {
        query("searchEcRingSetting", departmentId);
    }

    private static void testUpload(){
        String cellphone = "13880899948";
        String ringName = "video2";
        String ringFilePath = "D:\\zhongshi\\dev\\data\\video1.mp4";
        String copyRightPath = "D:\\zhongshi\\dev\\data\\copyright.pdf";
        uploadForCellphone(cellphone, ringName, ringFilePath, copyRightPath);
    }

    public static void main(String[] args) {
//        queryData("DPT15769927183");
//        querySetting("DPT15769927183");
//        String departmentId = DepartmentOperation.queryDeptId("13880718836");
//        System.out.println("departmentId: " + departmentId);
//        setRing(departmentId, "00:00:00", "23:59:59", Collections.singletonList("600926100601972696"));
//        String departmentId = DepartmentOperation.queryDeptId("13880899948");
//        System.out.println("departmentId: " + departmentId);
//        setRing(departmentId, "00:00:00", "23:59:59", Collections.singletonList("600926100601973117"));
        testUpload();
    }
}


