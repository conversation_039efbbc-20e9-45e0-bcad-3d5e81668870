server:
  port: 8912
  servlet:
    context-path: /uaa
    encoding:
      force: true
      charset: utf-8
      enabled: true
  tomcat:
    connection-timeout: 180000

spring:
  profiles:
    active: dev-zsgl
  application:
    name: xy-uaa
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

wechat:
  # 公众号配置
  pub:
    appid: wxb5c9d665ca1b7f14
    appsecret: 4855717459a2420558a7eb989ec9933b
    wxtoken: ringtone
    jsurl: https://www.xy.com/app/
    authUrl: https://ring.victorycd.cn/app/#/pages/index/index

  # 小程序配置
  mini:
    appid: wx7fcb006ea3576ae5
    appsecret: dffa7622795cd4372de0b0f73c9a20cd
  # 支付配置
  pay:
    mchId: 1660292017
    apiV3Key: C1B3bzadDOas923DfGasd892adlpqwer
    keyPath: classpath:apiclient_cert.p12
    privateKeyPath: classpath:apiclient_key.pem
    privateCertPath: classpath:apiclient_cert.pem
    notifyUrl: https://ring.victorycd.cn/uaa/order/notify