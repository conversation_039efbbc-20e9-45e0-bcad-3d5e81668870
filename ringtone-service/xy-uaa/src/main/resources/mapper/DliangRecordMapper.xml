<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.uaa.mapper.DliangRecordMapper">
  <resultMap id="BaseResultMap" type="com.xy.uaa.entity.DliangRecord">
    <!--@mbg.generated-->
    <!--@Table dliang_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="pro" jdbcType="VARCHAR" property="pro" />
    <result column="notice" jdbcType="VARCHAR" property="notice" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, channel, `source`, phone, pro, notice, create_time, update_time
  </sql>

</mapper>