<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.uaa.mapper.AccessTokenMapper">

    <resultMap id="BaseResultMap" type="com.xy.uaa.entity.AccessToken">
            <id property="id" column="id" />
            <result property="phone" column="phone" />
            <result property="stage" column="stage" />
            <result property="channel" column="channel" />
            <result property="source" column="source" />
            <result property="pro" column="pro" />
            <result property="platform" column="platform" />
            <result property="appPackage" column="app_package" />
            <result property="appName" column="app_name" />
            <result property="url" column="url" />
            <result property="ua" column="ua" />
            <result property="us" column="us" />
            <result property="ft" column="ft" />
            <result property="code" column="code" />
            <result property="errorCode" column="error_code" />
            <result property="msg" column="msg" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,phone,stage,channel,source,pro,
        platform,app_package,app_name,url,ua,
        us,ft,code,error_code,msg,
        create_time,update_time
    </sql>
</mapper>
