<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.uaa.mapper.ProInfoMapper">
  <resultMap id="BaseResultMap" type="com.xy.uaa.entity.ProInfo">
    <!--@mbg.generated-->
    <!--@Table pro_info-->
    <id column="pro" jdbcType="VARCHAR" property="pro" />
    <result column="channel_code" jdbcType="VARCHAR" property="channelCode" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="admin_phone" jdbcType="VARCHAR" property="adminPhone" />
    <result column="hotline" jdbcType="VARCHAR" property="hotline" />
    <result column="advertiser" jdbcType="VARCHAR" property="advertiser" />
    <result column="unique_acc_id" jdbcType="VARCHAR" property="uniqueAccId" />
    <result column="acc_password" jdbcType="VARCHAR" property="accPassword" />
    <result column="is_popup" jdbcType="INTEGER" property="isPopup" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    pro, channel_code, product_id, admin_phone, hotline, advertiser, unique_acc_id, acc_password, 
    is_popup, create_time, update_time
  </sql>
  <update id="updateByPrimaryKey" parameterType="com.xy.uaa.entity.ProInfo">
    <!--@mbg.generated-->
    update pro_info
    set channel_code = #{channelCode,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=VARCHAR},
      admin_phone = #{adminPhone,jdbcType=VARCHAR},
      hotline = #{hotline,jdbcType=VARCHAR},
      advertiser = #{advertiser,jdbcType=VARCHAR},
      unique_acc_id = #{uniqueAccId,jdbcType=VARCHAR},
      acc_password = #{accPassword,jdbcType=VARCHAR},
      is_popup = #{isPopup,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where pro = #{pro,jdbcType=VARCHAR}
  </update>
</mapper>