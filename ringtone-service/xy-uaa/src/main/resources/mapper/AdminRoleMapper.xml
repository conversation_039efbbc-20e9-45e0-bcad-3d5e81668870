<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.uaa.mapper.AdminRoleMapper">


    <select id="listWithRole" resultMap="AdminRoleVO">

        SELECT uaa_admin_role.id          as uaa_admin_role_id,
               uaa_admin_role.admin_id    as uaa_admin_role_admin_id,
               uaa_admin_role.role_id     as uaa_admin_role_role_id,
               uaa_admin_role.create_time as uaa_admin_role_create_time,
               uaa_role.id                as uaa_role_id,
               uaa_role.name              as uaa_role_name,
               uaa_role.creator_id        as uaa_role_creator_id,
               uaa_role.create_time       as uaa_role_create_time

        FROM uaa_admin_role
                 LEFT JOIN uaa_role ON uaa_admin_role.role_id = uaa_role.id
            ${ew.customSqlSegment}


    </select>

    <resultMap id="AdminRoleVO" type="com.xy.uaa.vo.AdminRoleVO">
        <id property="id" column="uaa_admin_role_id"/>
        <result property="adminId" column="uaa_admin_role_admin_id"/>
        <result property="roleId" column="uaa_admin_role_role_id"/>
        <result property="createTime" column="uaa_admin_role_create_time"/>
        <result property="name" column="uaa_role_name"/>
    </resultMap>
</mapper>
