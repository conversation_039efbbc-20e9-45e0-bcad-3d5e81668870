spring:
  cloud:
    nacos:
      config:
        enabled: false
        server-addr: 192.168.1.158:8848
        prefix: base
        file-extension: yaml
        group: DEFAULT_GROUP
      discovery:
        enabled: false
        server-addr: 192.168.1.158:8848
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************************************************
    username: couser1
    password: USero01@ARd21@Liy2504
    hikari:
      connection-timeout: 3000
      idle-timeout: 5000
      max-lifetime: 5500
      maximum-pool-size: 20
      minimum-idle: 5

  redis:
    database: 0
    host: **********
    password: Sqs@redis
    port: 6379

logging:
  config: classpath:logback-spring-test.xml
