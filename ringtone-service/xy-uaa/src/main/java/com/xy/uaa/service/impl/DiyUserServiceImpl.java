package com.xy.uaa.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.uaa.entity.DiyUser;
import com.xy.uaa.service.DiyUserService;
import com.xy.uaa.mapper.DiyUserMapper;
import org.springframework.stereotype.Service;

/**
 * 针对表【diy_user(用户diy数据)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Service
public class DiyUserServiceImpl extends ServiceImpl<DiyUserMapper, DiyUser>
        implements DiyUserService {

}




