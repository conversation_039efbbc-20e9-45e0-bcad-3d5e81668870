package com.xy.uaa.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyV3Result;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderV3Request;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderV3Result;
import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.constant.WxPayConstants;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.xy.base.core.annotation.DirectOutput;
import com.xy.base.core.enums.ExceptionResultEnum;
import com.xy.base.core.enums.UaaTypeEnum;
import com.xy.base.core.exception.AppException;
import com.xy.base.core.response.Result;
import com.xy.base.core.util.ThreadUtil;
import com.xy.base.starter.dto.CommonPageQuery;
import com.xy.base.starter.redis.RedisUtils;
import com.xy.base.starter.security.entity.UaaUserDetails;
import com.xy.base.starter.security.util.SecurityUtils;
import com.xy.lib.migu.config.ConfigData;
import com.xy.lib.migu.online.DepartmentOperation;
import com.xy.lib.migu.online.MemberOperation;
import com.xy.lib.migu.online.RingOperation;
import com.xy.lib.migu.util.AdTrackingUtil;
import com.xy.lib.migu.util.ProUtil;
import com.xy.lib.migu.vo.DepartmentObject;
import com.xy.lib.migu.vo.PhoneLocationType;
import com.xy.uaa.component.WechatServiceComponent;
import com.xy.uaa.dto.UniOrderDTO;
import com.xy.uaa.entity.AccessToken;
import com.xy.uaa.entity.RingtoneOrder;
import com.xy.uaa.entity.RingtoneTemplate;
import com.xy.uaa.entity.SubscriptionOrder;
import com.xy.uaa.service.AccessTokenService;
import com.xy.uaa.service.RingtoneOrderService;
import com.xy.uaa.service.RingtoneTemplateService;
import com.xy.uaa.service.SubscriptionOrderService;
import com.xy.uaa.util.PayUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/11/24 16:29
 */
@Slf4j
@RestController
@RequestMapping("/order")
@RequiredArgsConstructor
public class OrderController {

    @Value("${wechat.pay.notifyUrl}")
    private String payNotifyUrl;

    private final RingtoneOrderService ringtoneOrderService;
    private final SubscriptionOrderService subscriptionOrderService;
    private final WechatServiceComponent wechatServiceComponent;
    private final RingtoneTemplateService ringtoneTemplateService;
    private final AccessTokenService tokenService;


    /**
     * 用户获取自己的订单列表
     *
     * @param query query
     * @return Func
     */
    @PostMapping("/listMy")
    public IPage<RingtoneOrder> listMy(@RequestBody CommonPageQuery query) {
        UaaUserDetails current = SecurityUtils.current();

        Page<RingtoneOrder> page = query.buildPage();
        return ringtoneOrderService.lambdaQuery().eq(RingtoneOrder::getUserId, current.getId()).eq(query.getType() != null, RingtoneOrder::getPayed, query.getType()).orderByDesc(RingtoneOrder::getOrderNumber).page(page);
    }

    /**
     * 检查用户是否订阅
     */
    @GetMapping("/hasSubscription")
    public boolean hasSubscription() {
        List<DepartmentObject> depList = MemberOperation.queryDepartment(SecurityUtils.current().getPhone());
        return null != depList && !depList.isEmpty();
    }

    /**
     * 创建订阅订单
     */
    @PostMapping("/createSubscription")
    public SubscriptionOrder createSubscription(@RequestBody @Valid SubscriptionOrder order) {
        order.setPro(ProUtil.tryTranslatePro(order.getPro()));

        String token = RedisUtils.get(order.getPhone());

        AppException.tnt(!order.getToken().equals(token), "非法请求");

        String orderNumber = Long.toUnsignedString(IdUtil.getSnowflakeNextId(), 32).toUpperCase();
        order.setOrderNumber(orderNumber);
        order.setCreateTime(LocalDateTime.now());
        subscriptionOrderService.save(order);
        // 处理pro商自有逻辑
        processPro(order.getChannel(), order.getSource(), order.getPhone(), order.getPro());
        return order;
    }

    private void processPro(String channel, String source, String phone, String pro) {
        log.info("in processPro");
        AppException.tnt((null == phone || null == channel || null == source), ExceptionResultEnum.PARAMS_ERROR);

        AppException.tnt(null == pro, ExceptionResultEnum.PARAMS_ERROR);

        pro = ProUtil.tryTranslatePro(pro);

        if ("nmch".equals(pro)) {
            ProUtil.nmchSaveUser(phone);
        }
    }

    /**
     * 创建订阅订单并上传
     */
    @PostMapping("/createOrder")
    public Result<String> createOrder(@RequestBody Map<String, String> params) {
        AppException.tnt(params == null || params.size() < 1, "参数错误");
        String phone = params.get("phone");
        String token = params.get("token");
        AppException.tnt(phone == null || token == null, "请输入正确的手机号");

        String redisToken = RedisUtils.get(phone);
        AppException.tnt(!token.equals(redisToken), "非法请求");

        String channel = params.get("channel");
        String source = params.get("source");
        String pro = params.get("pro");
        AppException.tnt(channel == null || source == null, "channel,source必须存在");
        AppException.tnt(pro == null, "pro 必须存在");
        String url = params.get("url");
        AppException.tnt(url == null, "url必须存在");
        source = ProUtil.tryConvertSource(pro, channel, source, phone);
        params.put("source", source);

        params.put("pro", ProUtil.tryTranslatePro(pro));
        if (!params.containsKey("ua")) {
            String ua = RedisUtils.get(phone + "ua");
            if (null != ua) {
                params.put("ua", ua);
            }
        }
        if(!params.containsKey("projectid")) {
            String adid = RedisUtils.get(phone + "adid");
            if (null != adid) {
                String[] adids = adid.split(":");
                if (adids.length >= 2) {
                    params.put("projectid", adids[0]);
                    params.put("promotionid", adids[1]);
                }
            }
        }

        // 创建订单
        SubscriptionOrder order = new SubscriptionOrder();
        order.setPhone(phone);
        order.setToken(token);
        order.setChannel(channel);
        order.setSource(source);
        String orderNumber = Long.toUnsignedString(IdUtil.getSnowflakeNextId(), 32).toUpperCase();
        order.setOrderNumber(orderNumber);
        order.setCreateTime(LocalDateTime.now());
        // subscriptionOrderService.save(order);
        saveAccessInfo(phone, channel, source, pro);

        // 处理pro商自有逻辑
        processPro(channel, source, phone, pro);

        // 回传
        AdTrackingUtil.report(phone, channel, source, params);
        return Result.success(orderNumber);
    }

    private void saveAccessInfo(String phone, String channel, String source, String pro) {
        try {
            pro = ProUtil.tryTranslatePro(pro);
            // 根据 phone, channel, source, pro 找到对应的 access_token, 取离当前时间点最近的一条
            AccessToken accessToken = tokenService.lambdaQuery().eq(AccessToken::getPhone, phone)
                    .eq(AccessToken::getChannel, channel).eq(AccessToken::getSource, source).eq(AccessToken::getPro, pro)
                    .orderByDesc(AccessToken::getCreateTime).last("limit 1").one();

            if (accessToken == null) {
                return;
            }
            // 更新 access_token 表
            accessToken.setStage(1);
            tokenService.updateById(accessToken);
        } catch (Exception except) {
            except.printStackTrace();
            log.error("捕获到异常: ", except);
        }
    }

    /**
     * 查询订阅订单详情
     */
    @GetMapping("/querySubscription/{orderNumber}")
    public SubscriptionOrder querySubscription(@PathVariable("orderNumber") String orderNumber) {
        SubscriptionOrder order = subscriptionOrderService.getById(orderNumber);
        AppException.tnt(order == null, "订单号错误");
        // 隐藏手机号
        order.setPhone(null);

        return order;
    }

    /**
     * 创建订单Old
     */
    @PostMapping("/createOld")
    public RingtoneOrder createOld(@RequestBody @Valid RingtoneOrder order) {
        UaaUserDetails current = SecurityUtils.current();
        AppException.tnt(!current.getType().equals(UaaTypeEnum.USER.getValue()), "请使用用户身份下单");
        String deptString = DepartmentOperation.queryDept(ConfigData.orderId, "DPT" + current.getPhone());
//        String deptString = DepartmentOperation.queryDept(ConfigData.orderId, "DPT" + "13880718836");
//        String deptString = "{\"code\":\"000000\",\"info\":\"鎿嶄綔鎴愬姛\",\"data\":[{\"departmentId\":\"3099991306977\",\"departmentName\":\"DPT19574514011\",\"adminMsisdn\":null,\"createTime\":\"20240124101554\"}]}";
        if (StringUtils.hasText(deptString)) {
            try {
                JSONObject jsonObject = JSONUtil.parseObj(deptString);
                JSONArray data = jsonObject.getJSONArray("data");
                if (data.size() > 0) {
                    JSONObject dept = data.getJSONObject(0);

                    RingtoneTemplate template = ringtoneTemplateService.getById(order.getRingtoneId());
                    AppException.tnt(template == null, "模板错误");

                    // 是否已经购买过此模板
                    boolean exists = ringtoneOrderService.lambdaQuery()
//                            .eq(RingtoneOrder::getUserId, current.getId())
                            .eq(RingtoneOrder::getPayed, true).eq(RingtoneOrder::getRingtoneId, template.getId()).exists();

                    AppException.tnt(exists, "您已添加过此模板，请直接设置");

                    String orderNumber = Long.toUnsignedString(IdUtil.getSnowflakeNextId(), 32).toUpperCase();
                    order.setOrderNumber(orderNumber);
                    order.setCreateTime(LocalDateTime.now());
//                    order.setOpenid(current.getOpenid());
//                    order.setUserId(current.getId());
                    order.setFee(template.getPrice());
                    order.setRingtoneCover(template.getCover());
                    order.setRingtoneName(template.getName());
//                    order.setPhone(current.getPhone());
                    order.setDepartmentId(dept.getStr("departmentId"));
                    order.setDepartmentName(dept.getStr("departmentName"));

                    if (template.getPrice() <= 0) {
                        // 0元订单直接支付并设置铃声
                        order.setPayed(true);
                        order.setPayTime(LocalDateTime.now());
//                        ThreadUtil.getExecutorService().execute(() -> {
//                            RingOperation.setRing(order.getDepartmentId(), "00:00:00", "23:59:59", Collections.singletonList(template.getId()));
//                        });
                    }

                    ringtoneOrderService.save(order);
                    return order;
                }
            } catch (Exception e) {
                if (e instanceof AppException) {
                    throw e;
                } else {
                    throw new AppException("网络异常");
                }
            }


        }

        throw new AppException("请先办理订阅");


    }

    /**
     * 创建订单new
     *
     * <AUTHOR>
     * @date 2025/2/11
     */
    @PostMapping("/create")
    public RingtoneOrder create(@RequestBody @Valid RingtoneOrder order) {
        UaaUserDetails current = SecurityUtils.current();
        AppException.tnt(!current.getType().equals(UaaTypeEnum.USER.getValue()), "请使用用户身份下单");
        // 查询部门信息
        List<DepartmentObject> depList = MemberOperation.queryDepartment(current.getPhone());
        // 测试用
//        List<DepartmentObject> depList = MemberOperation.queryDepartment("13880718836");
        if (null == depList || depList.isEmpty()) {
            throw new AppException("请先订阅");
        }
        try {
            DepartmentObject department = depList.get(0);

            RingtoneTemplate template = ringtoneTemplateService.getById(order.getRingtoneId());
            AppException.tnt(template == null, "模板错误");

            // 判断是否已购买过此模板
            boolean exists = ringtoneOrderService.lambdaQuery().eq(RingtoneOrder::getUserId, current.getId())
                    .eq(RingtoneOrder::getPayed, true).eq(RingtoneOrder::getRingtoneId, template.getId()).exists();

            AppException.tnt(exists, "您已添加过此模板，请直接设置");

            // 生成订单
            order.setOrderNumber(Long.toUnsignedString(IdUtil.getSnowflakeNextId(), 32).toUpperCase());
            order.setCreateTime(LocalDateTime.now());
            order.setOpenid(current.getOpenid());
            order.setUserId(current.getId());
            order.setFee(template.getPrice());
            order.setRingtoneCover(template.getCover());
            order.setRingtoneName(template.getName());
            order.setPhone(current.getPhone());
            order.setDepartmentId(department.getDepartmentId());
            order.setDepartmentName(department.getDepartmentName());

            // 处理 0 元订单
            if (template.getPrice() <= 0) {
                order.setPayed(true);
                order.setPayTime(LocalDateTime.now());
            }

            // 保存订单
            ringtoneOrderService.save(order);
            return order;
        } catch (Exception e) {
            if (e instanceof AppException) {
                throw e;
            } else {
                throw new AppException("网络异常");
            }
        }
    }

    /**
     * 取消订单
     */
    @PostMapping("/cancelOrder")
    public Boolean cancelOrder(@RequestBody @Valid RingtoneOrder orderDTO) {
        UaaUserDetails current = SecurityUtils.current();
        AppException.tnt(!current.getType().equals(UaaTypeEnum.USER.getValue()), "请使用用户身份下单");

        RingtoneOrder order = ringtoneOrderService.lambdaQuery().eq(RingtoneOrder::getUserId, current.getId()).eq(RingtoneOrder::getOrderNumber, orderDTO.getOrderNumber()).one();

        AppException.tnt(order == null, "参数错误");

        order.setStatus(-10);

        return ringtoneOrderService.updateById(order);
    }

    /**
     * 统一下单，获取支付参数
     */
    @PostMapping("/uniOrder")
    public WxPayUnifiedOrderV3Result.JsapiResult uniOrder(@RequestBody @Valid UniOrderDTO uniOrder) {
        RingtoneOrder order = ringtoneOrderService.getById(uniOrder.getOrderNumber());
        AppException.tnt(order == null, "订单不存在");
        AppException.tnt(order.getPayed(), "订单已支付");

        // 是否已经购买过此模板
        boolean exists = ringtoneOrderService.lambdaQuery().eq(RingtoneOrder::getUserId, order.getUserId()).eq(RingtoneOrder::getPayed, true).eq(RingtoneOrder::getRingtoneId, order.getRingtoneId()).exists();

        AppException.tnt(exists, "您已添加过此模板，请直接设置");

        WxPayUnifiedOrderV3Request request = new WxPayUnifiedOrderV3Request();
        WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
        amount.setTotal(order.getFee());
        amount.setCurrency("CNY");
        request.setAmount(amount);
        request.setOutTradeNo(order.getOrderNumber());
        request.setDescription(order.getRingtoneName());
        WxPayUnifiedOrderV3Request.Payer payer = new WxPayUnifiedOrderV3Request.Payer();
        payer.setOpenid(uniOrder.getOpenid());
        request.setPayer(payer);
        request.setNotifyUrl(payNotifyUrl);
        try {
            return wechatServiceComponent.getPayService().createOrderV3(TradeTypeEnum.JSAPI, request);
        } catch (WxPayException e) {
            log.error("下单错误", e);
            throw new AppException("下单失败，请稍后再试");
        }
    }


    /**
     * @ignore 支付回调通知处理
     */
    @DirectOutput
    @PostMapping("/notify")
    public String parseOrderNotifyResult(@RequestBody String notifyData, HttpServletRequest request) throws WxPayException {
        log.info("【支付回调通知处理】:{}", notifyData);
        WxPayOrderNotifyV3Result result = wechatServiceComponent.getPayService().parseOrderNotifyV3Result(notifyData, PayUtil.getRequestHeader(request));
        // 解密后的数据
        WxPayOrderNotifyV3Result.DecryptNotifyResult notifyResult = result.getResult();
        if (WxPayConstants.WxpayTradeStatus.SUCCESS.equals(notifyResult.getTradeState())) {

            RingtoneOrder order = ringtoneOrderService.getById(notifyResult.getOutTradeNo());
            order.setPayed(true);
            order.setPayTime(LocalDateTime.parse(notifyResult.getSuccessTime(), DateTimeFormatter.ISO_OFFSET_DATE_TIME));
            order.setTransactionId(notifyResult.getTransactionId());
            order.setOutTradeNo(notifyResult.getOutTradeNo());

            ringtoneOrderService.updateById(order);
            // 支付回调成功，设置用户的彩铃
//            ThreadUtil.getExecutorService().execute(() -> {
//                RingOperation.setRing(order.getDepartmentId(), "00:00:00", "23:59:59", Collections.singletonList(order.getRingtoneId()));
//            });
        }
        if (WxPayConstants.WxpayTradeStatus.PAY_ERROR.equals(notifyResult.getTradeState())) {
            log.error("【支付回调通知失败】:{}", result);
            throw new WxPayException("微信支付-回调失败！");
        }
        return WxPayNotifyResponse.success("OK");
    }

    /**
     * 根据订单设置彩铃
     */
    @GetMapping("/setRing/{orderNumber}")
    @Transactional(rollbackFor = Exception.class)
    public void setRing(@PathVariable("orderNumber") String orderNumber) {

        RingtoneOrder order = ringtoneOrderService.getById(orderNumber);

        AppException.tnt(order == null, "订单号错误");

        RingtoneTemplate template;
        if (order.getProductId().equals("diy")) {
            // 自定义铃声，调用其他方法
            template = new RingtoneTemplate();
            template.setName(order.getRingtoneName());
            template.setCopyright(null);
            template.setType(2);
        } else {
            template = ringtoneTemplateService.getById(order.getRingtoneId());
        }

        String deptId = MemberOperation.queryDeptId(order.getPhone());
        String phone = order.getPhone();

        String ringName = template.getName();
        String copyright = template.getCopyright();
        if (!StringUtils.hasText(template.getCopyright())) {
            copyright = "D:/copyright/通用证书.pdf";
            ringName = "定制视频彩铃";
        }
        String finalRingName = ringName;
        String finalCopyright = copyright;
        if (template.getType().equals(1)) {
            RingOperation.setRing(deptId, "00:00:00", "23:59:59", Collections.singletonList(order.getRingtoneId()));
            order.setStatus(20);
        } else if (template.getType().equals(2)) {
            ThreadUtil.execute("设置彩铃", (n) -> {
                RingOperation.uploadContentProduct4Phone(orderNumber, phone, finalRingName, order.getProductId(), finalCopyright);
            });
            order.setStatus(10);
        } else if (template.getType().equals(4)) {
            ThreadUtil.execute("上传设置彩铃", (n) -> {
                String ringPath = template.getVideo().replace("https://oss.victorycd.cn", "D:");
                RingOperation.uploadWithOrder4Phone(orderNumber, phone, finalRingName, ringPath, finalCopyright);
            });
            order.setStatus(10);
        }

        order.setCurrent(true);

        ringtoneOrderService.lambdaUpdate().eq(RingtoneOrder::getUserId, SecurityUtils.getId()).set(RingtoneOrder::getCurrent, false).update();

        ringtoneOrderService.updateById(order);
    }


}
