package com.xy.uaa.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @TableName uaa_admin
 */
@TableName(value = "uaa_admin")
@Data
public class Admin implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    @NotNull
    private Integer orgId;

    private String orgPath;

    /**
     * 用户名
     */
    @NotNull
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 账号状态：0禁用，1正常
     */
    private Integer status;

    /**
     * 创建者，0表示系统
     */
    private Integer creatorId;

    /**
     *
     */
    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}