package com.xy.uaa.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xy.uaa.entity.Func;
import com.xy.uaa.vo.FuncTreeVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface FuncService extends IService<Func> {

    /**
     * 功能树
     *
     * @return FuncTreeVO
     */
    List<FuncTreeVO> tree();

    /**
     * 删除功能
     *
     * @param id id
     * @return boolean
     */
    boolean del(int id);

    /**
     * 获取个人所有具有的权限
     *
     * @return FuncTreeVO
     */
    List<FuncTreeVO> myTree();
}
