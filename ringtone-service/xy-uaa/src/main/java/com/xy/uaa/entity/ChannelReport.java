package com.xy.uaa.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@TableName(value = "channel_report")
@Data
public class ChannelReport implements Serializable {

    /**
     * 
     */
    private Long id;
    /**
     * 渠道：推广商
     */
    private String channel;
    /**
     * 请求来源：app、app2
     */
    private String source;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 0：未回传，1：已回传
     */
    private Integer reportStatus;
    /**
     * 回调字符串
     */
    private String callback;
    /**
     * 回调平台
     */
    private String platform;
    /**
     * 引导用户进来时素材对应的铃音id
     */
    private String ring;
    /**
     * 创建日期
     */
    private LocalDateTime createTime;
    /**
     * 修改日期
     */
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
