package com.xy.uaa.controller;

import com.xy.lib.migu.online.MemberOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequiredArgsConstructor
@RequestMapping("/test")
public class testController {
    @PostMapping("/test01")
    public String test01(@RequestBody Map<String, String> params) {
        String phone = params.get("phone");
        boolean result = MemberOperation.canSubscription(phone);
        System.out.println(result);
        return result ? "true" : "false";
    }
}
