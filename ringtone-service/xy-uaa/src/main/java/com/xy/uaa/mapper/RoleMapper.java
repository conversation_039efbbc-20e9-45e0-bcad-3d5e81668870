package com.xy.uaa.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.xy.uaa.entity.Func;
import com.xy.uaa.entity.Role;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Entity com.xy.uaa.entity.Role
 */
public interface RoleMapper extends BaseMapper<Role> {

    @Select("select b.* from uaa_role_func a left join uaa_func b on a.func_id = b.id ${ew.customSqlSegment}")
    List<Func> selFuncs(@Param(Constants.WRAPPER) QueryWrapper<Func> uaaFuncWrapper);
}




