package com.xy.uaa.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xy.base.core.constant.CommonConsts;
import com.xy.base.core.exception.AppException;
import com.xy.base.starter.dto.CommonPageQuery;
import com.xy.base.starter.security.util.SecurityUtils;
import com.xy.uaa.entity.Func;
import com.xy.uaa.entity.Role;
import com.xy.uaa.entity.RoleFunc;
import com.xy.uaa.service.FuncService;
import com.xy.uaa.service.RoleFuncService;
import com.xy.uaa.service.RoleService;
import com.xy.uaa.vo.FuncTreeVO;
import com.xy.uaa.vo.RoleVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 功能设置，如角色，权限等
 *
 * <AUTHOR>
 * @since 2022/11/4
 */
@Slf4j
@RestController
@RequestMapping("/sys")
@RequiredArgsConstructor
public class SysController {

    private final RoleService roleService;
    private final FuncService funcService;
    private final RoleFuncService roleFuncService;

    /**
     * 添加功能，需要权限 uaa:func:save
     *
     * @param func func
     * @return Func
     */
    @PreAuthorize("hasAuthority('uaa:func:save')")
    @PostMapping("/func/save")
    public Func funcSave(@RequestBody @Valid Func func) {

        if (func.getType().equals(CommonConsts.ONE) || func.getType().equals(CommonConsts.ZERO)) {
            if (!StringUtils.hasText(func.getRoutePath()) || !func.getRoutePath().startsWith("/")) {
                throw new AppException("routePath必须为/开头的绝对路径");
            }
        }

        if (null == func.getId()) {
            func.setCreatorId(SecurityUtils.getId());
            func.setCreateTime(LocalDateTime.now());
        }

        if (func.getType() < CommonConsts.TWO) {
            // 自动生成routeName  /product/erp/detail=>ProductErpDetail
            func.setRouteName(StrUtil.toCamelCase(func.getRoutePath().replace("/", "_")));
        }
        funcService.saveOrUpdate(func);
        return func;
    }

    /**
     * 功能列表，需要权限 uaa:func:list
     *
     * @param query query
     * @return Func
     */
    @PreAuthorize("hasAuthority('uaa:func:list')")
    @PostMapping("/func/list")
    public Page<Func> funcList(@RequestBody CommonPageQuery query) {

        // 默认只能看见自己创建的角色
        Page<Func> page = query.buildPage();
        return funcService.lambdaQuery()
                .like(StringUtils.hasText(query.getKeyword()), Func::getName, query.getKeyword())
                .orderByDesc(Func::getId)
                .page(page);
    }

    /**
     * 功能树，需要权限 uaa:func:list
     *
     * @return FuncTreeVO
     */
    @PreAuthorize("hasAuthority('uaa:func:list')")
    @GetMapping("/func/tree")
    public List<FuncTreeVO> tree() {

        return funcService.tree();
    }

    /**
     * 获取我的功能树，需要权限 uaa:role:save
     *
     * @return FuncTreeVO
     */
    @PreAuthorize("hasAuthority('uaa:func:list')")
    @GetMapping("/func/tree/my")
    public List<FuncTreeVO> myTree() {

        return funcService.myTree();
    }

    /**
     * 功能树，需要权限 uaa:func:save
     *
     * @return FuncTreeVO
     */
    @PreAuthorize("hasAuthority('uaa:func:save')")
    @GetMapping("/func/del/{funcId}")
    public Boolean funDel(@PathVariable("funcId") Integer funcId) {

        if (funcService.lambdaQuery().eq(Func::getParentId, funcId).exists()) {
            throw new AppException("当前功能下有子功能，不能删除");
        }
        return funcService.del(funcId);
    }

    /**
     * 添加角色，需要权限 uaa:role:save
     *
     * @param role role
     * @return Role
     */
    @PreAuthorize("hasAuthority('uaa:role:save')")
    @PostMapping("/role/save")
    @Transactional(rollbackFor = RuntimeException.class)
    public Role roleSave(@RequestBody @Valid RoleVO role) {

        if (role.getFuncs() == null || role.getFuncs().size() < 1) {
            throw new AppException("角色必须设置功能");
        }

        return roleService.saveData(role);
    }


    /**
     * 角色列表，需要权限 uaa:role:list
     *
     * @param query query
     * @return Role
     */
    @PreAuthorize("hasAuthority('uaa:role:list')")
    @PostMapping("/role/list")
    public IPage<RoleVO> roleList(@RequestBody CommonPageQuery query) {
        return roleService.listVO(query);
    }

    /**
     * 角色列表，需要权限 uaa:role:list
     *
     * @return Role
     */
    @PreAuthorize("hasAuthority('uaa:role:list')")
    @GetMapping("/role/getRoles")
    public List<Role> myRoles() {
        return roleService.lambdaQuery()
                .likeRight(Role::getOrgPath, SecurityUtils.current().getOrgPath())
                .orderByAsc(Role::getId)
                .list();
    }

    /**
     * 角色列表，需要权限 uaa:role:list
     *
     * @return Role
     */
    @PreAuthorize("hasAuthority('uaa:role:list')")
    @GetMapping("/role/getOrgRoles/{orgId}")
    public List<Role> getOrgRoles(@PathVariable("orgId") Integer orgId) {
        return roleService.lambdaQuery()
                .eq(Role::getOrgId, orgId)
                .orderByAsc(Role::getId)
                .list();
    }

    /**
     * 不分页角色列表，需要权限 uaa:role:list
     *
     * @return Role
     */
    @PreAuthorize("hasAuthority('uaa:role:list')")
    @GetMapping("/role/all")
    public List<Role> roleAll() {
        return roleService.lambdaQuery().list();
    }

    /**
     * 角色功能列表, 需要权限 uaa:role:list
     *
     * @param roleId roleId
     * @return RoleFunc
     */
    @PreAuthorize("hasAuthority('uaa:role:list')")
    @GetMapping("/role/roleFuncs/{roleId}")
    public List<RoleFunc> roleFuncs(@PathVariable("roleId") Integer roleId) {

        return roleFuncService.lambdaQuery().eq(RoleFunc::getRoleId, roleId).list();
    }

    /**
     * 删除角色，需要权限 uaa:role:del
     *
     * @return Boolean
     */
    @PreAuthorize("hasAuthority('uaa:role:save')")
    @GetMapping("/role/del/{rolId}")
    @Transactional(rollbackFor = Exception.class)
    public Boolean roleDel(@PathVariable Integer rolId) {
        roleService.roleDel(rolId);
        return roleFuncService.lambdaUpdate().eq(RoleFunc::getRoleId, rolId).remove();
    }

}
