package com.xy.uaa.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xy.uaa.entity.DliangRecord;

public interface DliangRecordService extends IService<DliangRecord> {
  /**
   * 保存到量记录
   * 
   * @param channel 渠道
   * @param source  来源
   * @param phone   手机号
   * @param pro     推广商
   * @param notice  提醒范围
   * @return 保存结果
   */
  boolean saveDliangRecord(String channel, String source, String phone, String pro, String notice);

}
