package com.xy.uaa.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/14
 */
@Data
public class RouterTreeVO {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 上级id，0表示根目录
     */
    private Integer parentId;

    /**
     * 功能名称
     */
    private String name;

    /**
     * 如果是路由，记录页面值
     */
    private String routePath;

    /**
     * 文件路径（默认填充@/views/，所以无需输入）
     */
    private String routeFile;

    /**
     * 路由名称，通常为英文
     */
    private String routeName;

    /**
     * 路由名称，通常为英文
     */
    private Integer routeOrder;

    /**
     * 路由名称，通常为英文
     */
    private Boolean menuDisplay;

    /**
     * 路由名称，通常为英文
     */
    private String menuIcon;

    /**
     * 路由名称，通常为英文
     */
    private Boolean keepAlive;

    private List<RouterTreeVO> children;
}
