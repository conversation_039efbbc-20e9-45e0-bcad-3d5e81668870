package com.xy.uaa.component;

import com.xy.uaa.event.OrderEvent;
import lombok.SneakyThrows;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/7/27.
 */
@Component
public class OrderListener implements ApplicationListener<OrderEvent> {

    /**
     * 使用 onApplicationEvent 方法对消息进行接收处理
     */
    @SneakyThrows
    @Override
    public void onApplicationEvent(OrderEvent event) {
        String orderId = event.getOrderId();
        long start = System.currentTimeMillis();
        Thread.sleep(2000);
        long end = System.currentTimeMillis();
    }
}
