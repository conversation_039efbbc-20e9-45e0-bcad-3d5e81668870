package com.xy.uaa.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.base.starter.security.entity.UaaAuthority;
import com.xy.base.starter.security.util.SecurityUtils;
import com.xy.uaa.entity.Func;
import com.xy.uaa.entity.RoleFunc;
import com.xy.uaa.mapper.FuncMapper;
import com.xy.uaa.service.FuncService;
import com.xy.uaa.service.RoleFuncService;
import com.xy.uaa.vo.FuncTreeVO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class FuncServiceImpl extends ServiceImpl<FuncMapper, Func>
        implements FuncService {

    private final RoleFuncService roleFuncService;


    @Override
    public List<FuncTreeVO> tree() {
        List<Func> funcs = lambdaQuery().orderByAsc(Func::getRouteOrder).list();
        return buildTree(funcs, 0);
    }

    @Override
    public boolean del(int id) {
        // 删除功能
        // 1删除该功能以及该功能下功能的所有功能
        // 2删除角色中的所有该功能及改功能下功能记录

        Set<Integer> funcIds = new HashSet<>();
        Set<Integer> pIds = new HashSet<>();
        funcIds.add(id);
        pIds.add(id);

        while (true) {
            List<Func> list = lambdaQuery().in(Func::getParentId, pIds).list();
            if (list == null || list.size() < 1) {
                break;
            }
            pIds.clear();
            list.forEach(func -> {
                funcIds.add(func.getId());
                pIds.add(func.getId());
            });
        }

        roleFuncService.lambdaUpdate().in(RoleFunc::getFuncId, funcIds).remove();
        return lambdaUpdate().in(Func::getId, funcIds).remove();

    }

    @Override
    public List<FuncTreeVO> myTree() {
        List<UaaAuthority> authorities = SecurityUtils.current().getAuthorities();

        List<Func> funcs = new ArrayList<>();
        authorities.forEach(auth -> {
            Func func = new Func();
            BeanUtils.copyProperties(auth, func);
            funcs.add(func);
        });

        return buildTree(funcs, 0);
    }

    private List<FuncTreeVO> buildTree(List<Func> funcs, Integer parentId) {
        List<FuncTreeVO> trees = null;
        for (Func func : funcs) {
            if (func.getParentId().equals(parentId)) {
                FuncTreeVO funcTree = new FuncTreeVO();
                BeanUtils.copyProperties(func, funcTree);
                funcTree.setChildren(buildTree(funcs, func.getId()));
                if (null == trees) {
                    trees = new ArrayList<>();
                }
                trees.add(funcTree);
            }
        }
        return trees;
    }
}




