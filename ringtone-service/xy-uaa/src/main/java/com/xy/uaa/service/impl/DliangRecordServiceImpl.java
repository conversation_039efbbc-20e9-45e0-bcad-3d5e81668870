package com.xy.uaa.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.uaa.entity.DliangRecord;
import com.xy.uaa.mapper.DliangRecordMapper;
import com.xy.uaa.service.DliangRecordService;
import org.springframework.stereotype.Service;

@Service
public class DliangRecordServiceImpl extends ServiceImpl<DliangRecordMapper, DliangRecord>
    implements DliangRecordService {

  @Override
  public boolean saveDliangRecord(String channel, String source, String phone, String pro, String notice) {
    DliangRecord record = new DliangRecord();
    record.setChannel(channel);
    record.setSource(source);
    record.setPhone(phone);
    record.setPro(pro);
    record.setNotice(notice);
    return this.save(record);
  }

}
