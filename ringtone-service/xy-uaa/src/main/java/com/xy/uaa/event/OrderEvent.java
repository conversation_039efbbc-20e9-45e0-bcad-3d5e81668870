package com.xy.uaa.event;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @since 2022/7/27.
 */
@Getter
@Setter
public class OrderEvent extends ApplicationEvent {

    /**
     * 该类型事件携带的信息
     */
    private String orderId;

    public OrderEvent(Object source, String orderId) {
        super(source);
        this.orderId = orderId;
    }
}
