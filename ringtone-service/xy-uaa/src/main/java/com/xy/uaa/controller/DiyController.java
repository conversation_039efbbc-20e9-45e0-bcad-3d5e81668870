package com.xy.uaa.controller;


import com.xy.base.core.constant.CommonConsts;
import com.xy.base.core.exception.AppException;
import com.xy.base.core.util.IdUtils;
import com.xy.base.starter.security.util.SecurityUtils;
import com.xy.uaa.entity.DiySysRes;
import com.xy.uaa.entity.DiyUser;
import com.xy.uaa.entity.RingtoneOrder;
import com.xy.uaa.service.DiySysResService;
import com.xy.uaa.service.DiyUserService;
import com.xy.uaa.service.RingtoneOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户自定义铃声
 *
 * <AUTHOR>
 * @since 2025/1/3 17:58
 */
@RestController
@RequestMapping("/diy")
@RequiredArgsConstructor
public class DiyController {

    private final DiySysResService sysResService;
    private final DiyUserService diyUserService;
    private final RingtoneOrderService orderService;


    /**
     * 是否有还在审核的内容
     */
    @GetMapping("/user/hasAudit")
    public Boolean hasAudit() {
        return diyUserService.lambdaQuery().eq(DiyUser::getUserId, SecurityUtils.getId())
                .eq(DiyUser::getStatus, CommonConsts.ZERO)
                .exists();
    }

    /**
     * 获取正在审核的资源数据
     */
    @GetMapping("/user/myAudit")
    public DiyUser myAudit() {

        // 查询相关订单状态，如果设置成功，那么修改状态为上传
        DiyUser diyUser = diyUserService.lambdaQuery().eq(DiyUser::getUserId, SecurityUtils.getId())
                .orderByDesc(DiyUser::getId)
                .last("limit 1")
                .one();

        if (diyUser != null) {
            RingtoneOrder order = orderService.lambdaQuery().eq(RingtoneOrder::getRingtoneId, diyUser.getId())
                    .orderByDesc(RingtoneOrder::getCreateTime)
                    .last("limit 1")
                    .one();

            if (order != null && order.getStatus() > 10) {
                diyUser.setStatus(2);
                diyUserService.updateById(diyUser);
            }
        }

        return diyUser;
    }

    /**
     * 素材列表
     */
    @GetMapping("/res/all")
    public List<DiySysRes> resAll() {
        return sysResService.lambdaQuery().orderByDesc(DiySysRes::getId).list();
    }

    /**
     * 用户数据
     */
    @PostMapping("/user/add")
    public DiyUser userAdd(@RequestBody DiyUser user) {

        boolean exists = diyUserService.lambdaQuery().eq(DiyUser::getUserId, SecurityUtils.getId())
                .eq(DiyUser::getStatus, CommonConsts.ZERO)
                .exists();

        AppException.tnt(exists, "您还有正在审核的订单，请稍后");

        user.setId(IdUtils.nextId());
        user.setUserId(SecurityUtils.getId());
        user.setUserPhone(SecurityUtils.current().getPhone());
        user.setCreateTime(LocalDateTime.now());
        user.setStatus(0);

        diyUserService.save(user);

        return user;
    }


}
