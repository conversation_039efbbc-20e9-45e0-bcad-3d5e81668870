package com.xy.uaa.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.xy.uaa.entity.AdminRole;
import com.xy.uaa.vo.AdminRoleVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Entity com.xy.uaa.entity.AdminRole
 */
public interface AdminRoleMapper extends BaseMapper<AdminRole> {

    /**
     * 查询AdminRoleVO
     *
     * @param wrapper wrapper
     * @return return
     */
    List<AdminRoleVO> listWithRole(@Param(Constants.WRAPPER) Wrapper<AdminRoleVO> wrapper);
}




