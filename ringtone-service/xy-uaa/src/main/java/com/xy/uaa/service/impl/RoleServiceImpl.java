package com.xy.uaa.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.base.core.exception.AppException;
import com.xy.base.starter.dto.CommonPageQuery;
import com.xy.base.starter.security.util.SecurityUtils;
import com.xy.uaa.entity.AdminRole;
import com.xy.uaa.entity.Func;
import com.xy.uaa.entity.Role;
import com.xy.uaa.entity.RoleFunc;
import com.xy.uaa.mapper.RoleMapper;
import com.xy.uaa.service.AdminRoleService;
import com.xy.uaa.service.OrgService;
import com.xy.uaa.service.RoleFuncService;
import com.xy.uaa.service.RoleService;
import com.xy.uaa.vo.RoleVO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role>
        implements RoleService {

    private final AdminRoleService adminRoleService;
    private final RoleFuncService roleFuncService;
    private final OrgService orgService;

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Role saveData(RoleVO role) {

//        Org org = orgService.getById(role.getOrgId());
//        AppException.tnt(org == null, "参数错误");

        if (role.getId() != null) {
            roleFuncService.lambdaUpdate().eq(RoleFunc::getRoleId, role.getId()).remove();
        } else {
            role.setCreatorId(SecurityUtils.getId());
            role.setCreateTime(LocalDateTime.now());
        }

//        role.setOrgId(org.getId());
//        role.setOrgPath(org.getPath());
        saveOrUpdate(role);

        List<RoleFunc> roleFuncs = new ArrayList<>();
        role.getFuncs().forEach(func -> {
            RoleFunc roleFunc = new RoleFunc();
            roleFunc.setRoleId(role.getId());
            roleFunc.setCreateTime(LocalDateTime.now());
            roleFunc.setFuncId(func.getId());
            roleFuncs.add(roleFunc);
        });
        roleFuncService.saveBatch(roleFuncs);
        return role;
    }

    @Override
    public Boolean roleDel(Integer rolId) {
        AppException.tnt(adminRoleService.lambdaQuery().eq(AdminRole::getRoleId, rolId).exists(), "该角色已分配,不能被移除");
        return removeById(rolId);
    }

    @Override
    public IPage<RoleVO> listVO(CommonPageQuery query) {
        IPage<Role> page = query.buildPage();
        lambdaQuery().like(StringUtils.hasText(query.getKeyword()), Role::getName, query.getKeyword())
                .orderByDesc(Role::getId).page(page);

        return page.convert(date ->{
            RoleVO roleVO = new RoleVO();
            BeanUtils.copyProperties(date,roleVO);
            QueryWrapper<Func> uaaFuncWrapper = new QueryWrapper<>();
            uaaFuncWrapper.eq("a.role_id",roleVO.getId());
            List<Func> funcs =  baseMapper.selFuncs(uaaFuncWrapper);
            roleVO.setFuncs(funcs);
            return roleVO;
        });
    }
}




