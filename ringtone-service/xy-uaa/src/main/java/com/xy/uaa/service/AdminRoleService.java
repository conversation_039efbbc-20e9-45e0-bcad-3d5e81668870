package com.xy.uaa.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xy.uaa.entity.AdminRole;
import com.xy.uaa.vo.AdminRoleVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AdminRoleService extends IService<AdminRole> {


    /**
     * 查询AdminRoleVO
     *
     * @param wrapper wrapper
     * @return return
     */
    List<AdminRoleVO> listWithRole(Wrapper<AdminRoleVO> wrapper);
}
