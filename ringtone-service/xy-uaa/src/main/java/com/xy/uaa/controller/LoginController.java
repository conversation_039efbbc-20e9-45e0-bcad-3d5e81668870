package com.xy.uaa.controller;

import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xy.base.core.dto.login.LoginDTO;
import com.xy.base.core.enums.UaaTypeEnum;
import com.xy.base.core.exception.AppException;
import com.xy.base.core.response.Result;
import com.xy.base.starter.redis.RedisUtils;
import com.xy.base.starter.security.entity.UaaUserDetails;
import com.xy.base.starter.security.util.SecurityUtils;
import com.xy.lib.migu.util.AdTrackingUtil;
import com.xy.lib.migu.util.LoginApiUtil;
import com.xy.lib.migu.util.ProUtil;
import com.xy.uaa.controller.dto.MiguTokenDTO;
import com.xy.uaa.dto.ChangePwdDTO;
import com.xy.uaa.dto.TrackingDTO;
import com.xy.uaa.entity.AccessToken;
import com.xy.uaa.entity.ProInfo;
import com.xy.uaa.mapper.ProInfoMapper;
import com.xy.uaa.service.AccessTokenService;
import com.xy.uaa.service.LoginService;
import com.xy.uaa.service.RingtoneCategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 登录
 *
 * <AUTHOR>
 * @since 2022/11/4
 */
@Slf4j
@RefreshScope
@RestController
@RequiredArgsConstructor
@RequestMapping("/login")
public class LoginController {

    private final LoginService loginService;
    private final RingtoneCategoryService categoryService;
    private final AccessTokenService tokenService;
    private final ProInfoMapper proInfoMapper;
    /**
     * @ignore
     */
    @GetMapping("/env")
    public Map<String, Object> env(HttpServletRequest request) {

        Map<String, Object> infos = new HashMap<>(10);

        infos.put("time2", LocalDateTime.now());
        RedisUtils.set("test", "ok", 10);
        infos.put("redis", RedisUtils.get("test"));

        infos.put("ip", ServletUtil.getClientIP(request));
        infos.put("login", SecurityUtils.isLogin());
        infos.put("x-forwarded-for", request.getHeader("x-forwarded-for"));
        infos.put("getRemoteAddress", request.getRemoteAddr());
        infos.put("X-Real-IP", request.getHeader("X-Real-IP"));

        infos.put("db", categoryService.getById(7));
        return infos;
    }

    /**
     * 统一登录
     *
     * @param dto dto
     * @return UserDetails 实际返回的是具体的子类
     */
    @PostMapping("/login")
    public UaaUserDetails login(@RequestBody LoginDTO dto) {

        return loginService.login(dto);
    }

    /**
     * 统一退出
     */
    @GetMapping("/logout")
    public void logout() {

        AppException.tnt(!SecurityUtils.isLogin(), "您还没有登录");
        loginService.logout(SecurityUtils.current().getToken());
    }

    /**
     * 适用于小程序，微信，app等自动登录
     *
     * @return UserDetails
     */
    @PostMapping("/init")
    public UaaUserDetails init(@RequestBody LoginDTO dto) {

        // 先判断redis中有没有该token，如果有直接返回,没有token看看是否有用户名密码，调用相关登录接口登录
        if (SecurityUtils.isLogin()) {
            return SecurityUtils.current();
        } else {
            if (StringUtils.hasText(dto.getPassword()) && StringUtils.hasText(dto.getUsername())) {
                return loginService.login(dto);
            } else {
                if (dto.getType().equals(UaaTypeEnum.USER.getValue()) && StringUtils.hasText(dto.getOpenid())) {
                    try {
                        return loginService.login(dto);
                    } catch (Exception ignore) {
                    }
                }
                return new UaaUserDetails();
            }
        }
    }

    /**
     * 获取migu token
     */
    @PostMapping("/miguToken2")
    public Result<String> miguToken2(@RequestBody MiguTokenDTO dto) {

        String miguStr = LoginApiUtil.login(dto.getPhone(), dto.getChannel(), dto.getSource(), dto.getPro());
        JSONObject obj = JSONUtil.parseObj(miguStr);

        return Result.success(obj.getStr("token"));
    }

    /**
     * 获取migu token，同时返回部门名称
     */
    @PostMapping("/miguToken3")
    public Result<Map<String, String>> miguToken3(@RequestBody MiguTokenDTO dto) {

        // 存储channel和source信息到Redis，便于后续到量记录使用
        RedisUtils.set("channel_source:" + dto.getPhone(), dto.getChannel() + ":" + dto.getSource(), 60L);
        String miguStr = LoginApiUtil.login(dto.getPhone(), dto.getChannel(), dto.getSource(), dto.getPro());
        JSONObject obj = JSONUtil.parseObj(miguStr);

        Map<String, String> dictionary = new HashMap<>();

        dictionary.put("token", obj.getStr("token"));
        dictionary.put("dept", obj.getStr("dept", dto.getPhone()));

        return Result.success(dictionary);
    }

    /**
     * 获取migu token, 同时提供UA
     */
    @PostMapping("/miguToken5")
    public Result<String> miguToken5(@RequestBody MiguTokenDTO dto) {
        AppException.tnt(dto.getUrl() == null, "请提供url");
        AppException.tnt(dto.getUa() == null, "请提供UA");

        // 存储channel和source信息到Redis，便于后续到量记录使用
        RedisUtils.set("channel_source:" + dto.getPhone(), dto.getChannel() + ":" + dto.getSource(), 60L);
        String miguStr = LoginApiUtil.login(dto.getPhone(), dto.getChannel(), dto.getSource(), dto.getPro(), dto.getUrl(), dto.getUa());
        JSONObject obj = JSONUtil.parseObj(miguStr);

        return Result.success(obj.getStr("token"));
    }

    /**
     * 获取migu token，同时返回部门名称，以及pro相关配置参数
     */
    @PostMapping("/miguToken6")
    public Result<Map<String, String>> miguToken6(@RequestBody MiguTokenDTO dto) {

        // 存储channel和source信息到Redis，便于后续到量记录使用
        RedisUtils.set("channel_source:" + dto.getPhone(), dto.getChannel() + ":" + dto.getSource(), 60L);
        String miguStr = LoginApiUtil.login(dto.getPhone(), dto.getChannel(), dto.getSource(), dto.getPro(), dto.getUrl(), dto.getUa());
        JSONObject obj = JSONUtil.parseObj(miguStr);

        Map<String, String> dictionary = new HashMap<>();

        dictionary.put("token", obj.getStr("token"));
        dictionary.put("dept", obj.getStr("dept", dto.getPhone()));
        dictionary.put("channelCode", obj.getStr("channelCode"));
        dictionary.put("productId", obj.getStr("productId"));
        dictionary.put("adminPhone", obj.getStr("adminPhone"));
        dictionary.put("hotline", obj.getStr("hotline"));

        return Result.success(dictionary);
    }

    /**
     * 获取migu token, 需提供UA，union_site
     */
    @PostMapping("/miguToken7")
    public Result<Map<String, String>> miguToken7(@RequestBody AccessToken dto) {
        AppException.tnt(dto.getUrl() == null, "请提供url");
        AppException.tnt(dto.getUa() == null, "请提供UA");

        try {
            RedisUtils.set("channel_src_ori:" + dto.getPhone(), dto.getChannel() + ":" + dto.getSource() + ":" + dto.getPro(), 600L);
            dto.setSource(ProUtil.tryConvertSource(dto.getPro(), dto.getChannel(), dto.getSource(), null));
            dto.setPro(ProUtil.tryTranslatePro(dto.getPro()));
            // 存储channel和source信息到Redis，便于后续到量记录使用
            RedisUtils.set("channel_source:" + dto.getPhone(), dto.getChannel() + ":" + dto.getSource(), 60L);
            String miguStr = LoginApiUtil.login(dto.getPhone(), dto.getChannel(), dto.getSource(), dto.getPro(), dto.getUrl(), dto.getUa(), dto.getUs(), dto.getFt());
            JSONObject obj = JSONUtil.parseObj(miguStr);

            Map<String, String> dictionary = new HashMap<>();

            dictionary.put("pro", obj.getStr("pro"));
            dictionary.put("token", obj.getStr("token"));
            dictionary.put("dept", obj.getStr("dept", dto.getPhone()));
            dictionary.put("channelCode", obj.getStr("channelCode"));
            dictionary.put("productId", obj.getStr("productId"));
            dictionary.put("adminPhone", obj.getStr("adminPhone"));
            dictionary.put("hotline", obj.getStr("hotline"));

            dto.setCode("200");
            tokenService.save(dto);

            return Result.success(dictionary);
        } catch (AppException e) {
            saveExceptionInfo(dto, e);
            throw e;
        } catch (Exception e) {
            saveExceptionInfo(dto, e.toString());
            throw e;
        }
    }

    /**
     * API接口：获取migu token，同时返回部门名称，以及pro相关配置参数
     */
    @PostMapping("/miguTokenAPI")
    public Result<Map<String, String>> miguTokenAPI(@RequestBody AccessToken dto) {
        try {
            RedisUtils.set(dto.getPhone() + "ua", dto.getUa(), 30 * 60);
            RedisUtils.set("channel_src_ori:" + dto.getPhone(), dto.getChannel() + ":" + dto.getSource() + ":" + dto.getPro(), 600L);
            RedisUtils.set(dto.getPhone() + "adid", dto.getProjectid() + ":" + dto.getPromotionid(), 600L);
            dto.setSource(ProUtil.tryConvertSource(dto.getPro(), dto.getChannel(), dto.getSource(), null));
            dto.setPro(ProUtil.tryTranslatePro(dto.getPro()));

            // 存储channel和source信息到Redis，便于后续到量记录使用
            RedisUtils.set("channel_source:" + dto.getPhone(), dto.getChannel() + ":" + dto.getSource(), 60L);
            String miguStr = LoginApiUtil.loginAPI(dto.getPhone(), dto.getChannel(), dto.getSource(), dto.getPro(), dto.getUrl(), dto.getUa());
            JSONObject obj = JSONUtil.parseObj(miguStr);

            Map<String, String> dictionary = new HashMap<>();

            dictionary.put("token", obj.getStr("token"));
            dictionary.put("dept", obj.getStr("dept", dto.getPhone()));
            dictionary.put("channelCode", obj.getStr("channelCode"));
            dictionary.put("productId", obj.getStr("productId"));
            dictionary.put("adminPhone", obj.getStr("adminPhone"));
            dictionary.put("hotline", obj.getStr("hotline"));
            dictionary.put("pro", obj.getStr("pro"));

            dto.setCode("200");
            tokenService.save(dto);

            return Result.success(dictionary);
        } catch (AppException e) {
            saveExceptionInfo(dto, e);
            throw e;
        } catch (Exception e) {
            saveExceptionInfo(dto, e.toString());
            throw e;
        }
    }
    
    /**
     * 是否弹窗
     *
     * @param pro 主键
     * @return  响应结果
     */
    @RequestMapping("/isPopup")
    public Result<Boolean> isPopup(@RequestParam String pro) {
        ProInfo proInfo = proInfoMapper.selectOne(new LambdaQueryWrapper<ProInfo>().eq(ProInfo::getPro, ProUtil.tryTranslatePro(pro)));
        return  Result.success(proInfo.getIsPopup());
    }

    private void saveExceptionInfo(AccessToken dto, AppException e) {
        try {
            dto.setCode(Integer.toString(e.getCode()));
            dto.setErrorCode(e.getErrorCode());
            dto.setMsg(e.getMessage());
            tokenService.save(dto);
        } catch (Exception except) {
            except.printStackTrace();
            log.error("捕获到异常: ", except);
        }
    }

    private void saveExceptionInfo(AccessToken dto, String exceptionMsg) {
        try {
            dto.setMsg(exceptionMsg);
            tokenService.save(dto);
        } catch (Exception except) {
            except.printStackTrace();
            log.error("捕获到异常: ", except);
        }
    }

    /**
     * 调用快手表单
     */
    @PostMapping("/ksTracking")
    public Result<String> ksTracking(@RequestBody @Valid TrackingDTO dto) {

        String token = RedisUtils.get(dto.getPhone());

        AppException.tnt(!dto.getToken().equals(token), "非法请求");

//        return Result.success(AdTrackingUtil.kuaishouReport(dto.getCallback(), dto.getPhone(), dto.getChannel(), dto.getSource()));
        return Result.success("已废弃");
    }

    /**
     * 调用上报接口
     */
    @PostMapping("/tracking")
    public void tracking(@RequestBody Map<String, String> params, HttpServletRequest request) {
        AppException.tnt(params == null || params.size() < 1, "参数错误");
        String phone = params.get("phone");
        String token = params.get("token");
        AppException.tnt(phone == null || token == null, "请输入正确的手机号");

        String redisToken = RedisUtils.get(phone);
        AppException.tnt(!token.equals(redisToken), "非法请求");

        String channel = params.get("channel");
        String source = params.get("source");
        // 兼容性处理
        String page = params.get("page");
        log.info("origin channel:{}, source:{}, page:{}", channel, source, page);
        if (null == source) {
            channel = "shfy";
            if ("info".equals(page)) {
                source = "ks01";
            } else if ("info2".equals(page)) {
                source = "ks02";
            }
            log.info("reset channel:{}, source:{}, page:{}", channel, source, page);
        }

        AppException.tnt(channel == null || source == null, "channel,source必须存在");

        AdTrackingUtil.report(phone, channel, source, params);
    }

    /**
     * 通过token获取当前登录信息
     *
     * @return UaaUserDetails
     */
    @GetMapping("/info")
    public UaaUserDetails userDetails() {

        return SecurityUtils.current();
    }

    /**
     * 修改密码
     *
     * @param pwd 修改密码参数
     * @return boolean
     */
    @PostMapping("/changePwd")
    public boolean changePwd(@Valid @RequestBody ChangePwdDTO pwd) {

        AppException.tnt(!SecurityUtils.isLogin(), "您还没有登录");

        return loginService.changePwd(pwd);

    }

}
