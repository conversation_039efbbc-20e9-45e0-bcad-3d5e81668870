package com.xy.uaa.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xy.base.starter.dto.CommonPageQuery;
import com.xy.uaa.entity.RingtoneCategory;
import com.xy.uaa.entity.RingtoneTemplate;
import com.xy.uaa.service.RingtoneCategoryService;
import com.xy.uaa.service.RingtoneTemplateService;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/14 10:24
 */
@RestController
@RequestMapping("/ringtone")
@RequiredArgsConstructor
public class RingtoneController {

    private final RingtoneTemplateService templateService;
    private final RingtoneCategoryService categoryService;


    /**
     * 模板列表
     */
    @PostMapping("/list")
    public IPage<RingtoneTemplate> list(@RequestBody CommonPageQuery query) {
        // code表示一级分类
        Page<RingtoneTemplate> page = query.buildPage();

        List<Integer> cateIds = new ArrayList<>();
        cateIds.add(0);
        cateIds.add(Integer.parseInt(query.getCode()));

        return templateService.lambdaQuery()
                .like(StringUtils.hasText(query.getKeyword()), RingtoneTemplate::getName, query.getKeyword())
                .in(RingtoneTemplate::getCategory, cateIds)
                .and(query.getType() != null && query.getType() == 0,
                        w -> w.isNull(RingtoneTemplate::getCategories).or().eq(RingtoneTemplate::getCategories, "")
                )
                .isNull(query.getType() != null && query.getType() == 0, RingtoneTemplate::getCategories)
                .apply(query.getType() != null && query.getType() > 0, "FIND_IN_SET({0}, categories)", query.getType())
                .orderByAsc(RingtoneTemplate::getSort)
                .orderByDesc(RingtoneTemplate::getCreateTime)
                .page(page);
    }

    /**
     * 模板详情
     */
    @GetMapping("/template/{id}")
    public RingtoneTemplate template(@PathVariable("id") String id) {
        return templateService.getById(id);
    }


    /**
     * 根据一级分类获取二级分类
     */
    @GetMapping("/cateByPid/{pid}")
    public List<RingtoneCategory> cateByPid(@PathVariable("pid") Integer pid) {

        return categoryService.lambdaQuery().eq(RingtoneCategory::getPid, pid).orderByAsc(RingtoneCategory::getSort).list();
    }
}
