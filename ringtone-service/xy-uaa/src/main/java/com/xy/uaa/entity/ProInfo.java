package com.xy.uaa.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @since 2025/6/24 14:35
 */
@Data
@Accessors(chain = true)
@SuperBuilder
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "pro_info")
public class ProInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	
	/**
	 * 广告
	 */
	@TableId(value = "pro", type = IdType.AUTO)
	@Size(max = 255, message = "广告最大长度要小于 255")
	@NotBlank(message = "广告不能为空")
	private String pro;
	
	/**
	 * 渠道号
	 */
	@TableField(value = "channel_code")
	@Size(max = 55, message = "渠道号最大长度要小于 55")
	@NotBlank(message = "渠道号不能为空")
	private String channelCode;
	
	/**
	 * 产品订购id
	 */
	@TableField(value = "product_id")
	@Size(max = 55, message = "产品订购id最大长度要小于 55")
	@NotBlank(message = "产品订购id不能为空")
	private String productId;
	
	/**
	 * 管理员电话号
	 */
	@TableField(value = "admin_phone")
	@Size(max = 55, message = "管理员电话号最大长度要小于 55")
	@NotBlank(message = "管理员电话号不能为空")
	private String adminPhone;
	
	/**
	 * 服务热线
	 */
	@TableField(value = "hotline")
	@Size(max = 55, message = "服务热线最大长度要小于 55")
	@NotBlank(message = "服务热线不能为空")
	private String hotline;
	
	/**
	 * 所属广告主
	 */
	@TableField(value = "advertiser")
	@Size(max = 55, message = "所属广告主最大长度要小于 55")
	private String advertiser;
	
	/**
	 * uniqueAccId
	 */
	@TableField(value = "unique_acc_id")
	@Size(max = 55, message = "uniqueAccId最大长度要小于 55")
	private String uniqueAccId;
	
	/**
	 * accPassword
	 */
	@TableField(value = "acc_password")
	@Size(max = 55, message = "accPassword最大长度要小于 55")
	private String accPassword;
	
	@TableField(value = "is_popup")
	private Boolean isPopup;
	
	/**
	 * 创建时间
	 */
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	
	/**
	 * 更新时间
	 */
	@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;
}