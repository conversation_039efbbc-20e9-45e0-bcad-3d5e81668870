package com.xy.uaa.util;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;

/**
 * 短信发送工具
 *
 * <AUTHOR>
 * @since 2023/11/29 9:44
 */
public class SmsUtil {

    private static final String ACCESS_KEY = "LTAI5tKMc2m6bgUvfYR8W5Lj";
    private static final String ACCESS_KEY_SECRET = "******************************";
    // 截止4月初剩余700多条，用完后切换使用下方代码
    //private static final String ACCESS_KEY = "LTAI5tSSdbUVJzJc7vjDo2Fc";
    //private static final String ACCESS_KEY_SECRET = "******************************";

    /**
     * 使用AK&SK初始化账号Client
     *
     * @return Client
     */
    public static Client createClient() throws Exception {
        Config config = new Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId(ACCESS_KEY)
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret(ACCESS_KEY_SECRET);
        // Endpoint 请参考 https://api.aliyun.com/product/Dysmsapi
        config.endpoint = "dysmsapi.aliyuncs.com";
        return new Client(config);
    }

    /**
     * 发送短信
     * @param phone 手机号
     * @param templateCode 模板名称
     * @param templateParam 模板参数
     */
    public static void send(String phone, String templateCode, String templateParam) throws Exception {
        //SMS_464120680
        Client client = createClient();
        SendSmsRequest sendSmsRequest = new SendSmsRequest()
                .setTemplateCode(templateCode)
                .setTemplateParam(templateParam)
                .setPhoneNumbers(phone)
                .setSignName("众视广联");
        try {
            // 复制代码运行请自行打印 API 的返回值
            client.sendSmsWithOptions(sendSmsRequest, new RuntimeOptions());
        } catch (TeaException error) {
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
    }

}
