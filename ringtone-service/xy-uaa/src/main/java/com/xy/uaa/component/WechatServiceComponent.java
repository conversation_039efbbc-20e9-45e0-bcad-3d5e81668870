package com.xy.uaa.component;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @date 2020/12/7
 */
@Component
public class WechatServiceComponent {

    @Value("${wechat.pub.appid}")
    private String appid;

    @Value("${wechat.pub.appsecret}")
    private String appsecret;

    @Value("${wechat.pub.wxtoken}")
    private String wxtoken;

    @Value("${wechat.mini.appid}")
    private String miniAppid;

    @Value("${wechat.mini.appsecret}")
    private String miniAppsecret;

    @Value("${wechat.pay.mchId}")
    private String mchId;

    @Value("${wechat.pay.apiV3Key}")
    private String apiV3Key;

    @Value("${wechat.pay.keyPath}")
    private String keyPath;

    @Value("${wechat.pay.privateKeyPath}")
    private String privateKeyPath;

    @Value("${wechat.pay.privateCertPath}")
    private String privateCertPath;

    private static WxMpService mpService;
    private static WxMaService maService;
    private static WxPayService payService;

    @PostConstruct
    private void init() {
        WxMpDefaultConfigImpl config = new WxMpDefaultConfigImpl();
        config.setAppId(appid);
        config.setSecret(appsecret);
        config.setToken(wxtoken);
        mpService = new WxMpServiceImpl();
        mpService.setWxMpConfigStorage(config);

        WxMaDefaultConfigImpl maConfig = new WxMaDefaultConfigImpl();
        maConfig.setAppid(miniAppid);
        maConfig.setSecret(miniAppsecret);
        maService = new WxMaServiceImpl();
        maService.setWxMaConfig(maConfig);

        WxPayConfig wxPayConfig = new WxPayConfig();
        wxPayConfig.setAppId(appid);
        wxPayConfig.setMchId(mchId);
        wxPayConfig.setKeyPath(keyPath);
        wxPayConfig.setPrivateKeyPath(privateKeyPath);
        wxPayConfig.setPrivateCertPath(privateCertPath);
        wxPayConfig.setApiV3Key(apiV3Key);

        payService = new WxPayServiceImpl();
        payService.setConfig(wxPayConfig);
    }

    public WxMpService getMpService() {
        return mpService;
    }

    public WxMaService getMaService() {
        return maService;
    }

    public WxPayService getPayService() {
        return payService;
    }

}
