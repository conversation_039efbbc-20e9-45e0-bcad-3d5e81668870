package com.xy.uaa.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 
 * @TableName access_token
 */
@TableName(value ="access_token")
@Data
public class AccessToken {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 业务阶段：0访问token接口，1访问了createOrder接口
     */
    private Integer stage;

    /**
     * 渠道：推广商
     */
    private String channel;

    /**
     * 请求来源：app、app2、ks01
     */
    private String source;

    /**
     * promote商
     */
    private String pro;

    /**
     * 平台
     */
    private String platform;

    /**
     * 包名
     */
    private String appPackage;

    /**
     * 应用名
     */
    private String appName;

    /**
     * remoteHost
     */
    private String url;

    /**
     * 页面携带进来的所有参数
     */
    private String ua;

    /**
     * 点位
     */
    private String us;

    /**
     * 过滤方式
     */
    private String ft;

    /**
     * 返回的状态码
     */
    private String code;

    /**
     * 返回的异常码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String msg;

    /**
     * 项目ID
     */
    private String projectid;

    /**
     * 推广ID
     */
    private String promotionid;

    /**
     * 创建日期
     */
    private Date createTime;

    /**
     * 修改日期
     */
    private Date updateTime;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        AccessToken other = (AccessToken) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getPhone() == null ? other.getPhone() == null : this.getPhone().equals(other.getPhone()))
            && (this.getStage() == null ? other.getStage() == null : this.getStage().equals(other.getStage()))
            && (this.getChannel() == null ? other.getChannel() == null : this.getChannel().equals(other.getChannel()))
            && (this.getSource() == null ? other.getSource() == null : this.getSource().equals(other.getSource()))
            && (this.getPro() == null ? other.getPro() == null : this.getPro().equals(other.getPro()))
            && (this.getPlatform() == null ? other.getPlatform() == null : this.getPlatform().equals(other.getPlatform()))
            && (this.getAppPackage() == null ? other.getAppPackage() == null : this.getAppPackage().equals(other.getAppPackage()))
            && (this.getAppName() == null ? other.getAppName() == null : this.getAppName().equals(other.getAppName()))
            && (this.getUrl() == null ? other.getUrl() == null : this.getUrl().equals(other.getUrl()))
            && (this.getUa() == null ? other.getUa() == null : this.getUa().equals(other.getUa()))
            && (this.getUs() == null ? other.getUs() == null : this.getUs().equals(other.getUs()))
            && (this.getFt() == null ? other.getFt() == null : this.getFt().equals(other.getFt()))
            && (this.getCode() == null ? other.getCode() == null : this.getCode().equals(other.getCode()))
            && (this.getErrorCode() == null ? other.getErrorCode() == null : this.getErrorCode().equals(other.getErrorCode()))
            && (this.getMsg() == null ? other.getMsg() == null : this.getMsg().equals(other.getMsg()))
            && (this.getProjectid() == null ? other.getProjectid() == null : this.getProjectid().equals(other.getProjectid()))
            && (this.getPromotionid() == null ? other.getPromotionid() == null : this.getPromotionid().equals(other.getPromotionid()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getPhone() == null) ? 0 : getPhone().hashCode());
        result = prime * result + ((getStage() == null) ? 0 : getStage().hashCode());
        result = prime * result + ((getChannel() == null) ? 0 : getChannel().hashCode());
        result = prime * result + ((getSource() == null) ? 0 : getSource().hashCode());
        result = prime * result + ((getPro() == null) ? 0 : getPro().hashCode());
        result = prime * result + ((getPlatform() == null) ? 0 : getPlatform().hashCode());
        result = prime * result + ((getAppPackage() == null) ? 0 : getAppPackage().hashCode());
        result = prime * result + ((getAppName() == null) ? 0 : getAppName().hashCode());
        result = prime * result + ((getUrl() == null) ? 0 : getUrl().hashCode());
        result = prime * result + ((getUa() == null) ? 0 : getUa().hashCode());
        result = prime * result + ((getUs() == null) ? 0 : getUs().hashCode());
        result = prime * result + ((getFt() == null) ? 0 : getFt().hashCode());
        result = prime * result + ((getCode() == null) ? 0 : getCode().hashCode());
        result = prime * result + ((getErrorCode() == null) ? 0 : getErrorCode().hashCode());
        result = prime * result + ((getMsg() == null) ? 0 : getMsg().hashCode());
        result = prime * result + ((getProjectid() == null) ? 0 : getProjectid().hashCode());
        result = prime * result + ((getPromotionid() == null) ? 0 : getPromotionid().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", phone=").append(phone);
        sb.append(", stage=").append(stage);
        sb.append(", channel=").append(channel);
        sb.append(", source=").append(source);
        sb.append(", pro=").append(pro);
        sb.append(", platform=").append(platform);
        sb.append(", appPackage=").append(appPackage);
        sb.append(", appName=").append(appName);
        sb.append(", url=").append(url);
        sb.append(", ua=").append(ua);
        sb.append(", us=").append(us);
        sb.append(", ft=").append(ft);
        sb.append(", code=").append(code);
        sb.append(", errorCode=").append(errorCode);
        sb.append(", msg=").append(msg);
        sb.append(", projectid=").append(projectid);
        sb.append(", promotionid=").append(promotionid);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }
}