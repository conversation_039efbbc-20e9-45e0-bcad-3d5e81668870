package com.xy.uaa.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 模板分类
 *
 * <AUTHOR>
 * @since 2023-12-29
 */
@TableName(value = "ringtone_category")
@Data
public class RingtoneCategory implements Serializable {

    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 0为根目录
     */
    private Integer pid;
    /**
     * 
     */
    private String name;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 
     */
    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
