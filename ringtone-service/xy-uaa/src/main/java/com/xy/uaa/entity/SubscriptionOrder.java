package com.xy.uaa.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
@TableName(value = "subscription_order")
@Data
public class SubscriptionOrder implements Serializable {

    /**
     * 
     */
    @TableId
    private String orderNumber;
    /**
     * 
     */
    private String phone;
    /**
     * 渠道
     */
    private String channel;
    private String source;
    private String pro;

    /**
     * token
     */
    private String token;

    /**
     * 
     */
    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
