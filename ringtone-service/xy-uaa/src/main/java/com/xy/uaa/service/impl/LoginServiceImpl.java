package com.xy.uaa.service.impl;

import com.xy.base.core.dto.login.LoginDTO;
import com.xy.base.core.enums.UaaTypeEnum;
import com.xy.base.core.exception.AppException;
import com.xy.base.starter.redis.RedisUtils;
import com.xy.base.starter.security.entity.UaaUserDetails;
import com.xy.base.starter.security.util.SecurityUtils;
import com.xy.uaa.dto.ChangePwdDTO;
import com.xy.uaa.service.AdminService;
import com.xy.uaa.service.LoginService;
import com.xy.uaa.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/10/17
 */
@Service
@RequiredArgsConstructor
public class LoginServiceImpl implements LoginService {


    private final AdminService adminService;
    private final UserService userService;

    @Override
    public UaaUserDetails login(LoginDTO dto) {

        UaaTypeEnum uaaType = UaaTypeEnum.of(dto.getType());

        switch (uaaType) {
            case ADMIN:
                return adminService.login(dto);
            case USER:
                return userService.login(dto);
        }

        throw new AppException("错误的用户类型");
    }

    @Override
    public void logout(String token) {
        RedisUtils.del(token);
    }

    @Override
    public boolean changePwd(ChangePwdDTO pwd) {

        UaaUserDetails current = SecurityUtils.current();

        UaaTypeEnum uaaType = UaaTypeEnum.of(current.getType());

        switch (uaaType) {
            case ADMIN:
                return adminService.changePwd(pwd, current.getId());
            case USER:
                return userService.changePwd(pwd, current.getId());
        }

        throw new AppException("错误的用户类型");
    }
}
