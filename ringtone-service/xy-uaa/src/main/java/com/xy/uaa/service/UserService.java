package com.xy.uaa.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xy.base.core.dto.login.LoginDTO;
import com.xy.base.starter.security.pojo.UserUserDetails;
import com.xy.uaa.dto.ChangePwdDTO;
import com.xy.uaa.entity.User;

/**
 * <AUTHOR>
 */
public interface UserService extends IService<User> {

    /**
     * 登录
     *
     * @param dto user
     * @return UaaUserDetails
     */
    UserUserDetails login(LoginDTO dto);

    /**
     * 修改密码
     *
     * @param pwd pwd
     * @return boolean
     */
    boolean changePwd(ChangePwdDTO pwd, Integer id);
}
