package com.xy.uaa.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xy.base.core.constant.CommonConsts;
import com.xy.base.core.exception.AppException;
import com.xy.base.starter.dto.CommonPageQuery;
import com.xy.base.starter.security.util.SecurityUtils;
import com.xy.uaa.entity.Org;
import com.xy.uaa.service.OrgService;
import com.xy.uaa.vo.OrgTreeVO;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/27.
 */
@RestController
@RequestMapping("/org")
@RequiredArgsConstructor
public class OrgController {

    private final OrgService orgService;


    /**
     * 账户列表，需要权限 uaa:org:list
     *
     * @param query query
     * @return Func
     */
    @PreAuthorize("hasAuthority('uaa:org:list')")
    @PostMapping("/orgs")
    public Page<Org> orgs(@RequestBody CommonPageQuery query) {

        Page<Org> page = query.buildPage();
        return orgService.lambdaQuery()
                .likeRight(Org::getPath, SecurityUtils.current().getOrgPath())
                .like(StringUtils.hasText(query.getKeyword()), Org::getName, query.getKeyword())
                .orderByDesc(Org::getId)
                .page(page);
    }

    @PreAuthorize("hasAuthority('uaa:org:list')")
    @GetMapping("/all")
    public List<Org> all() {
        return orgService.lambdaQuery().list();
    }

    /**
     * 增加或修改组织，需要权限 uaa:org:save
     *
     * @param org org
     * @return Admin
     */
    @PreAuthorize("hasAuthority('uaa:org:save')")
    @PostMapping("/save")
    @Transactional(rollbackFor = RuntimeException.class)
    public Org save(@RequestBody @Valid Org org) {
//        AppException.tnt((org.getId() != null && org.getId() < 2) || org.getPid() < 0, "内置组织不能改动");

        Org pOrg;
        if (org.getPid().equals(CommonConsts.ZERO)) {
            pOrg = new Org();
            pOrg.setPath("/");
        } else {
            pOrg = orgService.getById(org.getPid());
        }
        AppException.tnt(pOrg == null, "参数错误");

        if (org.getId() == null) {
            org.setCreatorId(SecurityUtils.getId());
            org.setCreateTime(LocalDateTime.now());
            org.setPath("-");
        }

        orgService.saveOrUpdate(org);
        org.setPath(pOrg.getPath() + org.getId() + "/");
        orgService.updateById(org);
        return org;
    }


    /**
     * 删除组织，需要权限 uaa:org:del
     *
     * @param id id
     * @return Admin
     */
    @PreAuthorize("hasAuthority('uaa:org:del')")
    @GetMapping("/del/{id}")
    public boolean del(@PathVariable("id") Integer id) {

        // 如果又子部门或者当前部门又人员，禁止删除
        if (orgService.lambdaQuery().eq(Org::getPid, id).exists()) {
            throw new AppException("当前部门下有子部门，不能删除");
        }


        return orgService.removeById(id);
    }


    /**
     * 获取组织树形结构
     *
     * @return Admin
     */
    @GetMapping("/orgTree")
    public List<OrgTreeVO> orgTree() {
        String path = SecurityUtils.current().getOrgPath();
        return orgService.orgTree(path);
    }

}
