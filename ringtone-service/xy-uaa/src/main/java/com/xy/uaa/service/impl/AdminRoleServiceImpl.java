package com.xy.uaa.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.uaa.entity.AdminRole;
import com.xy.uaa.mapper.AdminRoleMapper;
import com.xy.uaa.service.AdminRoleService;
import com.xy.uaa.vo.AdminRoleVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class AdminRoleServiceImpl extends ServiceImpl<AdminRoleMapper, AdminRole>
        implements AdminRoleService {


    @Override
    public List<AdminRoleVO> listWithRole(Wrapper<AdminRoleVO> wrapper) {
        return baseMapper.listWithRole(wrapper);

    }
}




