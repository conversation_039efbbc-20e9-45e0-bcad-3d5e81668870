package com.xy.uaa.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xy.base.core.dto.login.LoginDTO;
import com.xy.base.starter.dto.CommonPageQuery;
import com.xy.base.starter.security.pojo.AdminUserDetails;
import com.xy.uaa.dto.ChangePwdDTO;
import com.xy.uaa.entity.Admin;
import com.xy.uaa.vo.AdminVO;

/**
 * <AUTHOR>
 */
public interface AdminService extends IService<Admin> {

    /**
     * 登录
     *
     * @param dto dto
     * @return UaaUserDetails
     */
    AdminUserDetails login(LoginDTO dto);

    /**
     * 查询vo
     *
     * @param query   query
     * @param orgPath orgPath
     * @return AdminVO
     */
    Page<AdminVO> listWithRole(CommonPageQuery query, String orgPath);

    /**
     * 添加用户
     *
     * @param admin admin
     * @return admin
     */
    Admin addOrUpdate(AdminVO admin);

    /**
     * 修改密码
     *
     * @param pwd pwd
     * @return boolean
     */
    boolean changePwd(ChangePwdDTO pwd, Integer id);
}
