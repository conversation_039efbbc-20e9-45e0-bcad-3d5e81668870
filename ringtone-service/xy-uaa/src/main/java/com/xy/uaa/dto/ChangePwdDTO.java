package com.xy.uaa.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2021/1/29
 */
@Data
public class ChangePwdDTO {

    @Length(max = 100, min = 6, message = "旧密码错误")
    @NotNull
    private String oldPwd;

    @Length(max = 100, min = 6, message = "密码不能少于6位")
    @NotNull
    private String newPwd;
}
