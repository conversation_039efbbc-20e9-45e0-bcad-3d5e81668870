package com.xy.uaa.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.base.core.constant.CommonConsts;
import com.xy.base.core.dto.login.LoginDTO;
import com.xy.base.core.enums.ExceptionResultEnum;
import com.xy.base.core.enums.UaaTypeEnum;
import com.xy.base.core.exception.AppException;
import com.xy.base.starter.redis.RedisUtils;
import com.xy.base.starter.security.pojo.UserUserDetails;
import com.xy.base.starter.security.util.SecurityUtils;
import com.xy.uaa.dto.ChangePwdDTO;
import com.xy.uaa.entity.User;
import com.xy.uaa.mapper.UserMapper;
import com.xy.uaa.service.UserService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User>
        implements UserService {

    @Override
    public UserUserDetails login(LoginDTO dto) {

        User user = lambdaQuery().eq(User::getOpenid, dto.getOpenid()).one();
        AppException.tnt(user == null, ExceptionResultEnum.LOGIN_NONUSER);

        UserUserDetails details = new UserUserDetails();
        String token = SecurityUtils.buildToken(user.getId(), UaaTypeEnum.USER);
        details.setId(user.getId());
        details.setToken(token);
        details.setOpenid(user.getOpenid());
        details.setPhone(user.getPhone());
        details.setType(UaaTypeEnum.USER.getValue());
        RedisUtils.set(details.getToken(), details, CommonConsts.EX_TIME);

        return details;
    }

    @Override
    public boolean changePwd(ChangePwdDTO pwd, Integer id) {
        User user = getById(id);
        AppException.tnt(!new BCryptPasswordEncoder().matches(pwd.getOldPwd(), user.getPassword()), ExceptionResultEnum.LOGIN_PASSWORD_ERROR);

        return lambdaUpdate()
                .eq(User::getId, id)
                .set(User::getPassword, new BCryptPasswordEncoder().encode(pwd.getNewPwd()))
                .update();
    }
}




