package com.xy.uaa.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xy.base.starter.dto.CommonPageQuery;
import com.xy.uaa.entity.Role;
import com.xy.uaa.vo.RoleVO;

/**
 * <AUTHOR>
 */
public interface RoleService extends IService<Role> {

    /**
     * 保存角色数据
     *
     * @param role role
     * @return Role
     */
    Role saveData(RoleVO role);

    /**
     * 删除角色
     * @param rolId rolId
     * @return Boolean
     */
    Boolean roleDel(Integer rolId);

    /**
     * 查询vo
     * @param query query
     * @return return
     */
    IPage<RoleVO> listVO(CommonPageQuery query);
}
