package com.xy.uaa.config;

import com.xy.base.starter.security.filter.RedisSecurityFilter;
import com.xy.base.starter.security.handler.SimpleAccessDeniedHandler;
import com.xy.base.starter.security.handler.SimpleAuthenticationEntryPoint;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * EnableGlobalMethodSecurity 开启security方法注解
 *
 * <AUTHOR>
 * @since 2020/7/27
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class MyWebSecurityConfig {

    @Bean
    public RedisSecurityFilter authenticationTokenFilterBean() {
        return new RedisSecurityFilter();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity httpSecurity) throws Exception {

        return httpSecurity
                // 关闭csrf
                .csrf(AbstractHttpConfigurer::disable)
                .exceptionHandling(exp -> exp
                        .accessDeniedHandler(new SimpleAccessDeniedHandler())
                        .authenticationEntryPoint(new SimpleAuthenticationEntryPoint()))
                // 基于token，所以不需要session
                .sessionManagement(s -> s.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                // 需要放行的接口，下一版本方法名称有所调整antMatchers->requestMatchers
                .authorizeHttpRequests(authorize -> authorize
                        .antMatchers("/login/**", "/wechat/**", "/user/**",
                                "/ringtone/**", "/order/notify", "/order/createSubscription","/order/createOrder","/order/querySubscription/**").permitAll()
                        .anyRequest().authenticated())
                //使用自定义的 Token过滤器 验证请求的Token是否合法
                .addFilterBefore(authenticationTokenFilterBean(), UsernamePasswordAuthenticationFilter.class)
                // 禁用缓存
                .headers(h -> h.cacheControl(HeadersConfigurer.CacheControlConfig::disable))
                .build();
    }
}
