package com.xy.uaa.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 组织结构
 *
 * <AUTHOR>
 * @TableName uaa_org
 */
@TableName(value = "uaa_org")
@Data
public class Org implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 顶级为0
     */
    @NotNull
    private Integer pid;
    /**
     * 完整路径
     */
    private String path;
    /**
     * 组织名称
     */
    @NotNull
    private String name;
    /**
     * 备注
     */
    private String note;

    private Integer creatorId;
    /**
     *
     */
    private LocalDateTime createTime;
}