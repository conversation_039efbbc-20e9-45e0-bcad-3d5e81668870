package com.xy.uaa.service;


import com.xy.base.core.dto.login.LoginDTO;
import com.xy.base.starter.security.entity.UaaUserDetails;
import com.xy.uaa.dto.ChangePwdDTO;

/**
 * <AUTHOR>
 * @since 2022/10/17
 */
public interface LoginService {

    /**
     * 统一登录
     *
     * @param dto dto
     * @return UaaUserDetails
     */
    UaaUserDetails login(LoginDTO dto);

    /**
     * 退出登录
     *
     * @param token token
     */
    void logout(String token);

    /**
     * 修改密码
     *
     * @param pwd pwd
     * @return boolean
     */
    boolean changePwd(ChangePwdDTO pwd);
}
