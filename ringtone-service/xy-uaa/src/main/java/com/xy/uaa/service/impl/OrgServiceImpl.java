package com.xy.uaa.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.uaa.entity.Org;
import com.xy.uaa.mapper.OrgMapper;
import com.xy.uaa.service.OrgService;
import com.xy.uaa.vo.OrgTreeVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class OrgServiceImpl extends ServiceImpl<OrgMapper, Org>
        implements OrgService {

    @Override
    public List<OrgTreeVO> orgTree(String path) {
        List<Org> funcs = lambdaQuery().likeRight(Org::getPath, path).orderByAsc(Org::getId).list();
        return buildTree(funcs, 0);
    }

    private List<OrgTreeVO> buildTree(List<Org> funcs, Integer parentId) {
        List<OrgTreeVO> trees = null;
        for (Org org : funcs) {
            if (org.getPid().equals(parentId)) {
                OrgTreeVO orgTree = new OrgTreeVO();
                BeanUtils.copyProperties(org, orgTree);
                orgTree.setChildren(buildTree(funcs, org.getId()));
                if (null == trees) {
                    trees = new ArrayList<>();
                }
                trees.add(orgTree);
            }
        }
        return trees;
    }
}




