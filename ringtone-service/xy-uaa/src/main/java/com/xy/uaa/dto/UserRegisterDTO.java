package com.xy.uaa.dto;

import cn.hutool.core.lang.RegexPool;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @since 2023/11/23 11:53
 */
@Data
public class UserRegisterDTO {

    @NotBlank(message = "phone不能为空")
    @Pattern(regexp = RegexPool.MOBILE)
    private String phone;

    private String validateCode;

    @NotBlank(message = "openid不能为空")
    private String openid;
}
