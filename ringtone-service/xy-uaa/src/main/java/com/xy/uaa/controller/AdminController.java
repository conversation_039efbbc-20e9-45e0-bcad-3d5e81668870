package com.xy.uaa.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xy.base.core.exception.AppException;
import com.xy.base.starter.dto.CommonPageQuery;
import com.xy.base.starter.security.util.SecurityUtils;
import com.xy.uaa.entity.Admin;
import com.xy.uaa.service.AdminService;
import com.xy.uaa.vo.AdminVO;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @since 2021/12/23.
 */
@RestController
@RequestMapping("/admin")
@RequiredArgsConstructor
public class AdminController {

    private final AdminService adminService;


    /**
     * 账户列表，需要权限 uaa:admin:list
     *
     * @param query query
     * @return Func
     */
    @PreAuthorize("hasAuthority('uaa:admin:list')")
    @PostMapping("/admins")
    public Page<Admin> admins(@RequestBody CommonPageQuery query) {

        // 默认只能看见自己创建的角色
        Page<Admin> page = query.buildPage();
        return adminService.lambdaQuery()
                .like(StringUtils.hasText(query.getKeyword()), Admin::getUsername, query.getKeyword())
                .orderByDesc(Admin::getId)
                .page(page);
    }

    /**
     * 用户列表，需要权限 uaa:admin:list
     *
     * @param query query
     * @return AdminVO
     */
    @PreAuthorize("hasAuthority('uaa:admin:list')")
    @PostMapping("/list")
    public Page<AdminVO> list(@RequestBody CommonPageQuery query) {
        return adminService.listWithRole(query, SecurityUtils.current().getOrgPath());
    }

    /**
     * 添加用户，需要权限 uaa:admin:save
     *
     * @param admin admin
     * @return Admin
     */
    @PreAuthorize("hasAuthority('uaa:admin:save')")
    @PostMapping("/save")
    @Transactional(rollbackFor = RuntimeException.class)
    public Admin save(@RequestBody @Valid AdminVO admin) {

        AppException.tnt(admin.getRoles() == null || admin.getRoles().size() < 1, "用户必须设置角色");
        return adminService.addOrUpdate(admin);
    }

    /**
     * 删除用户
     *
     * @param id id
     * @return boolean
     */
    @GetMapping("delAdmin/{id}")
    public boolean delAdmin(@PathVariable("id") Integer id) {
        return adminService.removeById(id);
    }


}
