package com.xy.uaa.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 铃音视频模板
 *
 * <AUTHOR>
 * @since 2023-12-29
 */
@TableName(value = "ringtone_template")
@Data
public class RingtoneTemplate implements Serializable {

    /**
     * 模板id
     */
    @TableId
    private String id;

    /**
     * 视频类别，1直接设置的铃音，2通过咪咕url合成的铃音，3需给出联系方式人工干预的铃音
     */
    private Integer type;
    /**
     * 一级分类id，0表示所有
     */
    private Integer category;
    /**
     * 二级分类id
     */
    private String categories;
    /**
     * 模板名称
     */
    private String name;
    /**
     * 使用人数
     */
    private Integer used;
    /**
     * 图片封面地址
     */
    private String cover;
    /**
     * 视频播放地址
     */
    private String video;
    /**
     * 咪咕预览地址
     */
    private String miguUrl;
    /**
     * 版权证书地址
     */
    private String copyright;
    /**
     * 价格（分）
     */
    private Integer price;
    /**
     * 是否默认铃音
     */
    private Integer isDefault;
    /**
     * 排序，从小到大
     */
    private Integer sort;
    /**
     * 
     */
    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
