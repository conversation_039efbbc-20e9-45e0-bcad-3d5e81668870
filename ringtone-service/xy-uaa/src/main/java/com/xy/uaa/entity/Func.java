package com.xy.uaa.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @TableName uaa_func
 */
@TableName(value = "uaa_func")
@Data
public class Func implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 上级id，0表示根目录
     */
    private Integer parentId;

    /**
     * 功能名称
     */
    private String name;

    /**
     * 功能代码如tc:x:y，其中x可选值page,
     */
    private String code;

    /**
     * 类型：0路由，1页面，2功能，下列属性请从前缀推测用途
     */
    private Integer type;

    /**
     * 如果是路由，记录页面值
     */
    private String routePath;

    /**
     * 路由名称，通常为英文
     */
    private String routeName;

    /**
     * 文件路径（默认填充@/views/，所以无需输入）
     */
    private String routeFile;

    /**
     * 路由排序asc
     */
    private Integer routeOrder;

    /**
     * 是否显示
     */
    private Boolean menuDisplay;

    /**
     * 图标名称
     */
    private String menuIcon;

    /**
     * 是否缓存
     */
    private Boolean keepAlive;

    /**
     *
     */
    private Integer creatorId;


    /**
     *
     */
    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}