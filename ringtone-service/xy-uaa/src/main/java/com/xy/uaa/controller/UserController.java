package com.xy.uaa.controller;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xy.base.core.dto.login.LoginDTO;
import com.xy.base.core.exception.AppException;
import com.xy.base.starter.dto.CommonPageQuery;
import com.xy.base.starter.redis.RedisUtils;
import com.xy.base.starter.security.pojo.UserUserDetails;
import com.xy.uaa.dto.SmsDTO;
import com.xy.uaa.dto.UserRegisterDTO;
import com.xy.uaa.entity.User;
import com.xy.uaa.service.UserService;
import com.xy.uaa.util.SmsUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2021/12/23.
 */
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;


    /**
     * 用户列表，需要权限 uaa:user:list
     *
     * @param query query
     * @return Func
     */
    @PreAuthorize("hasAuthority('uaa:user:list')")
    @PostMapping("/list")
    public IPage<User> list(@RequestBody CommonPageQuery query) {
        Page<User> page = query.buildPage();
        return userService.lambdaQuery()
                .or(StringUtils.hasText(query.getKeyword()),
                        w -> w.like(User::getUsername, query.getKeyword()).or().like(User::getPhone, query.getKeyword()))
                .orderByDesc(User::getId)
                .page(page);
    }


    /**
     * 发送短信
     */
    @PostMapping("/sendSms")
    public boolean sendSms(@RequestBody @Valid UserRegisterDTO sendDto) throws Exception {

        String key = SmsDTO.getRedisKey(sendDto.getOpenid());

        SmsDTO entity = RedisUtils.getForEntity(key, SmsDTO.class);

        if (entity == null || entity.getCreateTime().isBefore(LocalDateTime.now().plusMinutes(1))) {
            String validateCode = RandomUtil.randomNumbers(6);
            System.out.println(validateCode);

            SmsDTO sms = new SmsDTO();
            sms.setPhone(sendDto.getPhone());
            sms.setCreateTime(LocalDateTime.now());
            sms.setValidateCode(validateCode);

            SmsUtil.send(sendDto.getPhone(), "SMS_464120680", "{\"code\":\"" + validateCode + "\"}");
            // 截止4月初剩余700多条，用完后切换使用下方代码
            //SmsUtil.send(sendDto.getPhone(), "SMS_465423482", "{\"code\":\"" + validateCode + "\"}");
            RedisUtils.set(key, sms, 5 * 60);

            return true;
        } else {
            throw new AppException("短信发送过于频繁");
        }
    }

    /**
     * 发送短信
     */
    @PostMapping("/register")
    public UserUserDetails register(@RequestBody @Valid UserRegisterDTO registerDto) {
        // 验证短信验证码是否正确

        AppException.tnt(!StringUtils.hasText(registerDto.getValidateCode()), "验证码不正确");

        SmsDTO entity = RedisUtils.getForEntity(SmsDTO.getRedisKey(registerDto.getOpenid()), SmsDTO.class);
        AppException.tnt(entity == null || !entity.getValidateCode().equals(registerDto.getValidateCode()), "验证码不正确");
//        User user = userService.lambdaQuery().eq(User::getOpenid, registerDto.getOpenid()).one();
        User user = userService.lambdaQuery().eq(User::getPhone, registerDto.getPhone()).one();
        if (user == null) {
            user = new User();
            user.setPhone(registerDto.getPhone());
            user.setOpenid(registerDto.getOpenid());
            user.setCreateTime(LocalDateTime.now());
        } else {
            user.setOpenid(registerDto.getOpenid());
        }

        userService.saveOrUpdate(user);

        LoginDTO dto = new LoginDTO();
        dto.setOpenid(user.getOpenid());
        return userService.login(dto);
    }
}
