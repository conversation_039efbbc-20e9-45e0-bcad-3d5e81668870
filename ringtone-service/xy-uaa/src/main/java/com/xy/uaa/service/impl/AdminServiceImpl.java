package com.xy.uaa.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.base.core.constant.CommonConsts;
import com.xy.base.core.dto.login.LoginDTO;
import com.xy.base.core.enums.ExceptionResultEnum;
import com.xy.base.core.enums.UaaTypeEnum;
import com.xy.base.core.exception.AppException;
import com.xy.base.starter.dto.CommonPageQuery;
import com.xy.base.starter.redis.RedisUtils;
import com.xy.base.starter.security.entity.UaaAuthority;
import com.xy.base.starter.security.pojo.AdminUserDetails;
import com.xy.base.starter.security.util.SecurityUtils;
import com.xy.uaa.dto.ChangePwdDTO;
import com.xy.uaa.entity.*;
import com.xy.uaa.mapper.AdminMapper;
import com.xy.uaa.service.*;
import com.xy.uaa.vo.AdminRoleVO;
import com.xy.uaa.vo.AdminVO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class AdminServiceImpl extends ServiceImpl<AdminMapper, Admin>
        implements AdminService {

    private final FuncService funcService;
    private final AdminRoleService adminRoleService;
    private final RoleFuncService roleFuncService;
    private final OrgService orgService;

    @Override
    public AdminUserDetails login(LoginDTO dto) {


        Admin admin = lambdaQuery()
                .eq(Admin::getUsername, dto.getUsername())
                .eq(Admin::getStatus, CommonConsts.ONE)
                .one();

        AppException.tnt(admin == null, ExceptionResultEnum.LOGIN_NONUSER);
        AppException.tnt(!new BCryptPasswordEncoder().matches(dto.getPassword(), admin.getPassword()), ExceptionResultEnum.LOGIN_PASSWORD_ERROR);

        AdminUserDetails userDetails = new AdminUserDetails();

        String token = SecurityUtils.buildToken(admin.getId(), UaaTypeEnum.ADMIN);
        userDetails.setId(admin.getId());
        userDetails.setUsername(admin.getUsername());
        userDetails.setToken(token);
        userDetails.setOrgId(admin.getOrgId());
        userDetails.setOrgPath(admin.getOrgPath());
        userDetails.setRealName(admin.getRealName());
        userDetails.setType(UaaTypeEnum.ADMIN.getValue());

        // 给用户赋权限
        List<Func> funcs = null;
        if (CommonConsts.SUPER_ORG.equals(admin.getOrgPath())) {
            funcs = funcService.lambdaQuery().orderByAsc(Func::getRouteOrder).list();
        } else {
            List<AdminRole> roles = adminRoleService.lambdaQuery().eq(AdminRole::getAdminId, admin.getId()).list();

            Set<Integer> roleIds = new HashSet<>();
            roles.forEach(r -> roleIds.add(r.getRoleId()));
            if (roleIds.size() > 0) {
                Set<Integer> funcIds = new HashSet<>();
                List<RoleFunc> roleFuncs = roleFuncService.lambdaQuery().in(RoleFunc::getRoleId, roleIds).list();
                roleFuncs.forEach(f -> funcIds.add(f.getFuncId()));

                if (funcIds.size() > 0) {
                    funcs = funcService.lambdaQuery().in(Func::getId, funcIds).orderByAsc(Func::getRouteOrder).list();
                }
            }
        }

        List<UaaAuthority> authorities = new ArrayList<>();

        // 开始根据funcs构建权限
        if (funcs != null && funcs.size() > 0) {
            funcs.forEach(f -> {
                UaaAuthority authority = new UaaAuthority();
                BeanUtils.copyProperties(f, authority);
                authority.setAuthority(f.getCode());
                authorities.add(authority);
            });
        }

        userDetails.setAuthorities(authorities);
        RedisUtils.set(userDetails.getToken(), userDetails, CommonConsts.EX_TIME);

        return userDetails;
    }

    @Override
    public Page<AdminVO> listWithRole(CommonPageQuery query, String orgPath) {
        Page<Admin> page = query.buildPage();

        lambdaQuery()
                .likeRight(Admin::getOrgPath, orgPath)
                .and(StringUtils.hasText(query.getKeyword()), i -> i.like(Admin::getUsername, query.getKeyword())
                        .or().like(Admin::getRealName, query.getKeyword()))
                .orderByDesc(Admin::getId)
                .page(page);

        Set<Integer> adminIds = new HashSet<>();
        List<AdminRoleVO> adminRoles = new ArrayList<>();
        page.getRecords().forEach(admin -> adminIds.add(admin.getId()));
        if (adminIds.size() > 0) {
            LambdaQueryWrapper<AdminRoleVO> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(AdminRole::getAdminId, adminIds);
            adminRoles = adminRoleService.listWithRole(wrapper);
        }

        List<AdminVO> admins = new ArrayList<>();
        List<AdminRoleVO> finalAdminRoles = adminRoles;
        page.getRecords().forEach(admin -> {
            AdminVO adminVO = new AdminVO();
            BeanUtils.copyProperties(admin, adminVO);
            adminVO.setRoles(new ArrayList<>());
            admins.add(adminVO);
            for (AdminRoleVO adminRole : finalAdminRoles) {
                if (adminRole.getAdminId().equals(admin.getId())) {
                    Role role = new Role();
                    role.setId(adminRole.getRoleId());
                    role.setName(adminRole.getName());
                    adminVO.getRoles().add(role);
                }
            }
        });

        Page<AdminVO> returnPage = new Page<>();
        returnPage.setSize(page.getSize());
        returnPage.setPages(page.getPages());
        returnPage.setCurrent(page.getCurrent());
        returnPage.setTotal(page.getTotal());
        returnPage.setRecords(admins);

        return returnPage;
    }

    @Override
    public Admin addOrUpdate(AdminVO admin) {

        Org org = orgService.getById(admin.getOrgId());

        AppException.tnt(org == null, "组织数据错误");

        if (admin.getId() != null) {
            adminRoleService.lambdaUpdate().eq(AdminRole::getAdminId, admin.getId()).remove();
        } else {
            admin.setPassword(new BCryptPasswordEncoder().encode("123456"));
            admin.setCreatorId(SecurityUtils.getId());
            admin.setCreateTime(LocalDateTime.now());
        }
        admin.setOrgId(org.getId());
        admin.setOrgPath(org.getPath());
        saveOrUpdate(admin);

        List<AdminRole> adminRoles = new ArrayList<>();
        admin.getRoles().forEach(role -> {
            AdminRole adminRole = new AdminRole();
            adminRole.setRoleId(role.getId());
            adminRole.setCreateTime(LocalDateTime.now());
            adminRole.setAdminId(admin.getId());
            adminRoles.add(adminRole);
        });
        adminRoleService.saveBatch(adminRoles);

        return admin;
    }

    @Override
    public boolean changePwd(ChangePwdDTO pwd, Integer id) {
        Admin admin = getById(id);
        AppException.tnt(!new BCryptPasswordEncoder().matches(pwd.getOldPwd(), admin.getPassword()), ExceptionResultEnum.LOGIN_PASSWORD_ERROR);

        return lambdaUpdate()
                .eq(Admin::getId, id)
                .set(Admin::getPassword, new BCryptPasswordEncoder().encode(pwd.getNewPwd()))
                .update();
    }
}




