package com.xy.uaa.controller;

import com.xy.base.core.annotation.DirectOutput;
import com.xy.base.core.response.Result;
import com.xy.uaa.component.WechatServiceComponent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 处理微信公众号相关 如授权 获取openid等
 *
 * <AUTHOR>
 * @since 2021/12/22.
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/wechat")
public class WechatController {

    private final WechatServiceComponent wechatServiceComponent;

    @Value("${wechat.pub.authUrl}")
    private String authUrl;


    /**
     * @ignore 微信公众号回调认证
     */
    @GetMapping("message")
    @DirectOutput
    public String configAccess(String signature, String timestamp, String nonce, String echostr) {
        // 校验签名
        if (wechatServiceComponent.getMaService().checkSignature(timestamp, nonce, signature)) {
            return echostr;
        }
        // 校验失败
        return null;
    }

    /**
     * 获取网页授权url
     */
    @GetMapping("authUrl")
    public Result<String> authUrl() {
        String url = wechatServiceComponent
                .getMpService()
                .getOAuth2Service().buildAuthorizationUrl(authUrl, WxConsts.OAuth2Scope.SNSAPI_BASE, null);

        return Result.success(url);
    }

    /**
     * 获取公众号用户openid
     */
    @GetMapping("/mpOpenid/{code}")
    public Result<String> mpOpenid(@PathVariable("code") String code) throws WxErrorException {
        WxOAuth2AccessToken accessToken = wechatServiceComponent.getMpService().getOAuth2Service().getAccessToken(code);
        return Result.success(accessToken.getOpenId());
    }

    /**
     * 获取小程序用户openid
     */
    @GetMapping("/openid/{code}")
    public Result<String> openid(@PathVariable("code") String code) throws WxErrorException {
        String openid = wechatServiceComponent.getMaService().getUserService()
                .getSessionInfo(code).getOpenid();

        return Result.success(openid);
    }
}
