package com.xy.uaa.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

@Data
@Accessors(chain = true)
@SuperBuilder
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "dliang_record")
public class DliangRecord implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @NotNull(message = "不能为null")
    private Long id;

    /**
     * 渠道：推广商
     */
    @TableField(value = "channel")
    @Size(max = 55,message = "渠道：推广商最大长度要小于 55")
    private String channel;

    /**
     * 请求来源：app、app2、ks01
     */
    @TableField(value = "`source`")
    @Size(max = 55,message = "请求来源：app、app2、ks01最大长度要小于 55")
    private String source;

    /**
     * 手机号
     */
    @TableField(value = "phone")
    @Size(max = 11,message = "手机号最大长度要小于 11")
    private String phone;

    /**
     * promote商
     */
    @TableField(value = "pro")
    @Size(max = 55,message = "promote商最大长度要小于 55")
    private String pro;

    /**
     * 提醒范围
     */
    @TableField(value = "notice")
    @Size(max = 55,message = "提醒范围最大长度要小于 55")
    private String notice;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}