package com.xy.uaa.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.uaa.entity.AccessToken;
import com.xy.uaa.service.AccessTokenService;
import com.xy.uaa.mapper.AccessTokenMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【access_token】的数据库操作Service实现
* @createDate 2025-05-21 10:27:26
*/
@Service
public class AccessTokenServiceImpl extends ServiceImpl<AccessTokenMapper, AccessToken>
    implements AccessTokenService{

}




