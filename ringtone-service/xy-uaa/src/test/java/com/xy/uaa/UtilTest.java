package com.xy.uaa;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.xy.uaa.util.SmsUtil;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @since 2023/11/25
 */
public class UtilTest {


    @Test
    public void testTime() throws Exception {

        SmsUtil.send("13880899948", "SMS_464120680", "{\"code\":\"123456\"}");
        // SmsUtil.send("13880899948", "SMS_465423482", "{\"code\":\"123456\"}");


        String time = "2018-06-08T10:34:56+08:00";

        DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;

        LocalDateTime parse = LocalDateTime.parse(time, formatter);


        JSONObject json = JSONUtil.parseObj("{\"s\":1}");

        json.put("a","2");


        System.out.println(parse);
    }
}
