FROM frolvlad/alpine-java:jdk8-slim
LABEL maintainer="nto"
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
VOLUME /tmp
VOLUME /logs
VOLUME /config
ARG PROFILE=dev-test
ARG JAR_FILE
COPY target/xy-uaa-1.0.0.jar /app.jar

ARG PROFILE
ARG MARK=SY
ENV env=$PROFILE
ENV m=$MARK

RUN echo "java -agentlib:jdwp=transport=dt_socket,server=y,suspend=y,address=5005 -Dmark.name=${m} -server -XX:MetaspaceSize=32m -XX:MaxMetaspaceSize=256m -Xms256m -Xmx256m -Xmn128m -Xss256k -XX:+UseParNewGC -XX:+UseConcMarkSweepGC -jar /app.jar --spring.profiles.active=${env} --spring.config.location=classpath:/,file:/config/" > /run.sh && chmod 777 /run.sh
ENTRYPOINT ["/bin/sh","/run.sh"]
