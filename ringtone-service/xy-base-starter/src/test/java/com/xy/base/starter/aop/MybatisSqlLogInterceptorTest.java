package com.xy.base.starter.aop;

import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;
import java.sql.Statement;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * MybatisSqlLogInterceptor 测试类
 * 用于验证SQL日志解析的修复
 *
 * <AUTHOR>
 * @since 2025/08/04
 */
class MybatisSqlLogInterceptorTest {

    @Test
    void testGetSql_MySQL_WithValidFormat() throws SQLException {
        // 模拟正常的MySQL Statement toString格式
        Statement statement = mock(Statement.class);
        Connection connection = mock(Connection.class);
        DatabaseMetaData metaData = mock(DatabaseMetaData.class);
        StatementHandler sh = mock(StatementHandler.class);
        
        when(statement.getConnection()).thenReturn(connection);
        when(connection.getMetaData()).thenReturn(metaData);
        when(metaData.getDatabaseProductName()).thenReturn("MySQL");
        when(statement.toString()).thenReturn("HikariProxyPreparedStatement@1321445577 wrapping com.mysql.cj.jdbc.ClientPreparedStatement: SELECT * FROM user WHERE id = 1");
        
        String result = MybatisSqlLogInterceptor.getSql(statement, 1000L, "100", sh);
        
        assertTrue(result.contains("SELECT * FROM user WHERE id = 1"));
        assertFalse(result.contains("HikariProxyPreparedStatement"));
        assertFalse(result.contains("@"));
    }

    @Test
    void testGetSql_MySQL_WithInvalidFormat() throws SQLException {
        // 模拟异常的MySQL Statement toString格式（没有冒号）
        Statement statement = mock(Statement.class);
        Connection connection = mock(Connection.class);
        DatabaseMetaData metaData = mock(DatabaseMetaData.class);
        StatementHandler sh = mock(StatementHandler.class);
        BoundSql boundSql = mock(BoundSql.class);
        
        when(statement.getConnection()).thenReturn(connection);
        when(connection.getMetaData()).thenReturn(metaData);
        when(metaData.getDatabaseProductName()).thenReturn("MySQL");
        when(statement.toString()).thenReturn("org.apache.ibatis.logging.jdbc.PreparedStatementLogger@3827369e");
        when(sh.getBoundSql()).thenReturn(boundSql);
        when(boundSql.getSql()).thenReturn("SELECT * FROM user WHERE id = ?");
        
        String result = MybatisSqlLogInterceptor.getSql(statement, 1000L, "100", sh);
        
        assertTrue(result.contains("SELECT * FROM user WHERE id = ?"));
        assertFalse(result.contains("PreparedStatementLogger"));
        assertFalse(result.contains("@"));
    }

    @Test
    void testGetSql_MySQL_WithColonButNoSpace() throws SQLException {
        // 模拟包含冒号但格式不标准的情况
        Statement statement = mock(Statement.class);
        Connection connection = mock(Connection.class);
        DatabaseMetaData metaData = mock(DatabaseMetaData.class);
        StatementHandler sh = mock(StatementHandler.class);
        BoundSql boundSql = mock(BoundSql.class);
        
        when(statement.getConnection()).thenReturn(connection);
        when(connection.getMetaData()).thenReturn(metaData);
        when(metaData.getDatabaseProductName()).thenReturn("MySQL");
        when(statement.toString()).thenReturn("SomeStatement:SELECT * FROM user");
        when(sh.getBoundSql()).thenReturn(boundSql);
        when(boundSql.getSql()).thenReturn("SELECT * FROM user WHERE id = ?");
        
        String result = MybatisSqlLogInterceptor.getSql(statement, 1000L, "100", sh);
        
        // 应该使用原始SQL，因为没有找到": "格式
        assertTrue(result.contains("SELECT * FROM user WHERE id = ?"));
    }

    @Test
    void testGetSql_PostgreSQL() throws SQLException {
        // 测试PostgreSQL数据库
        Statement statement = mock(Statement.class);
        Connection connection = mock(Connection.class);
        DatabaseMetaData metaData = mock(DatabaseMetaData.class);
        StatementHandler sh = mock(StatementHandler.class);
        
        when(statement.getConnection()).thenReturn(connection);
        when(connection.getMetaData()).thenReturn(metaData);
        when(metaData.getDatabaseProductName()).thenReturn("PostgreSQL");
        when(statement.toString()).thenReturn("PostgreSQL Statement: SELECT * FROM user");
        
        String result = MybatisSqlLogInterceptor.getSql(statement, 1000L, "100", sh);
        
        assertTrue(result.contains("PostgreSQL Statement: SELECT * FROM user"));
    }

    @Test
    void testGetSql_OtherDatabase() throws SQLException {
        // 测试其他数据库
        Statement statement = mock(Statement.class);
        Connection connection = mock(Connection.class);
        DatabaseMetaData metaData = mock(DatabaseMetaData.class);
        StatementHandler sh = mock(StatementHandler.class);
        BoundSql boundSql = mock(BoundSql.class);
        
        when(statement.getConnection()).thenReturn(connection);
        when(connection.getMetaData()).thenReturn(metaData);
        when(metaData.getDatabaseProductName()).thenReturn("Oracle");
        when(sh.getBoundSql()).thenReturn(boundSql);
        when(boundSql.getSql()).thenReturn("SELECT * FROM user WHERE id = ?");
        
        String result = MybatisSqlLogInterceptor.getSql(statement, 1000L, "100", sh);
        
        assertTrue(result.contains("SELECT * FROM user WHERE id = ?"));
    }

    @Test
    void testGetSql_FormatOutput() throws SQLException {
        // 测试输出格式
        Statement statement = mock(Statement.class);
        Connection connection = mock(Connection.class);
        DatabaseMetaData metaData = mock(DatabaseMetaData.class);
        StatementHandler sh = mock(StatementHandler.class);
        
        when(statement.getConnection()).thenReturn(connection);
        when(connection.getMetaData()).thenReturn(metaData);
        when(metaData.getDatabaseProductName()).thenReturn("MySQL");
        when(statement.toString()).thenReturn("Statement: SELECT   *   FROM   user   WHERE   id   =   1");
        
        String result = MybatisSqlLogInterceptor.getSql(statement, 1754281456303L, "343", sh);
        
        // 验证格式
        assertTrue(result.startsWith("【TS】:1754281456303,【Time】:343,【SQL】:"));
        assertTrue(result.contains("SELECT * FROM user WHERE id = 1")); // 多余空格应该被清理
    }
}
