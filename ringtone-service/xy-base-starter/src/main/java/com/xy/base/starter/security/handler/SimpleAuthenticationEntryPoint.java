package com.xy.base.starter.security.handler;

import com.xy.base.core.enums.ExceptionResultEnum;
import com.xy.base.starter.security.util.SecurityUtils;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2020/11/18
 */
public class SimpleAuthenticationEntryPoint implements AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) throws IOException {

        SecurityUtils.denyRequest(request, response, ExceptionResultEnum.NOT_LOGIN);
    }
}
