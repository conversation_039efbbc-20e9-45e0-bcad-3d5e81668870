package com.xy.base.starter.security.pojo;

import com.xy.base.starter.security.entity.UaaUserDetails;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/10/17
 */
@EqualsAndHashCode(callSuper = true)
@Data
public abstract class AbstractAdminUserDetails extends UaaUserDetails {

    private List<Role> roles;

    @Data
    public static class Role {

        /**
         * id
         */
        private Integer id;

        /**
         * 角色名称
         */
        private String name;
    }
}
