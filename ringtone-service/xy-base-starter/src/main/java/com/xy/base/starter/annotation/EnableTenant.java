package com.xy.base.starter.annotation;

import com.xy.base.starter.config.selector.TenantSelector;
import org.springframework.context.annotation.Import;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 是否启用mybatis-plus的多租户插件
 *
 * <AUTHOR>
 * @since 2022/8/29.
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Import(TenantSelector.class)
public @interface EnableTenant {

    /**
     * 不需要自动添加租户ID查询的表名称
     */
    String[] excludes() default {};
}
