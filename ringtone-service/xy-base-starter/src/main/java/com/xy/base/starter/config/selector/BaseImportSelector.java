package com.xy.base.starter.config.selector;

import com.xy.base.core.util.SpringUtils;
import com.xy.base.starter.aop.LoggerAspect;
import com.xy.base.starter.aop.MybatisSqlLogInterceptor;
import com.xy.base.starter.config.DefaultWebConfig;
import com.xy.base.starter.config.JacksonDateFormatConfig;
import com.xy.base.starter.config.MybatisPlusConfig;
import com.xy.base.starter.handler.ResponseResultHandler;
import com.xy.base.starter.redis.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.ImportSelector;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.lang.NonNull;

/**
 * 关键类，用于自动导入需要装配的类
 *
 * <AUTHOR>
 * @since 2020/5/29
 */
@Slf4j
public class BaseImportSelector implements ImportSelector {

    @NonNull
    @Override
    public String[] selectImports(@NonNull AnnotationMetadata annotationMetadata) {
        log.info("BaseImportSelector 初始化");
        return new String[]{
                ResponseResultHandler.class.getName(),
                DefaultWebConfig.class.getName(),
                MybatisPlusConfig.class.getName(),
                LoggerAspect.class.getName(),
                JacksonDateFormatConfig.class.getName(),
                MybatisSqlLogInterceptor.class.getName(),
                SpringUtils.class.getName(),
                RedisUtils.class.getName()
        };
    }
}
