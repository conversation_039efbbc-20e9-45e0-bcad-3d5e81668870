package com.xy.base.starter.redis;

import com.xy.base.core.exception.AppException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2022/9/15.
 */
public class RedisLock implements AutoCloseable {

    private static final String LOCK_PREFIX = "lock:";

    private final RedisTemplate<String, String> redisTemplate;
    private final String key;
    private final String errorMsg;
    private String value;

    public RedisLock(RedisTemplate<String, String> redisTemplate, String key, String errorMsg) {
        this.redisTemplate = redisTemplate;
        this.key = LOCK_PREFIX + key;
        this.errorMsg = errorMsg;
        this.lock();
    }

    private void lock() {
        int i = 5;
        String uuid = UUID.randomUUID().toString();
        // 尝试5次获取锁，如果没有获取到直接报错
        try {
            while (i > 0) {
                Boolean locked = redisTemplate.opsForValue().setIfAbsent(key, uuid, 30, TimeUnit.SECONDS);
                if (Boolean.TRUE.equals(locked)) {
                    this.value = uuid;
                    return;
                }
                i--;
                Thread.sleep(1000);
            }

        } catch (InterruptedException ignored) {
        }
        throw new AppException(errorMsg);
    }

    public String info() {
        return String.format("key:%s,value:%s", this.key, this.value);
    }


    @Override
    public void close() {
        if (StringUtils.hasText(value)) {
            String uuid = redisTemplate.opsForValue().get(key);
            if (StringUtils.hasText(uuid)) {
                AppException.tnt(!value.equals(uuid), "无权操作");
                redisTemplate.delete(key);
            }
        }
    }
}
