package com.xy.base.starter.aop;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.ResultHandler;

import java.sql.SQLException;
import java.sql.Statement;

/**
 * Mybatis 拦截器，装配SQL方便调试（beta阶段，遇到批量插入之类的sql可能会有异常）
 * 目前项目都是走StatementHandler，所以只拦截这个地方
 *
 * <AUTHOR>
 * @since 2021/11/15.
 */

@Intercepts({
//        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
//        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
        @Signature(type = StatementHandler.class, method = "query", args = {Statement.class, ResultHandler.class}),
        @Signature(type = StatementHandler.class, method = "update", args = {Statement.class})
})
@Slf4j
public class MybatisSqlLogInterceptor implements Interceptor {

    private static final String DB_MYSQL = "MYSQL";
    private static final String DB_POSTGRE = "POSTGRESQL";

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object[] args = invocation.getArgs();
        StatementHandler sh = (StatementHandler) invocation.getTarget();
        Statement statement = (Statement) args[0];
        long start = System.currentTimeMillis();

        String time = "fail";
        Object returnValue;

        try {
            returnValue = invocation.proceed();
            time = (System.currentTimeMillis() - start) + "";

        } finally {
            try {
                String sql = getSql(statement, start, time, sh);
                if (LoggerAspect.getLogger() == null) {
                    log.info(sql);
                } else {
                    LoggerAspect.getLogger().addSql(sql);
                }
            } catch (Exception e) {
                if (LoggerAspect.getLogger() == null) {
                    log.error("Mybatis拦截器解析SQL日志异常，但不影响执行", e);
                } else {
                    LoggerAspect.getLogger().addException("Mybatis拦截器解析SQL日志异常，但不影响执行", e);
                }
            }
        }

        return returnValue;
    }

    public static String getSql(Statement statement, long start, String time, StatementHandler sh) throws SQLException {

        String db = statement.getConnection().getMetaData().getDatabaseProductName().toUpperCase();

        // Mysql输出类似：HikariProxyPreparedStatement@1321445577 wrapping com.mysql.cj.jdbc.ClientPreparedStatement: SELECT COUNT(*) AS total FROM shop_product_general WHERE (status = 1 AND (virtual_pro = 0 OR (virtual_pro = 1 AND activity_start_time >= '2021-11-18 17:24:47.937' AND activity_end_time < '2021-11-18 17:24:47.938')))
        // 所以需要截掉前面的内容，不过前面的内容不知道是不是固定的
        // 外部有异常拦截，所以暂时考虑出错的情况
        String sql;
        switch (db) {
            case DB_MYSQL:
                sql = statement.toString();
                sql = sql.substring(sql.indexOf(":") + 1);
                break;
            case DB_POSTGRE:
                sql = statement.toString();
                break;
            default:
                sql = sh.getBoundSql().getSql();
                break;
        }

        sql = sql.replaceAll("[\\s]+", " ").trim();
        return StrUtil.format("【TS】:{},【Time】:{},【SQL】:{}", start, time, sql);
    }
}
