package com.xy.base.starter.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 默认的Webconfig，允许跨域，要覆盖直接继承DefaultWebConfig而不要实现WebMvcConfigurer
 *
 * <AUTHOR>
 * @since 2019-11-22
 */
@Configuration
public class DefaultWebConfig implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {

        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "DELETE", "PUT")
                .allowCredentials(true).maxAge(3600);

//        registry.addMapping("/**")
//                .allowedOrigins("*")
//                .allowedHeaders("*")
//                .allowedMethods("GET", "POST", "DELETE", "PUT")
//                .allowCredentials(true).maxAge(3600);
    }

    /**
     * 枚举类的转换器工厂 addConverterFactory
     */
//    @Override
//    public void addFormatters(FormatterRegistry registry) {
//        registry.addConverterFactory(new ValueToEnumConverterFactory<Integer>());
//        registry.addConverterFactory(new ValueToEnumConverterFactory<String>());
//    }

}
