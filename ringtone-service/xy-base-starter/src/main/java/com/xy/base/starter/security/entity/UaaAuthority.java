package com.xy.base.starter.security.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.security.core.GrantedAuthority;

/**
 * <AUTHOR>
 * @since 2020/11/17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UaaAuthority implements GrantedAuthority {

    private Integer id;
    private String authority;

    /**
     * 上级id，0表示根目录
     */
    private Integer parentId;


    /**
     * 功能名称
     */
    private String name;


    /**
     * 类型：1路由，2功能，下列属性请从前缀推测用途
     */
    private Integer type;

    /**
     * 如果是路由，记录页面值
     */
    private String routePath;


    /**
     * routeFile
     */
    private String routeFile;


    /**
     * 路由名称，通常为英文
     */
    private String routeName;


    /**
     * 是否显示
     */
    private Boolean menuDisplay;


    /**
     * 图标名称
     */
    private String menuIcon;

    /**
     * 是否缓存
     */
    private Boolean keepAlive;

    @Override
    public String getAuthority() {
        return authority;
    }
}
