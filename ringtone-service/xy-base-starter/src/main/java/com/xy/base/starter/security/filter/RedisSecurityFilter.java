package com.xy.base.starter.security.filter;


import com.xy.base.core.constant.CommonConsts;
import com.xy.base.core.enums.UaaTypeEnum;
import com.xy.base.starter.redis.RedisUtils;
import com.xy.base.starter.security.entity.UaaUserDetails;
import com.xy.base.starter.security.pojo.AdminUserDetails;
import com.xy.base.starter.security.pojo.UserUserDetails;
import com.xy.base.starter.security.util.SecurityUtils;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2020/7/27
 */
public class RedisSecurityFilter extends OncePerRequestFilter {


    @Override
    protected void doFilterInternal(
            HttpServletRequest request,
            @NonNull HttpServletResponse response,
            @NonNull FilterChain filterChain) throws ServletException, IOException {

        String token = request.getHeader(CommonConsts.HTTP_AUTH_HEADER_NAME);
        String platform = request.getHeader(CommonConsts.PLATFORM);

        if (SecurityUtils.checkToken(token, platform)) {
            UaaUserDetails userDetails;

            if (token.equals(CommonConsts.INTERNAL_TOKEN)) {
                userDetails = SecurityUtils.buildInternalUser(token);
            } else {
                String type = getTokenType(token);
                // 这里userDetails并不一定是UaaUserDetails对象，可能是其子类
                userDetails = RedisUtils.getForEntity(token, securityType(type));
            }
            if (userDetails != null) {
                // 用户每次正常操作，将redis生存时间提高到4小时后
                if (!token.equals(CommonConsts.INTERNAL_TOKEN)) {
                    RedisUtils.expire(token, CommonConsts.EX_TIME);
                }

                UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                        userDetails, null, userDetails.getAuthorities());

                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authentication);
            }
        }

        filterChain.doFilter(request, response);
    }

    private String getTokenType(String token) {
        if (StringUtils.hasText(token)) {
            return token.split(":")[2];
        } else {
            return null;
        }
    }

    private Class<? extends UaaUserDetails> securityType(String type) {
        if (UaaTypeEnum.ADMIN.getValue().equals(type)) {
            return AdminUserDetails.class;
        } else if (UaaTypeEnum.USER.getValue().equals(type)) {
            return UserUserDetails.class;
        }
        return UaaUserDetails.class;
    }

}
