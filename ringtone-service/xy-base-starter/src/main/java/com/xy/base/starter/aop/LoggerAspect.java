package com.xy.base.starter.aop;

import cn.hutool.core.util.IdUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import cn.hutool.extra.servlet.ServletUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xy.base.core.annotation.DisableLoggerRequest;
import com.xy.base.core.annotation.DisableLoggerResponse;
import com.xy.base.core.dto.LoggerInfo;
import com.xy.base.core.util.DbUtil;
import com.xy.base.core.util.IdUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.time.LocalDateTime;
import java.util.HashMap;


/**
 * 接口日志切面
 *
 * <AUTHOR>
 * @since 2021/2/20
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class LoggerAspect {

    private final ObjectMapper mapper;
    private static final ThreadLocal<LoggerInfo> threadLog = new ThreadLocal<>();

    public static ThreadLocal<LoggerInfo> getThreadLog() {
        return threadLog;
    }

    public static LoggerInfo getLogger() {
        return threadLog.get();
    }

    /**
     * 将所有RestController的请求切入
     * 以自定义 @LogPrint 注解为切点
     */
    @Pointcut("@within(org.springframework.web.bind.annotation.RestController)")
    public void logPrint() {
    }

    /**
     * 环绕
     *
     * @param proceedingJoinPoint proceedingJoinPoint
     * @return Object
     */
    @Around("logPrint()")
    public Object doAround(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {

        long start = System.currentTimeMillis();

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert attributes != null;
        HttpServletRequest request = attributes.getRequest();
        LoggerInfo info = getInfoFromRequest(proceedingJoinPoint, request);
        threadLog.set(info);

        info.setStartTime(start);
        Object result = proceedingJoinPoint.proceed();
        if (info.isLogRes()) {
            info.setResult(mapper.writeValueAsString(result));
        }
        info.setEndTime(System.currentTimeMillis());

        outputLog(info);

        return result;
    }

    @AfterThrowing(pointcut = "logPrint()", throwing = "e")
    public void doAfterThrow(JoinPoint joinPoint, RuntimeException e) {

        LoggerInfo info = threadLog.get();
        info.getExceptions().add(e);
        info.setEndTime(System.currentTimeMillis());

        outputLog(info);
    }


    public void outputLog(LoggerInfo info) {
        log.info(info.toString());

        // 将日志记录进数据库
        try {
            String url = StringUtils.hasText(info.getUrl()) ? info.getUrl().replace("http://", "").replace("https://", "") : "";

            /*
            Entity entity = Entity.create("log_sys")
                    .set("id", IdUtils.nextId())
                    .set("url", url.substring(url.indexOf("/")))
                    .set("ip", info.getIp())
                    .set("method", info.getMethod())
                    .set("position", info.getId())
                    .set("request", info.getRequestParams())
                    .set("response", info.getResult())
                    .set("timing", info.getEndTime() - info.getStartTime())
                    .set("create_time", LocalDateTime.now());
            if (info.getExceptions() != null && info.getExceptions().size() > 0) {
                RuntimeException exception = info.getExceptions().get(0);
                StringWriter sw = new StringWriter();
                exception.printStackTrace(new PrintWriter(sw, true));
                String st = sw.toString();
                entity.set("exception", true)
                        .set("exception_name", exception.getClass().getName())
                        .set("exception_trace", st.length() > 1000 ? st.substring(0, 1000) : st);
            }
            Db.use().insert(entity);
            */
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 日志打印完毕后，清理相关内容，避免内存溢出
        threadLog.remove();
    }

    private String getParams(JoinPoint joinPoint) {
        StringBuilder sb = new StringBuilder();
        if (joinPoint.getArgs() != null && joinPoint.getArgs().length > 0) {

            for (Object arg : joinPoint.getArgs()) {
                if ((arg instanceof HttpServletResponse) || (arg instanceof HttpServletRequest)
                        || (arg instanceof MultipartFile) || (arg instanceof MultipartFile[])) {
                    sb.append(arg.getClass()).append(",");
                }
                try {
                    sb.append(mapper.writeValueAsString(arg));
                } catch (Exception e1) {
                    log.error(e1.getMessage());
                }
            }
        }
        return sb.toString();
    }

    private String getHeader(HttpServletRequest request) {

        HashMap<String, String> hashMap = new HashMap<>(6);
        hashMap.put("tenantToken", request.getHeader("tenantToken"));
        hashMap.put("Authorization", request.getHeader("Authorization"));
        hashMap.put("Platform", request.getHeader("Platform"));
        hashMap.put("Version", request.getHeader("Version"));

        try {
            return mapper.writeValueAsString(hashMap);
        } catch (JsonProcessingException e) {
            log.error(e.getMessage());
            return null;
        }
    }

    private LoggerInfo getInfoFromRequest(JoinPoint joinPoint, HttpServletRequest request) {

        LoggerInfo info = new LoggerInfo();

        DisableLoggerRequest reqClassAno = joinPoint.getTarget().getClass().getAnnotation(DisableLoggerRequest.class);
        MethodSignature ms = (MethodSignature) joinPoint.getSignature();
        DisableLoggerRequest reqMethodAno = ms.getMethod().getAnnotation(DisableLoggerRequest.class);

        if (reqClassAno != null || reqMethodAno != null) {
            info.setLogReq(false);
            info.setRequestParams("show disabled");
        }

        DisableLoggerResponse resClassAno = joinPoint.getTarget().getClass().getAnnotation(DisableLoggerResponse.class);
        MethodSignature ms2 = (MethodSignature) joinPoint.getSignature();
        DisableLoggerRequest resMethodAno = ms2.getMethod().getAnnotation(DisableLoggerRequest.class);

        if (resClassAno != null || resMethodAno != null) {
            info.setLogRes(false);
            info.setResult("show disabled");
        }


        info.setUrl(request.getRequestURL().toString());
        info.setHeader(getHeader(request));
        info.setMethod(request.getMethod());
        info.setId(joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName());
        info.setIp(ServletUtil.getClientIP(request));
        if (info.isLogReq()) {
            info.setRequestParams(getParams(joinPoint));
        }

        return info;
    }
}
