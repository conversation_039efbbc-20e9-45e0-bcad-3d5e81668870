package com.xy.base.starter.security.handler;

import com.xy.base.core.enums.ExceptionResultEnum;
import com.xy.base.starter.security.util.SecurityUtils;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2020/11/18
 */
public class SimpleAccessDeniedHandler implements AccessDeniedHandler {

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException e) throws IOException {
       /*
        只能拦截.antMatchers("/hello0").hasRole("AAA")这种配置抛出的异常，
        @PreAuthorize抛出的异常并不会被拦截，需要在全局中处理
        */
        SecurityUtils.denyRequest(request, response, ExceptionResultEnum.ACCESS_DENIED);
    }
}
