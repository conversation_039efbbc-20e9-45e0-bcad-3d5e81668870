package com.xy.base.starter.security.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/11/17
 */
@Getter
@Setter
public class UaaUserDetails implements UserDetails {

    public UaaUserDetails() {
    }

    public UaaUserDetails(Integer id, String username) {
        this.id = id;
        this.username = username;
    }

    /**
     * 应用审核状态，true表示在审核中，false表示正常运行状态
     */
    private boolean audit = false;

    private Integer orgId;

    private String orgPath;

    private String openid;
    /**
     * 当前登录用户对应的ID(可能是用户ID，也可能是管理员或者租户管理员ID)
     */
    private Integer id;

    /**
     * 用户令牌
     */
    private String token;

    /**
     * 用户登录名，一般为电话号码
     */
    private String username;

    /**
     * 用户登录名，一般为电话号码
     */
    private String phone;

    /**
     * 账号状态：0未审核（但可以登录，登陆后进行企业认证），1正常，-1未通过，2已经认证企业，等待后台审核，3资质异常
     */
    private Integer status;

    /**
     * 用户类型
     *
     * @see com.xy.base.core.enums.UaaTypeEnum
     */
    private String type;

    /**
     * 真实姓名,如果是用户，表示为单位名称
     */
    private String realName;

    /**
     * 用户权限
     */
    private List<UaaAuthority> authorities;

    @Override
    public List<UaaAuthority> getAuthorities() {
        return authorities;
    }

    @Override
    public String getPassword() {
        return null;
    }

    @Override
    public String getUsername() {
        return username;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

}
