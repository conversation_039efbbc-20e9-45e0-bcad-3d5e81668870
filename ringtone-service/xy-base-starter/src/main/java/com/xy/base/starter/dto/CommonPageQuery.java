package com.xy.base.starter.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2020/11/30
 * 通用分页查询参数
 */
@Data
public class CommonPageQuery implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 当前页码
     */
    private int page = 1;

    /**
     * 每页数量
     */
    private int size = 10;

    /**
     * 查询关键字
     */
    private String keyword;
    /**
     * 查询关键字
     */
    private String code;

    /**
     * 查询类型
     */
    private Integer type;

    private LocalDateTime startTime;
    private LocalDateTime endTime;

    public <T> Page<T> buildPage() {
        return new Page<>(page, size);
    }
}
