package com.xy.base.starter.config;

import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.xy.base.core.enhancer.JacksonMaker;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2021/6/17.
 */
@MappedTypes({Object.class})
@MappedJdbcTypes(JdbcType.VARCHAR)
public class LocalJacksonTypeHandler extends AbstractJsonTypeHandler<Object> {

    private final Class<?> type;

    public LocalJacksonTypeHandler(Class<?> type) {

        Assert.notNull(type, "Type argument cannot be null");
        this.type = type;
    }

    @Override
    protected Object parse(String json) {
        try {
            return JacksonMaker.uniMapper().readValue(json, this.type);
        } catch (IOException var3) {
            throw new RuntimeException(var3);
        }
    }

    @Override
    protected String toJson(Object obj) {
        try {
            return JacksonMaker.uniMapper().writeValueAsString(obj);
        } catch (JsonProcessingException var3) {
            throw new RuntimeException(var3);
        }
    }
}
