package com.xy.base.starter.config.selector;

import com.xy.base.starter.annotation.EnableTenant;
import com.xy.base.starter.config.DefaultBehavior;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.ImportSelector;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.lang.NonNull;

import java.util.Arrays;

/**
 * 多租户实现类
 *
 * <AUTHOR>
 * @since 2022/8/29
 */
@Slf4j
public class TenantSelector implements ImportSelector {

    public TenantSelector() {
        log.info("TenantSelector 初始化");
        DefaultBehavior.useTenant = true;
    }

    @NonNull
    @Override
    public String[] selectImports(@NonNull AnnotationMetadata annotationMetadata) {
        DefaultBehavior.sortedExcludes = annotationMetadata.getAnnotations().get(EnableTenant.class).getStringArray("excludes");
        Arrays.sort(DefaultBehavior.sortedExcludes);

        return new String[]{};
    }
}
