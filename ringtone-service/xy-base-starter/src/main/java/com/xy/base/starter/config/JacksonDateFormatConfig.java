package com.xy.base.starter.config;

import com.xy.base.core.enhancer.JacksonMaker;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.boot.jackson.JsonComponent;
import org.springframework.context.annotation.Bean;


/**
 * <AUTHOR>
 * @since 2021/5/31.
 */
@JsonComponent
public class JacksonDateFormatConfig {

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer() {
        return JacksonMaker::uniBuilder;
    }

}
