package com.xy.base.starter.security.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import cn.hutool.extra.servlet.ServletUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xy.base.core.dto.LoggerInfo;
import com.xy.base.core.constant.CommonConsts;
import com.xy.base.core.enums.ExceptionResultEnum;
import com.xy.base.core.enums.PlatformEnum;
import com.xy.base.core.enums.UaaTypeEnum;
import com.xy.base.core.response.Result;
import com.xy.base.starter.security.entity.UaaAuthority;
import com.xy.base.starter.security.entity.UaaUserDetails;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2020/8/11
 */
@Slf4j
public class SecurityUtils {

    public static final String SUPER_NAME = "admin";
    public static final String DEFAULT_PWD = "123456";
    public static final String TOKEN_PREFIX = "bearer";

    /**
     * 将用户id通过AES加密，必须为16位
     */
    private static final String AES_KEY = "SYKJAESAdminIDtk";

    /**
     * 随机生成Token
     */
    public static String buildToken(Integer id, UaaTypeEnum typeEnum) {

        return buildToken(getRequestPlatform(), id, typeEnum);
    }

    /**
     * 随机生成Token
     */
    public static String buildToken(PlatformEnum platform, Integer id, UaaTypeEnum typeEnum) {

        SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, AES_KEY.getBytes(StandardCharsets.UTF_8));
        // 加密为16进制表示
        String encryptHex = aes.encryptHex(id.toString());

        return StrUtil.format("{}:{}:{}:{}:{}", TOKEN_PREFIX, platform.getValue(), typeEnum.getValue(), encryptHex, System.currentTimeMillis());
    }

    /**
     * 随机生成Token
     */
    public static String buildToken(String prefix, String... strings) {
        long timestamp = System.currentTimeMillis();
        StringBuilder tokenMeta = new StringBuilder();
        for (String s : strings) {
            tokenMeta.append(s);
        }
        tokenMeta.append(timestamp);
        tokenMeta.append(Math.random());

        return prefix + ":" + DigestUtils.md5DigestAsHex(tokenMeta.toString().getBytes());
    }

    /**
     * 从token中解密id
     *
     * @param token    token字符串
     * @param original 是否为原始字符串
     * @return Integer
     */
    public static Integer decryptTokenId(String token, boolean original) {
        if (original) {
            token = token.split(":")[2];
        }

        SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, AES_KEY.getBytes(StandardCharsets.UTF_8));

        // 解密为16进制表示
        String decryptStr = aes.decryptStr(token, StandardCharsets.UTF_8);
        return Integer.parseInt(decryptStr);
    }

    /**
     * 获取当前登录用户
     * (请确保在登录成功调用，否则会出现异常)
     *
     * @return UaaUserDetails
     */
    public static <T extends UaaUserDetails> T current() {

        return (T) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    }

    /**
     * 判断是否登录
     *
     * @return boolean
     */
    public static boolean isLogin() {
        if (SecurityContextHolder.getContext().getAuthentication() == null) {
            return false;
        }
        return SecurityContextHolder.getContext().getAuthentication().getPrincipal() instanceof UaaUserDetails;
    }

    public static String getTenantId() {
        return "tc";
    }

    /**
     * 获取当前登录用户ID
     *
     * @return Integer
     */
    public static Integer getId() {

        return current().getId();
    }


    /**
     * 检查token是否正常
     *
     * @param token    token
     * @param platform platform
     * @return boolean
     */
    public static boolean checkToken(String token, String platform) {
        if (StringUtils.hasText(token)) {
            if (token.equals(CommonConsts.INTERNAL_TOKEN)) {
                return true;
            }
            PlatformEnum platformEnum = PlatformEnum.of(platform);
            if (platformEnum.getCode() < PlatformEnum.IOS.getCode()) {
                return token.startsWith(TOKEN_PREFIX + ":" + platform + ":");
            }
            return true;
        }
        return false;
    }


    public static boolean isYwy() {
        return current().getType().equals(UaaTypeEnum.YWY.getValue());
    }

    public static boolean isUser() {
        return current().getType().equals(UaaTypeEnum.USER.getValue());
    }

    public static boolean isAdmin() {
        return current().getType().equals(UaaTypeEnum.ADMIN.getValue());
    }

    public static boolean isTenant() {
        return current().getType().equals(UaaTypeEnum.TENANT.getValue());
    }

    public static boolean isIndustry() {
        return current().getType().equals(UaaTypeEnum.INDUSTRY.getValue());
    }

    /**
     * 获取request中的参数
     *
     * @return PlatformEnum
     */
    public static PlatformEnum getRequestPlatform() {
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        return PlatformEnum.of(request.getHeader(CommonConsts.PLATFORM));
    }

    /**
     * 获取request中的参数
     *
     * @return PlatformEnum
     */
    public static String getRequestVersion() {
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        return request.getHeader(CommonConsts.VERSION);
    }

    /**
     * 获取APP运行模式，0审核模式，屏蔽敏感信息。1正常模式，全功能开放
     *
     * @param version    微信审核版本号
     * @param appVersion app审核版本号
     * @return int
     */
    public static int getAppModel(String version, String appVersion) {
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        String reqVersion = request.getHeader(CommonConsts.VERSION);
        String reqPlatform = request.getHeader(CommonConsts.PLATFORM);

        String finalVersion = reqPlatform.equalsIgnoreCase(PlatformEnum.WX_MINI.getValue()) ? version : appVersion;

        return finalVersion.equals(reqVersion) ? 0 : 1;
    }

    /**
     * 是否为超管
     */
    public static boolean isSuper() {
        return SUPER_NAME.equals(current().getUsername());
    }


    public static boolean canAccessManagement(int source, int target) {
        return (source | target) == source;
    }

    public static UaaUserDetails buildInternalUser(String token) {
        UaaUserDetails userDetails = new UaaUserDetails();
        userDetails.setToken(token);
        userDetails.setId(0);
        userDetails.setUsername("internal");
        userDetails.setRealName("内置用户");
        List<UaaAuthority> authorities = new ArrayList<>();
        UaaAuthority authority = new UaaAuthority();
        authority.setId(1);
        authority.setAuthority("internal:internal");
        authorities.add(authority);
        userDetails.setAuthorities(authorities);
        return userDetails;
    }

    /**
     * 处理拒绝访问的情况
     */
    public static void denyRequest(HttpServletRequest request, HttpServletResponse response, ExceptionResultEnum error) throws IOException {
        // 由于拒绝访问未发生在controller中，日志切面无法获取，所以无法执行日志输出，需要手动打印(输出简单日志)
        LoggerInfo info = new LoggerInfo();

        info.setUrl(request.getRequestURL().toString());
        info.setMethod(request.getMethod());
        info.setHeader(request.getHeader("Authorization"));
        info.setIp(ServletUtil.getClientIP(request));
        info.setRequestParams(request.getQueryString());

        Result<?> result = new Result<>(error);
        response.setCharacterEncoding("utf-8");
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        ObjectMapper objectMapper = new ObjectMapper();
        String resultStr = objectMapper.writeValueAsString(result);
        info.setResult(resultStr);
        log.info(info.toString());

        PrintWriter printWriter = response.getWriter();
        printWriter.print(resultStr);
        printWriter.flush();
        printWriter.close();
    }
}
