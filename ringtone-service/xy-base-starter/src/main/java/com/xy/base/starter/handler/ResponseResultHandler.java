package com.xy.base.starter.handler;

import cn.hutool.core.util.IdUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xy.base.core.annotation.DirectOutput;
import com.xy.base.core.constant.ResultCodeConsts;
import com.xy.base.core.enums.ExceptionResultEnum;
import com.xy.base.core.exception.AppException;
import com.xy.base.core.response.PageData;
import com.xy.base.core.response.Result;
import com.xy.base.core.util.DbUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.lang.NonNull;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * <AUTHOR>
 * @since 2019-11-22
 * Controller增强，将返回的内容进行固定封装，并且拦截全局异常
 */
@Slf4j
@RestControllerAdvice
public class ResponseResultHandler implements ResponseBodyAdvice<Object> {

    @ExceptionHandler(AppException.class)
    public Result<?> appExceptionHandler(AppException e) {
        // 如果AppException中含有errorCode字段，则使用它
        if (e.getErrorCode() != null) {
            return Result.error(e.getCode(), e.getErrorCode(), e.getMsg(), e.getData());
        }
        return Result.error(e.getCode(), e.getMsg(), e.getData());
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<?> methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) {

        ObjectError objectError = e.getBindingResult().getAllErrors().get(0);
        return Result.error(ResultCodeConsts.ERROR_PARAMS, objectError.getDefaultMessage());
    }

    @ExceptionHandler(AccessDeniedException.class)
    public Result<String> accessDeniedException(Exception e) {
        log.info("捕获了一个拒绝访问的异常" + e.getMessage());
        return new Result<>(ExceptionResultEnum.ACCESS_DENIED);
    }

    @ExceptionHandler(DataAccessException.class)
    public Result<?> databaseExceptionHandler(DataAccessException e) {
        if (e instanceof DuplicateKeyException) {
            return new Result<>(ExceptionResultEnum.DATA_DUPLICATE);
        } else {
            return new Result<>(ExceptionResultEnum.DATA_ACCESS_ERROR);
        }
    }

    @ExceptionHandler(Exception.class)
    public Result<String> exceptionHandler(Exception e) {

        // 捕获一个未知异常的时候，记录进数据库
        try {
            Entity entity = Entity.create("log_sys")
                    .set("id", IdUtil.getSnowflake().nextId())
                    .set("exception_message", e.getMessage())
                    .set("exception_name", e.getClass().getName());
            StringWriter sw = new StringWriter();
            e.printStackTrace(new PrintWriter(sw, true));
            String st = sw.toString();
            entity.set("exception_trace", st.length() > 1000 ? st.substring(0, 1000) : st);
            Db.use().insert(entity);
        } catch (Exception exception) {
            exception.printStackTrace();
        }

        String exceptionType = e.getClass().getName();

        // 由于没有引入feign模块，无法直接捕获HystrixRuntimeException等feign异常
        // 通过名称判断具体类型
        if (exceptionType.endsWith("HystrixRuntimeException")) {
            String service = e.getMessage().contains("#") ? e.getMessage().substring(0, e.getMessage().indexOf("#")) : "";
            return new Result<>(ResultCodeConsts.ERROR_FEIGN, service + "服务调用失败", e.getMessage());
        } else if (exceptionType.startsWith("feign.")) {
            if (exceptionType.endsWith(".DecodeException")) {
                return new Result<>(ResultCodeConsts.ERROR_FEIGN, "feign处理结果异常", e.getMessage());
            } else {
                return new Result<>(ResultCodeConsts.ERROR_FEIGN, "feign访问异常", e.getMessage());
            }
        }

        return new Result<>(ResultCodeConsts.ERROR_UNKNOWN, "网络异常", e.getMessage());
    }

    @Override
    public boolean supports(MethodParameter methodParameter, @NonNull Class<? extends HttpMessageConverter<?>> aClass) {

        // 如果接口返回的类型本身就是Result或者注解了直接输出，返回false
        return (!methodParameter.getParameterType().equals(Result.class))
                && (methodParameter.getMethodAnnotation(DirectOutput.class) == null);

    }

    @Override
    public Object beforeBodyWrite(Object body,
                                  @NonNull MethodParameter methodParameter,
                                  @NonNull MediaType mediaType,
                                  @NonNull Class<? extends HttpMessageConverter<?>> aClass,
                                  @NonNull ServerHttpRequest serverHttpRequest,
                                  @NonNull ServerHttpResponse serverHttpResponse) {

        // 如果body已经被包装了，就不进行包装
        // 由于序列化器的原因，禁止直接返回String类型。
        // 如需直接返回String，可用Result直接包装后返回
        // return Result.success("string")

        if (body instanceof Result) {
            return body;
        } else if (body instanceof IPage) {
            IPage<?> page = (IPage<?>) body;
            PageData<?> pageData = new PageData<>(page.getCurrent(), page.getTotal(), page.getSize(), page.getRecords());
            return Result.success(pageData);
        } else {
            return Result.success(body);
        }
    }
}
