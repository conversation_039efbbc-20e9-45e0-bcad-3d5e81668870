<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.xy</groupId>
    <artifactId>xy-services</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0</version>
    <modules>
        <module>xy-uaa</module>
        <module>xy-base-core</module>
        <module>xy-base-starter</module>
        <module>xy-base-feign</module>
        <module>xy-base-mq</module>
        <module>xy-resource</module>
        <module>xy-scanner</module>
        <module>xy-cdc-es</module>
        <module>xy-mq-consumer</module>
        <module>xy-gateway</module>
        <module>xy-admin</module>
        <module>xy-admin-api</module>
        <module>xy-lib-migu</module>
        <module>xy-promote</module>
    </modules>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.6</version>
        <relativePath/>
    </parent>

    <properties>

        <!-- Environment Settings -->
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <!-- Spring Settings -->
        <spring-cloud.version>2021.0.4</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.1.0</spring-cloud-alibaba.version>


        <!-- Other Settings -->
        <base.version>1.0.0</base.version>
        <maven.test.skip>true</maven.test.skip>
        <hutool.version>5.8.20</hutool.version>
        <poi.version>4.1.2</poi.version>
        <mybatis-plus.version>*******</mybatis-plus.version>
        <smart-doc.verison>2.7.1</smart-doc.verison>
        <wxjava.version>4.5.0</wxjava.version>
        <guava.version>30.1-jre</guava.version>
        <mq.verison>4.9.4</mq.verison>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>

            <plugin>
                <groupId>com.github.shalousun</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <version>${smart-doc.verison}</version>
                <configuration>
                    <configFile>./src/main/resources/smart-doc.json</configFile>
                    <projectName>xy</projectName>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>html</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>

</project>