package com.xy.base.mq.selector;

import com.xy.base.mq.config.MqProducerConfig;
import org.springframework.context.annotation.ImportSelector;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.lang.NonNull;

/**
 * <AUTHOR>
 * @since 2022/10/26
 */
public class ProducerSelector implements ImportSelector {


    @NonNull
    @Override
    public String[] selectImports(@NonNull AnnotationMetadata annotationMetadata) {

        return new String[]{
                MqProducerConfig.class.getName()
        };
    }
}