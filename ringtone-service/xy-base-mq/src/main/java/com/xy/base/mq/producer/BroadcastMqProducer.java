package com.xy.base.mq.producer;

import com.xy.base.mq.constant.MQTopic;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.exception.RemotingException;

/**
 * 快速发送消息的消费者
 *
 * <AUTHOR>
 * @since 2022/10/27
 */
public class BroadcastMqProducer extends DefaultMQProducer {

    public BroadcastMqProducer(String producerGroup) {
        super(null, producerGroup, null);
    }

    /**
     * 发送消息
     *
     * @param topic topic
     * @param tag   tag
     * @param body  消息体
     * @return SendResult
     */
    public SendResult send(String topic, String tag, String body) throws MQ<PERSON>rokerException, RemotingException, InterruptedException, MQClientException {

        Message sendMsg = new Message(topic, tag, body.getBytes());
        return send(sendMsg);
    }

    /**
     * 通知trade服务更新商户信息（商户信息变动时需要调用）
     *
     * @return SendResult
     */
    public SendResult sendTenantModify() throws MQBrokerException, RemotingException, InterruptedException, MQClientException {

        return send(MQTopic.HOT, MQTopic.TAG_HOT_TENANT, "1");
    }

}
