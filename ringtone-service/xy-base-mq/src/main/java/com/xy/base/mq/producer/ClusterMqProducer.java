package com.xy.base.mq.producer;

import com.xy.base.core.enhancer.JacksonMaker;
import com.xy.base.mq.constant.MQTopic;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.exception.RemotingException;

/**
 * 快速发送消息的消费者
 *
 * <AUTHOR>
 * @since 2022/10/27
 */
public class ClusterMqProducer extends DefaultMQProducer {

    public ClusterMqProducer(String producerGroup) {
        super(null, producerGroup, null);
    }

    /**
     * 发送消息
     *
     * @param topic topic
     * @param tag   tag
     * @param body  消息体
     * @return SendResult
     */
    public SendResult send(String topic, String tag, String body) throws MQ<PERSON>rokerException, RemotingException, InterruptedException, MQClientException {

        Message sendMsg = new Message(topic, tag, body.getBytes());
        return this.send(sendMsg, 1000);
    }

    /**
     * 发送消息(直接发送对象)
     *
     * @param topic topic
     * @param tag   tag
     * @param obj   消息体
     * @return SendResult
     */
    public SendResult send(String topic, String tag, Object obj) throws MQBrokerException, RemotingException, InterruptedException, MQClientException {

        String body = JacksonMaker.writeValueAsString(obj);
        return send(topic, tag, body);
    }

    /**
     * 发送订单通知
     *
     * @return SendResult
     */
    public SendResult sendOrderMsg(String tag, String body) throws MQBrokerException, RemotingException, InterruptedException, MQClientException {

        return send(MQTopic.ORDER, tag, body);
    }

}
