package com.xy.base.mq.config;

import com.xy.base.mq.constant.MQConsts;
import com.xy.base.mq.producer.BroadcastMqProducer;
import com.xy.base.mq.producer.ClusterMqProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.exception.MQClientException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

/**
 * <AUTHOR>
 * @since 2022/10/26
 */
@Slf4j
public class MqProducerConfig {

    @Value("${mq.address:************:9876}")
    private String address;


    // 消息最大长度 默认 1024 * 4 (4M)
    private static final int MAX_MESSAGE_SIZE = 1024;

    // 发送消息超时时间，默认 3000
    private static final int SEND_MSG_TIME_OUT = 3000;

    // 发送消息失败重试次数，默认2
    private static final int RETRY_TIMES_WHEN_SEND_FAILED = 2;

    /**
     * mq 生产者配置
     */
    @Bean
    public ClusterMqProducer clusterMqProducer() throws MQClientException {
        log.info("MQ CLUSTER生产者正在创建---------------------------------------");
        ClusterMqProducer producer = new ClusterMqProducer(MQConsts.CLUSTER_GROUP);
        producer.setNamesrvAddr(address);
        producer.setVipChannelEnabled(false);
        producer.setMaxMessageSize(MAX_MESSAGE_SIZE);
        producer.setSendMsgTimeout(SEND_MSG_TIME_OUT);
        producer.setRetryTimesWhenSendAsyncFailed(RETRY_TIMES_WHEN_SEND_FAILED);
        producer.start();
        log.info("MQ CLUSTER生产者创建成功----------------------------------");
        return producer;
    }

    @Bean
    public BroadcastMqProducer broadcastMqProducer() throws MQClientException {
        log.info("MQ BROADCAST生产者正在创建---------------------------------------");
        BroadcastMqProducer producer = new BroadcastMqProducer(MQConsts.BROADCAST_GROUP);
        producer.setNamesrvAddr(address);
        producer.setVipChannelEnabled(false);
        producer.setMaxMessageSize(MAX_MESSAGE_SIZE);
        producer.setSendMsgTimeout(SEND_MSG_TIME_OUT);
        producer.setRetryTimesWhenSendAsyncFailed(RETRY_TIMES_WHEN_SEND_FAILED);
        producer.start();
        log.info("MQ BROADCAST生产者创建成功----------------------------------");
        return producer;
    }
}
