package com.xy.base.mq.constant;

/**
 * 系统中有的topic，禁止通过字符串私自创建
 *
 * <AUTHOR>
 * @since 2022/10/27
 */
public class MQTopic {

    public static final String WECHAT = "WechatTopic";

    public static final String SYS = "SysTopic";

    public static final String TEST = "TestTopic";

    public static final String CANAL = "CanalTopic";

    public static final String ORDER = "OrderTopic";

    /**
     * 热数据(通常以广播形式订阅)
     */
    public static final String HOT = "HotTopic";


    // 以下为标签列表
    public static final String TAG_TEST_TEST = "testTag";
    public static final String TAG_SEND_SEND = "sendTag";
    public static final String TAG_CHECK_CHECK = "checkTag";

    /**
     * 更新租户信息消息
     */
    public static final String TAG_HOT_TENANT = "tenantTag";

    public static final String TAG_ORDER_PAYED = "payedTag";

    public static final String TAG_ORDER_CREATE = "createTag";
    public static final String TAG_ORDER_YWY_CREATE = "ywyCreate";
    public static final String TAG_ORDER_YWY_FINISH = "ywyFinish";


}
